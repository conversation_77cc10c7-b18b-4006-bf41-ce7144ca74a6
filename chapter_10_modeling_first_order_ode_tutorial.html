<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 10: Modeling with First-Order ODEs - ODE Tutorial</title>
    
    <!-- External Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>

    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                packages: {'[+]': ['ams', 'newcommand', 'configmacros']}
            },
            svg: {
                fontCache: 'global'
            }
        };

        // Chart.js loading verification
        function checkChartJSLoaded() {
            if (typeof Chart === 'undefined') {
                console.error('Chart.js failed to load from CDN');
                // Show error message to user
                const errorDiv = document.createElement('div');
                errorDiv.className = 'fixed top-4 left-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
                errorDiv.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <div>
                            <strong>Chart Library Loading Error:</strong><br>
                            The Chart.js library failed to load. Visualizations will not display correctly.
                            <button onclick="location.reload()" class="ml-2 text-blue-600 underline">Refresh page</button>
                        </div>
                        <button onclick="this.parentElement.parentElement.remove()" class="ml-auto font-bold">&times;</button>
                    </div>
                `;
                document.body.appendChild(errorDiv);
                return false;
            }
            console.log('Chart.js loaded successfully, version:', Chart.version);
            return true;
        }

        // Check when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkChartJSLoaded, 100);
        });
    </script>

    <style>
        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid #007acc;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        
        .python-code { border-left-color: #3776ab; }
        .r-code { border-left-color: #276dc3; }
        
        .definition-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .theorem-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .example-box {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .application-box {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .nav-section {
            position: sticky;
            top: 0;
            background: white;
            z-index: 10;
            border-bottom: 2px solid #e5e7eb;
            padding: 1rem 0;
        }
        
        .chart-container {
            height: 400px;
            margin: 2rem 0;
            background: white;
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .chart-container canvas {
            max-width: 100%;
            height: 100% !important;
        }
        
        .large-chart {
            height: 600px;
            margin: 2rem 0;
            background: white;
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .large-chart canvas {
            max-width: 100%;
            height: 100% !important;
        }
        
        /* Ensure charts are visible */
        canvas {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
        
        /* Loading animation for charts */
        .chart-container:not(.loaded) {
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0, #f0f0f0);
            background-size: 400% 400%;
            animation: gradient-loading 2s ease-in-out infinite;
        }
        
        .chart-container:not(.loaded)::before {
            content: "Loading visualization...";
            color: #666;
            font-size: 14px;
        }
        
        @keyframes gradient-loading {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        @media print {
            .nav-section { position: static; }
            .chart-container, .large-chart { 
                height: 400px; 
                page-break-inside: avoid; 
            }
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-8">
        <div class="container mx-auto px-4">
            <h1 class="text-4xl font-bold mb-2">
                <i class="fas fa-flask mr-3"></i>Chapter 10: Modeling with First-Order ODEs
            </h1>
            <p class="text-xl opacity-90">Part 2: First-Order Differential Equations - Comprehensive Applications</p>
            <div class="mt-4 text-sm opacity-75">
                <span class="mr-4"><i class="fas fa-book mr-1"></i>Advanced Tutorial</span>
                <span class="mr-4"><i class="fas fa-code mr-1"></i>Python & R</span>
                <span><i class="fas fa-chart-line mr-1"></i>Real-World Applications</span>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="nav-section">
        <div class="container mx-auto px-4">
            <nav class="flex flex-wrap gap-4 text-sm">
                <a href="#prerequisites" class="text-blue-600 hover:text-blue-800 font-medium">Prerequisites</a>
                <a href="#intro" class="text-blue-600 hover:text-blue-800 font-medium">Introduction</a>
                <a href="#methodology" class="text-blue-600 hover:text-blue-800 font-medium">Modeling Methodology</a>
                <a href="#mixing" class="text-blue-600 hover:text-blue-800 font-medium">Mixing Problems</a>
                <a href="#population" class="text-blue-600 hover:text-blue-800 font-medium">Population Dynamics</a>
                <a href="#economics" class="text-blue-600 hover:text-blue-800 font-medium">Economic Models</a>
                <a href="#physics" class="text-blue-600 hover:text-blue-800 font-medium">Physics Applications</a>
                <a href="#pharmacology" class="text-blue-600 hover:text-blue-800 font-medium">Clinical Pharmacology</a>
                <a href="#engineering" class="text-blue-600 hover:text-blue-800 font-medium">Engineering Systems</a>
                <a href="#advanced" class="text-blue-600 hover:text-blue-800 font-medium">Advanced Analysis</a>
                <a href="#case-studies" class="text-blue-600 hover:text-blue-800 font-medium">Case Studies</a>
            </nav>
        </div>
    </div>

    <div class="container mx-auto px-4 py-8">

        <!-- Prerequisites -->
        <section id="prerequisites" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-graduation-cap text-indigo-600 mr-3"></i>Prerequisites and Mathematical Foundations
            </h2>

            <div class="definition-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-book mr-2"></i>Required Mathematical Background</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-3">Calculus Foundations</h4>
                        <ul class="list-disc list-inside space-y-2 text-sm">
                            <li><strong>Derivatives:</strong> Understanding $\frac{dy}{dx}$ as rate of change</li>
                            <li><strong>Integration:</strong> Antiderivatives and definite integrals</li>
                            <li><strong>Chain Rule:</strong> For composite functions</li>
                            <li><strong>Implicit Differentiation:</strong> For related rates</li>
                            <li><strong>Exponential/Logarithmic Functions:</strong> Properties and derivatives</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-3">Differential Equations Basics</h4>
                        <ul class="list-disc list-inside space-y-2 text-sm">
                            <li><strong>Order and Degree:</strong> Classification of ODEs</li>
                            <li><strong>Initial Value Problems:</strong> Solutions with conditions</li>
                            <li><strong>General vs Particular Solutions:</strong> Family of curves</li>
                            <li><strong>Existence and Uniqueness:</strong> When solutions exist</li>
                            <li><strong>Direction Fields:</strong> Geometric interpretation</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="grid md:grid-cols-3 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">
                        <i class="fas fa-calculator text-blue-600 mr-2"></i>Solution Methods Review
                    </h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-blue-50 rounded">
                            <strong>Separable ODEs:</strong>
                            <p class="text-sm mt-1">$\frac{dy}{dx} = g(x)h(y)$</p>
                            <p class="text-xs text-gray-600">Separate variables and integrate</p>
                        </div>
                        <div class="p-3 bg-green-50 rounded">
                            <strong>Linear ODEs:</strong>
                            <p class="text-sm mt-1">$\frac{dy}{dx} + P(x)y = Q(x)$</p>
                            <p class="text-xs text-gray-600">Use integrating factor $\mu(x) = e^{\int P(x)dx}$</p>
                        </div>
                        <div class="p-3 bg-purple-50 rounded">
                            <strong>Exact ODEs:</strong>
                            <p class="text-sm mt-1">$M(x,y)dx + N(x,y)dy = 0$</p>
                            <p class="text-xs text-gray-600">When $\frac{\partial M}{\partial y} = \frac{\partial N}{\partial x}$</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">
                        <i class="fas fa-chart-line text-green-600 mr-2"></i>Key Mathematical Concepts
                    </h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-yellow-50 rounded">
                            <strong>Exponential Growth/Decay:</strong>
                            <p class="text-sm mt-1">$y = Ae^{kt}$</p>
                            <p class="text-xs text-gray-600">k > 0: growth, k < 0: decay</p>
                        </div>
                        <div class="p-3 bg-orange-50 rounded">
                            <strong>Equilibrium Solutions:</strong>
                            <p class="text-sm mt-1">$\frac{dy}{dt} = 0$</p>
                            <p class="text-xs text-gray-600">Constant solutions</p>
                        </div>
                        <div class="p-3 bg-red-50 rounded">
                            <strong>Stability Analysis:</strong>
                            <p class="text-sm mt-1">Linearization near equilibria</p>
                            <p class="text-xs text-gray-600">Stable vs unstable behavior</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">
                        <i class="fas fa-laptop-code text-purple-600 mr-2"></i>Computational Tools
                    </h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-indigo-50 rounded">
                            <strong>Python Libraries:</strong>
                            <p class="text-sm mt-1">scipy.integrate, numpy, matplotlib</p>
                            <p class="text-xs text-gray-600">For numerical solutions and plotting</p>
                        </div>
                        <div class="p-3 bg-cyan-50 rounded">
                            <strong>R Packages:</strong>
                            <p class="text-sm mt-1">deSolve, ggplot2, plotly</p>
                            <p class="text-xs text-gray-600">Statistical analysis and visualization</p>
                        </div>
                        <div class="p-3 bg-pink-50 rounded">
                            <strong>Symbolic Math:</strong>
                            <p class="text-sm mt-1">SymPy (Python), Mathematica</p>
                            <p class="text-xs text-gray-600">Analytical solutions</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="theorem-box mt-6">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-exclamation-triangle mr-2"></i>Important Theorems</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Existence and Uniqueness Theorem</h4>
                        <p class="text-sm mb-2">For the IVP: $\frac{dy}{dx} = f(x,y)$, $y(x_0) = y_0$</p>
                        <p class="text-sm">If $f$ and $\frac{\partial f}{\partial y}$ are continuous in a rectangle containing $(x_0, y_0)$, then there exists a unique solution in some interval around $x_0$.</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Fundamental Theorem of Calculus</h4>
                        <p class="text-sm mb-2">$\frac{d}{dx}\int_{a}^{x} f(t)dt = f(x)$</p>
                        <p class="text-sm">Essential for solving ODEs by integration and understanding accumulation functions.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Introduction -->
        <section id="intro" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-rocket text-blue-600 mr-3"></i>Introduction to ODE Modeling
            </h2>
            
            <div class="definition-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-lightbulb mr-2"></i>Mathematical Modeling with ODEs</h3>
                <p class="mb-3">Mathematical modeling with first-order differential equations involves:</p>
                <ul class="list-disc list-inside space-y-2">
                    <li><strong>Problem Identification:</strong> Recognizing systems that change continuously over time</li>
                    <li><strong>Mathematical Translation:</strong> Converting real-world relationships into ODE form</li>
                    <li><strong>Solution Strategy:</strong> Choosing appropriate solution methods (separable, linear, exact)</li>
                    <li><strong>Parameter Estimation:</strong> Determining model parameters from data</li>
                    <li><strong>Validation:</strong> Testing model predictions against observations</li>
                    <li><strong>Interpretation:</strong> Understanding what solutions mean in context</li>
                </ul>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-8">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-tools text-green-600 mr-2"></i>Solution Methods Review
                    </h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-blue-50 rounded">
                            <strong>Separable:</strong> $\frac{dy}{dx} = g(x)h(y)$
                            <br><span class="text-sm text-gray-600">Growth, decay, cooling problems</span>
                        </div>
                        <div class="p-3 bg-green-50 rounded">
                            <strong>Linear:</strong> $\frac{dy}{dx} + P(x)y = Q(x)$
                            <br><span class="text-sm text-gray-600">Mixing, circuits, harvesting</span>
                        </div>
                        <div class="p-3 bg-purple-50 rounded">
                            <strong>Exact:</strong> $M dx + N dy = 0$
                            <br><span class="text-sm text-gray-600">Conservative systems, thermodynamics</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-globe text-blue-600 mr-2"></i>Application Domains
                    </h3>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <i class="fas fa-flask text-green-500 mr-2"></i>
                            <span>Chemistry & Biology</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-chart-line text-blue-500 mr-2"></i>
                            <span>Economics & Finance</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-atom text-purple-500 mr-2"></i>
                            <span>Physics & Engineering</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-users text-orange-500 mr-2"></i>
                            <span>Population Dynamics</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-thermometer-half text-red-500 mr-2"></i>
                            <span>Environmental Science</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Modeling Methodology -->
        <section id="methodology" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-cogs text-purple-600 mr-3"></i>Systematic Modeling Methodology
            </h2>

            <div class="theorem-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-list-ol mr-2"></i>The Modeling Process</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-2">1. Problem Formulation</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Identify the system and variables</li>
                            <li>Determine what changes over time</li>
                            <li>Establish the independent variable (usually time)</li>
                            <li>Define the dependent variable(s)</li>
                        </ul>
                        
                        <h4 class="font-bold mb-2 mt-4">2. Mathematical Translation</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Express rate of change: $\frac{dy}{dt}$</li>
                            <li>Identify relationships between variables</li>
                            <li>Apply physical laws or principles</li>
                            <li>Include initial/boundary conditions</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">3. Solution Strategy</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Classify the ODE type</li>
                            <li>Choose appropriate solution method</li>
                            <li>Solve analytically or numerically</li>
                            <li>Handle parameter estimation</li>
                        </ul>
                        
                        <h4 class="font-bold mb-2 mt-4">4. Validation & Analysis</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Compare with experimental data</li>
                            <li>Perform sensitivity analysis</li>
                            <li>Check limiting behavior</li>
                            <li>Interpret results in context</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-decision text-blue-600 mr-2"></i>Equation Classification Decision Tree
                </h3>
                <div class="chart-container">
                    <canvas id="classificationChart"></canvas>
                </div>
            </div>
        </section>

        <!-- Mixing Problems -->
        <section id="mixing" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-tint text-blue-600 mr-3"></i>Mixing Problems (Tank Problems)
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-flask mr-2"></i>General Mixing Problem Setup</h3>
                <p class="mb-4">A tank contains a solution with concentration that changes over time due to inflow and outflow.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Fundamental Principle:</h4>
                    <p class="text-center text-lg">
                        $$\frac{d(\text{Amount})}{dt} = \text{Rate In} - \text{Rate Out}$$
                    </p>
                </div>

                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <h4 class="font-bold mb-2">Key Variables:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            <li>$V(t)$ = Volume in tank at time $t$</li>
                            <li>$S(t)$ = Amount of substance at time $t$</li>
                            <li>$C(t) = S(t)/V(t)$ = Concentration</li>
                            <li>$r_{in}, r_{out}$ = Flow rates in/out</li>
                            <li>$C_{in}$ = Input concentration</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">Standard Model:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            <li>Rate In = $r_{in} \cdot C_{in}$</li>
                            <li>Rate Out = $r_{out} \cdot C(t)$</li>
                            <li>ODE: $\frac{dS}{dt} = r_{in}C_{in} - r_{out}C(t)$</li>
                            <li>If $V$ constant: $\frac{dC}{dt} + \frac{r}{V}C = \frac{r_{in}C_{in}}{V}$</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 1: Salt Water Tank</h3>
                <p class="mb-3">A tank contains 1000 L of pure water. Salt water with concentration 0.5 kg/L flows in at 10 L/min. The well-mixed solution flows out at 10 L/min.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Solution Setup:</h4>
                    <ul class="list-disc list-inside space-y-1">
                        <li>$V = 1000$ L (constant volume)</li>
                        <li>$r_{in} = r_{out} = 10$ L/min</li>
                        <li>$C_{in} = 0.5$ kg/L</li>
                        <li>$C(0) = 0$ kg/L (initially pure water)</li>
                    </ul>
                    
                    <p class="mt-3"><strong>ODE:</strong> $\frac{dC}{dt} + \frac{10}{1000}C = \frac{10 \times 0.5}{1000}$</p>
                    <p class="mt-2">Simplifies to: $\frac{dC}{dt} + 0.01C = 0.005$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Analytical Solution:</h4>
                    <p>This is a first-order linear ODE. Using integrating factor $\mu = e^{0.01t}$:</p>
                    <p class="text-center mt-2">$$C(t) = 0.5(1 - e^{-0.01t})$$</p>
                    <p class="mt-2">Equilibrium concentration: $C_{\infty} = 0.5$ kg/L</p>
                    <p>Time to reach 95% of equilibrium: $t = -\frac{\ln(0.05)}{0.01} \approx 300$ minutes</p>
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Python Implementation
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp

def mixing_tank(t, y, r_in, r_out, V, C_in):
    """
    Tank mixing model
    y[0] = C(t) = concentration
    """
    C = y[0]
    dCdt = (r_in * C_in - r_out * C) / V
    return [dCdt]

# Parameters
V = 1000  # Tank volume (L)
r_in = r_out = 10  # Flow rates (L/min)
C_in = 0.5  # Input concentration (kg/L)
C0 = 0  # Initial concentration

# Time span
t_span = (0, 500)
t_eval = np.linspace(0, 500, 1000)

# Solve ODE
sol = solve_ivp(mixing_tank, t_span, [C0], t_eval=t_eval, 
                args=(r_in, r_out, V, C_in))

# Analytical solution for comparison
t_analytical = t_eval
C_analytical = C_in * (1 - np.exp(-r_out * t_analytical / V))

# Plot results
plt.figure(figsize=(10, 6))
plt.plot(sol.t, sol.y[0], 'b-', linewidth=2, 
         label='Numerical Solution')
plt.plot(t_analytical, C_analytical, 'r--', linewidth=2, 
         label='Analytical Solution')
plt.axhline(y=C_in, color='g', linestyle=':', alpha=0.7, 
            label='Equilibrium')
plt.xlabel('Time (min)')
plt.ylabel('Concentration (kg/L)')
plt.title('Salt Water Tank Mixing Problem')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# Parameter estimation from data
def fit_mixing_model(t_data, C_data, V, r_flow):
    """Estimate C_in from experimental data"""
    from scipy.optimize import curve_fit
    
    def model(t, C_in):
        return C_in * (1 - np.exp(-r_flow * t / V))
    
    popt, pcov = curve_fit(model, t_data, C_data)
    return popt[0], np.sqrt(pcov[0,0])

# Example with noisy data
t_data = np.array([0, 50, 100, 150, 200, 300, 400])
C_true = 0.5 * (1 - np.exp(-0.01 * t_data))
C_data = C_true + np.random.normal(0, 0.01, len(t_data))

C_in_est, C_in_err = fit_mixing_model(t_data, C_data, V, r_out)
print(f"Estimated C_in: {C_in_est:.3f} ± {C_in_err:.3f} kg/L")

# Sensitivity analysis
def sensitivity_analysis():
    V_values = np.linspace(800, 1200, 50)
    C_final = []
    
    for V_test in V_values:
        t_final = 300
        C_eq = C_in * (1 - np.exp(-r_out * t_final / V_test))
        C_final.append(C_eq)
    
    plt.figure(figsize=(8, 5))
    plt.plot(V_values, C_final, 'b-', linewidth=2)
    plt.xlabel('Tank Volume (L)')
    plt.ylabel('Concentration at t=300 min (kg/L)')
    plt.title('Sensitivity to Tank Volume')
    plt.grid(True, alpha=0.3)
    plt.show()

sensitivity_analysis()</code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-blue-800 mr-2"></i>R Implementation
                    </h3>
                    <div class="code-block r-code">
                        <pre><code>library(deSolve)
library(ggplot2)
library(dplyr)

# Define mixing tank model
mixing_tank <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    C <- state[1]
    dCdt <- (r_in * C_in - r_out * C) / V
    return(list(dCdt))
  })
}

# Parameters
parms <- list(
  V = 1000,      # Tank volume (L)
  r_in = 10,     # Inflow rate (L/min)
  r_out = 10,    # Outflow rate (L/min)
  C_in = 0.5     # Input concentration (kg/L)
)

# Initial conditions and time
initial_state <- c(C = 0)  # Initial concentration
times <- seq(0, 500, by = 1)

# Solve ODE
solution <- ode(y = initial_state, 
                times = times, 
                func = mixing_tank, 
                parms = parms)

# Convert to data frame
sol_df <- as.data.frame(solution)

# Analytical solution
sol_df$C_analytical <- with(parms, 
  C_in * (1 - exp(-r_out * sol_df$time / V)))

# Create visualization
p1 <- ggplot(sol_df, aes(x = time)) +
  geom_line(aes(y = C, color = "Numerical"), size = 1.2) +
  geom_line(aes(y = C_analytical, color = "Analytical"), 
            linetype = "dashed", size = 1.2) +
  geom_hline(yintercept = parms$C_in, 
             color = "green", linetype = "dotted", alpha = 0.7) +
  labs(title = "Salt Water Tank Mixing Problem",
       x = "Time (min)",
       y = "Concentration (kg/L)",
       color = "Solution") +
  theme_minimal() +
  theme(legend.position = "bottom")

print(p1)

# Parameter estimation function
estimate_parameters <- function(t_data, C_data, V, r_flow) {
  # Define model function
  model_func <- function(t, C_in) {
    C_in * (1 - exp(-r_flow * t / V))
  }
  
  # Fit model
  fit <- nls(C_data ~ model_func(t_data, C_in),
             start = list(C_in = 0.4))
  
  return(summary(fit))
}

# Generate synthetic data with noise
set.seed(123)
t_data <- c(0, 50, 100, 150, 200, 300, 400)
C_true <- with(parms, C_in * (1 - exp(-r_out * t_data / V)))
C_data <- C_true + rnorm(length(t_data), 0, 0.01)

# Estimate parameters
fit_result <- estimate_parameters(t_data, C_data, 
                                  parms$V, parms$r_out)
print(fit_result)

# Sensitivity analysis
sensitivity_analysis <- function() {
  V_range <- seq(800, 1200, length.out = 50)
  t_final <- 300
  
  C_final <- sapply(V_range, function(V_test) {
    with(parms, C_in * (1 - exp(-r_out * t_final / V_test)))
  })
  
  p2 <- ggplot(data.frame(V = V_range, C_final = C_final), aes(x = V, y = C_final)) +
    geom_line(color = "blue", size = 1.2) +
    labs(title = "Sensitivity to Tank Volume",
         x = "Tank Volume (L)",
         y = "Concentration at t=300 min (kg/L)") +
    theme_minimal()
  
  return(p2)
}

sens_plot <- sensitivity_analysis()
print(sens_plot)

# Multi-tank system
multi_tank_model <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    C1 <- state[1]  # Tank 1 concentration
    C2 <- state[2]  # Tank 2 concentration
    
    # Tank 1: Input from external source
    dC1dt <- (r_in * C_in - r12 * C1) / V1
    
    # Tank 2: Input from Tank 1, output to environment
    dC2dt <- (r12 * C1 - r_out * C2) / V2
    
    return(list(c(dC1dt, dC2dt)))
  })
}

# Multi-tank parameters
multi_parms <- list(
  V1 = 1000, V2 = 800,
  r_in = 10, r12 = 10, r_out = 10,
  C_in = 0.5
)

multi_initial <- c(C1 = 0, C2 = 0)
multi_solution <- ode(y = multi_initial,
                      times = times,
                      func = multi_tank_model,
                      parms = multi_parms)

multi_df <- as.data.frame(multi_solution)

# Plot multi-tank system
p3 <- ggplot(multi_df, aes(x = time)) +
  geom_line(aes(y = C1, color = "Tank 1"), size = 1.2) +
  geom_line(aes(y = C2, color = "Tank 2"), size = 1.2) +
  labs(title = "Two-Tank Mixing System",
       x = "Time (min)",
       y = "Concentration (kg/L)",
       color = "Tank") +
  theme_minimal() +
  theme(legend.position = "bottom")

print(p3)
                        </code></pre>
                    </div>
                </div>
            </div>

            <!-- Interactive Mixing Problem Visualization -->
            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-chart-line text-blue-600 mr-2"></i>Interactive Mixing Problem Visualization
                </h3>
                <p class="text-gray-600 mb-4">
                    This chart shows the concentration change over time in a salt water mixing problem. 
                    The solution approaches equilibrium concentration exponentially.
                </p>
                <div class="chart-container">
                    <canvas id="mixingChart"></canvas>
                </div>

                <!-- Interactive Controls for Mixing Problem -->
                <div class="mt-4 bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-bold mb-3">Interactive Parameter Controls</h4>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium mb-1">Tank Volume (L)</label>
                            <input type="range" id="volumeSlider" min="500" max="2000" value="1000"
                                   class="w-full" oninput="updateMixingChart()">
                            <span id="volumeValue" class="text-sm text-gray-600">1000 L</span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Flow Rate (L/min)</label>
                            <input type="range" id="flowSlider" min="5" max="20" value="10"
                                   class="w-full" oninput="updateMixingChart()">
                            <span id="flowValue" class="text-sm text-gray-600">10 L/min</span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Inflow Concentration (kg/L)</label>
                            <input type="range" id="concentrationSlider" min="0.1" max="1.0" step="0.1" value="0.5"
                                   class="w-full" oninput="updateMixingChart()">
                            <span id="concentrationValue" class="text-sm text-gray-600">0.5 kg/L</span>
                        </div>
                    </div>
                </div>

                <div class="mt-4 grid md:grid-cols-3 gap-4 text-sm">
                    <div class="bg-blue-50 p-3 rounded">
                        <strong>Current Conditions:</strong><br>
                        <span id="currentVolume">Tank volume: 1000 L</span><br>
                        <span id="currentFlow">Flow rate: 10 L/min</span><br>
                        <span id="currentConcentration">Inflow concentration: 0.5 kg/L</span>
                    </div>
                    <div class="bg-green-50 p-3 rounded">
                        <strong>Calculated Parameters:</strong><br>
                        <span id="timeConstant">Time constant: τ = 100 min</span><br>
                        <span id="equilibriumTime">Equilibrium time: ~500 min</span><br>
                        <span id="halfTime">Half-time: ~69 min</span>
                    </div>
                    <div class="bg-orange-50 p-3 rounded">
                        <strong>Mathematical Model:</strong><br>
                        dC/dt = (r/V)(C_in - C)<br>
                        Solution: C(t) = C_in(1 - e^(-t/τ))<br>
                        Where τ = V/r
                    </div>
                </div>
            </div>
        </section>

        <!-- Mixing Problem Variations -->
        <div class="theorem-box mt-6">
            <h3 class="text-xl font-bold mb-4">
                <i class="fas fa-cogs mr-2"></i>Mixing Problem Variations
            </h3>
            <p class="mb-4">Real-world mixing problems often involve more complex scenarios. Here are common variations and their mathematical formulations:</p>
            
            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-3"><i class="fas fa-chart-line mr-2"></i>Variable Volume Tank</h4>
                    <p class="mb-2">When inflow ≠ outflow, volume changes with time:</p>
                    <div class="bg-white bg-opacity-20 p-3 rounded mb-2">
                        <p>$$\frac{dV}{dt} = r_{in} - r_{out}$$</p>
                        <p>$$\frac{dS}{dt} = r_{in}C_{in} - r_{out}C(t)$$</p>
                        <p>$$C(t) = \frac{S(t)}{V(t)}$$</p>
                    </div>
                    <p class="text-sm">More complex as both $S(t)$ and $V(t)$ vary</p>
                </div>
                
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-3"><i class="fas fa-wave-square mr-2"></i>Time-Varying Input</h4>
                    <p class="mb-2">Input concentration changes with time:</p>
                    <div class="bg-white bg-opacity-20 p-3 rounded mb-2">
                        <p>$$C_{in}(t) = A + B\sin(\omega t)$$</p>
                        <p>$$\frac{dC}{dt} + \frac{r}{V}C = \frac{r \cdot C_{in}(t)}{V}$$</p>
                    </div>
                    <p class="text-sm">Periodic forcing leads to oscillatory solutions</p>
                </div>
                
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-3"><i class="fas fa-sitemap mr-2"></i>Multi-Tank Systems</h4>
                    <p class="mb-2">Connected tanks create system of ODEs:</p>
                    <div class="bg-white bg-opacity-20 p-3 rounded mb-2">
                        <p>$$\frac{dC_1}{dt} = \frac{r_{in}C_{in} - r_{12}C_1}{V_1}$$</p>
                        <p>$$\frac{dC_2}{dt} = \frac{r_{12}C_1 - r_{out}C_2}{V_2}$$</p>
                    </div>
                    <p class="text-sm">Cascade effect with time delays</p>
                </div>
                
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-3"><i class="fas fa-exclamation-triangle mr-2"></i>Chemical Reactions</h4>
                    <p class="mb-2">Substance undergoes reaction while mixing:</p>
                    <div class="bg-white bg-opacity-20 p-3 rounded mb-2">
                        <p>$$\frac{dC}{dt} = \frac{r_{in}C_{in} - r_{out}C}{V} - kC^n$$</p>
                    </div>
                    <p class="text-sm">$k$ = reaction rate, $n$ = reaction order</p>
                </div>
            </div>
            
            <div class="mt-6 bg-white bg-opacity-20 p-4 rounded">
                <h4 class="font-bold mb-3"><i class="fas fa-lightbulb mr-2"></i>Key Solution Strategies</h4>
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <h5 class="font-semibold mb-2">Analytical Methods:</h5>
                        <ul class="text-sm space-y-1">
                            <li>• Integrating factor for linear cases</li>
                            <li>• Separation of variables when applicable</li>
                            <li>• Laplace transforms for complex inputs</li>
                            <li>• Steady-state analysis for equilibrium</li>
                        </ul>
                    </div>
                    <div>
                        <h5 class="font-semibold mb-2">Numerical Approaches:</h5>
                        <ul class="text-sm space-y-1">
                            <li>• Runge-Kutta methods for systems</li>
                            <li>• Adaptive step-size for stiff problems</li>
                            <li>• Parameter estimation from data</li>
                            <li>• Sensitivity and uncertainty analysis</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="mt-4 p-4 bg-white bg-opacity-20 rounded">
                <h4 class="font-bold mb-2"><i class="fas fa-industry mr-2"></i>Real-World Applications</h4>
                <div class="grid md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <strong>Water Treatment:</strong>
                        <ul class="mt-1 space-y-1">
                            <li>• Chlorination systems</li>
                            <li>• pH adjustment tanks</li>
                            <li>• Coagulation processes</li>
                        </ul>
                    </div>
                    <div>
                        <strong>Chemical Processing:</strong>
                        <ul class="mt-1 space-y-1">
                            <li>• Reactor vessels</li>
                            <li>• Distillation columns</li>
                            <li>• Batch processing</li>
                        </ul>
                    </div>
                    <div>
                        <strong>Environmental:</strong>
                        <ul class="mt-1 space-y-1">
                            <li>• Lake pollution models</li>
                            <li>• Atmospheric mixing</li>
                            <li>• Groundwater contamination</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Population Dynamics -->
        <section id="population" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-users text-green-600 mr-3"></i>Population Dynamics
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-seedling mr-2"></i>Population Growth Models</h3>
                <p class="mb-4">Population dynamics models describe how populations change over time under various conditions.</p>
                
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Exponential Growth</h4>
                        <p class="text-sm mb-2">$\frac{dP}{dt} = rP$</p>
                        <p class="text-sm">Solution: $P(t) = P_0 e^{rt}$</p>
                        <p class="text-xs mt-1">Unlimited resources</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Logistic Growth</h4>
                        <p class="text-sm mb-2">$\frac{dP}{dt} = rP(1 - \frac{P}{K})$</p>
                        <p class="text-sm">S-shaped growth curve</p>
                        <p class="text-xs mt-1">Carrying capacity $K$</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">With Harvesting</h4>
                        <p class="text-sm mb-2">$\frac{dP}{dt} = rP - H$</p>
                        <p class="text-sm">Constant harvest rate</p>
                        <p class="text-xs mt-1">Sustainability analysis</p>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 2: Logistic Population Model</h3>
                <p class="mb-3">A population grows according to the logistic model with intrinsic growth rate $r = 0.1$ per year and carrying capacity $K = 1000$ individuals.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Logistic Equation:</h4>
                    <p class="text-center">$$\frac{dP}{dt} = rP\left(1 - \frac{P}{K}\right) = 0.1P\left(1 - \frac{P}{1000}\right)$$</p>
                    
                    <p class="mt-3"><strong>This is separable:</strong></p>
                    <p class="text-center">$$\frac{dP}{P(1 - P/K)} = r \, dt$$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Solution by Partial Fractions:</h4>
                    <p>$$\frac{1}{P(1 - P/K)} = \frac{A}{P} + \frac{B}{1 - P/K}$$</p>
                    <p>After partial fractions and integration:</p>
                    <p class="text-center mt-2">$$P(t) = \frac{K}{1 + \left(\frac{K}{P_0} - 1\right)e^{-rt}}$$</p>
                    <p class="mt-2"><strong>Key Features:</strong></p>
                    <ul class="list-disc list-inside space-y-1 text-sm">
                        <li>Inflection point at $P = K/2$</li>
                        <li>Maximum growth rate at $P = K/2$: $\frac{dP}{dt}|_{max} = \frac{rK}{4}$</li>
                        <li>Approaches carrying capacity: $\lim_{t \to \infty} P(t) = K$</li>
                    </ul>
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Population Dynamics in Python
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp
from scipy.optimize import fsolve

# Population growth models
def exponential_growth(t, y, r):
    """Exponential growth: dP/dt = rP"""
    return [r * y[0]]

def logistic_growth(t, y, r, K):
    """Logistic growth: dP/dt = rP(1 - P/K)"""
    P = y[0]
    return [r * P * (1 - P/K)]

def growth_with_harvesting(t, y, r, K, H):
    """Growth with harvesting: dP/dt = rP(1 - P/K) - H"""
    P = y[0]
    return [r * P * (1 - P/K) - H]

# Parameters
r = 0.1  # Growth rate (1/year)
K = 1000  # Carrying capacity
P0 = 50   # Initial population
t_span = (0, 50)
t_eval = np.linspace(0, 50, 500)

# Solve different models
sol_exp = solve_ivp(exponential_growth, t_span, [P0], 
                    t_eval=t_eval, args=(r,))
sol_log = solve_ivp(logistic_growth, t_span, [P0], 
                    t_eval=t_eval, args=(r, K))

# Analytical solutions
t = t_eval
P_exp_analytical = P0 * np.exp(r * t)
P_log_analytical = K / (1 + (K/P0 - 1) * np.exp(-r * t))

# Plot comparison
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# Population curves
ax1.plot(t, P_exp_analytical, 'r-', linewidth=2, 
         label='Exponential')
ax1.plot(t, P_log_analytical, 'b-', linewidth=2, 
         label='Logistic')
ax1.axhline(y=K, color='g', linestyle='--', alpha=0.7, 
            label='Carrying Capacity')
ax1.set_xlabel('Time (years)')
ax1.set_ylabel('Population')
ax1.set_title('Population Growth Models')
ax1.legend()
ax1.grid(True, alpha=0.3)
ax1.set_ylim(0, 1200)

# Growth rates
dPdt_exp = r * P_exp_analytical
dPdt_log = r * P_log_analytical * (1 - P_log_analytical/K);

ax2.plot(P_exp_analytical, dPdt_exp, 'r-', linewidth=2, 
         label='Exponential')
ax2.plot(P_log_analytical, dPdt_log, 'b-', linewidth=2, 
         label='Logistic')
ax2.axvline(x=K/2, color='purple', linestyle=':', alpha=0.7, 
            label='Max Growth Rate')
ax2.set_xlabel('Population')
ax2.set_ylabel('Growth Rate (dP/dt)')
ax2.set_title('Phase Portrait')
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Harvesting analysis
def analyze_harvesting():
    """Analyze sustainable harvesting"""
    # Critical harvesting rate (maximum sustainable)
    H_max = r * K / 4  # At P = K/2
    
    print(f"Maximum sustainable harvest rate: {H_max:.2f}")
    
    # Different harvesting scenarios
    H_values = [0, H_max/2, H_max, H_max*1.2]
    colors = ['blue', 'green', 'orange', 'red']
    labels = ['No harvest', 'Sustainable', 'Critical', 'Overharvest']
    
    plt.figure(figsize=(12, 8))
    
    for i, H in enumerate(H_values):
        if H < H_max * 1.1:  # Avoid extinction scenarios
            sol_h = solve_ivp(growth_with_harvesting, t_span, [P0], 
                             t_eval=t_eval, args=(r, K, H))
            plt.plot(sol_h.t, sol_h.y[0], color=colors[i], 
                    linewidth=2, label=f'{labels[i]} (H={H:.1f})')
    
    plt.axhline(y=K, color='gray', linestyle='--', alpha=0.5, 
                label='Carrying Capacity')
    plt.xlabel('Time (years)')
    plt.ylabel('Population')
    plt.title('Population Dynamics with Harvesting')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()
    
    # Equilibrium analysis
    def equilibrium_points(H):
        """Find equilibrium points for harvesting model"""
        def f(P):
            return r * P * (1 - P/K) - H
        
        if H <= H_max:
            roots = fsolve(f, [K/4, 3*K/4])
            return roots[roots > 0]  # Positive roots only
        else:
            return []
    
    H_range = np.linspace(0, H_max*1.5, 100)
    equilibria = []
    
    for H in H_range:
        eq_points = equilibrium_points(H)
        if len(eq_points) > 0:
            equilibria.extend([(H, P) for P in eq_points])
    
    if equilibria:
        H_eq, P_eq = zip(*equilibria)
        plt.figure(figsize=(10, 6))
        plt.plot(H_eq, P_eq, 'b.', markersize=3)
        plt.axvline(x=H_max, color='red', linestyle='--', 
                   label=f'Critical H = {H_max:.2f}')
        plt.xlabel('Harvest Rate')
        plt.ylabel('Equilibrium Population')
        plt.title('Bifurcation Diagram: Harvest Rate vs Population')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()

analyze_harvesting()

# Parameter estimation from data
def fit_logistic_model(t_data, P_data):
    """Fit logistic model to population data"""
    from scipy.optimize import curve_fit
    
    def logistic_func(t, r, K, P0):
        return K / (1 + (K/P0 - 1) * np.exp(-r * t))
    
    # Initial guess
    K_guess = max(P_data) * 1.2
    r_guess = 0.1
    P0_guess = P_data[0]
    
    popt, pcov = curve_fit(logistic_func, t_data, P_data, 
                          p0=[r_guess, K_guess, P0_guess],
                          bounds=([0, max(P_data), 0], 
                                 [1, max(P_data)*3, max(P_data)]))
    
    r_fit, K_fit, P0_fit = popt
    r_err, K_err, P0_err = np.sqrt(np.diag(pcov))
    
    return (r_fit, K_fit, P0_fit), (r_err, K_err, P0_err)

# Generate synthetic data
t_data = np.array([0, 2, 5, 10, 15, 20, 25, 30])
P_true = K / (1 + (K/P0 - 1) * np.exp(-r * t_data))
P_data = P_true + np.random.normal(0, P_true * 0.05)  # 5% noise

# Fit model
params, errors = fit_logistic_model(t_data, P_data)
r_fit, K_fit, P0_fit = params
print(f"Fitted parameters:")
print(f"r = {r_fit:.4f} ± {errors[0]:.4f}")
print(f"K = {K_fit:.1f} ± {errors[1]:.1f}")
print(f"P0 = {P0_fit:.1f} ± {errors[2]:.1f}")

# Compare with true values
P_fitted = K_fit / (1 + (K_fit/P0_fit - 1) * np.exp(-r_fit * t_eval))

plt.figure(figsize=(10, 6))
plt.plot(t_eval, P_log_analytical, 'b-', linewidth=2, label='True Model')
plt.plot(t_eval, P_fitted, 'r--', linewidth=2, label='Fitted Model')
plt.scatter(t_data, P_data, color='black', s=50, 
           label='Data Points', zorder=5)
plt.xlabel('Time (years)')
plt.ylabel('Population')
plt.title('Logistic Model Fitting')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# Model diagnostics - residual analysis
residuals = P_data - K_fit / (1 + (K_fit/P0_fit - 1) * np.exp(-r_fit * t_data))
fitted_values = K_fit / (1 + (K_fit/P0_fit - 1) * np.exp(-r_fit * t_data))

fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# Residuals vs fitted values
ax1.scatter(fitted_values, residuals, color='blue', s=50)
ax1.axhline(y=0, color='red', linestyle='--', alpha=0.7)
ax1.set_xlabel('Fitted Values')
ax1.set_ylabel('Residuals')
ax1.set_title('Residual Analysis')
ax1.grid(True, alpha=0.3)

# Residuals vs time
ax2.scatter(t_data, residuals, color='blue', s=50)
ax2.plot(t_data, residuals, color='blue', alpha=0.5)
ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7)
ax2.set_xlabel('Time')
ax2.set_ylabel('Residuals')
ax2.set_title('Residuals vs Time')
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()
                        </code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-red-600 mr-2"></i>Population Dynamics in R
                    </h3>
                    <div class="code-block r-code">
                        <pre><code># Population growth models in R
library(deSolve)
library(ggplot2)
library(gridExtra)

# Define population growth models
exponential_growth <- function(t, y, parms) {
  with(as.list(c(y, parms)), {
    dP <- r * P
    list(c(dP))
  })
}

logistic_growth <- function(t, y, parms) {
  with(as.list(c(y, parms)), {
    dP <- r * P * (1 - P/K)
    list(c(dP))
  })
}

growth_with_harvesting <- function(t, y, parms) {
  with(as.list(c(y, parms)), {
    dP <- r * P * (1 - P/K) - H
    list(c(dP))
  })
}

# Parameters
parameters <- list(r = 0.1, K = 1000, H = 25)
initial_conditions <- c(P = 50)
times <- seq(0, 50, by = 0.1)

# Solve ODEs
sol_exp <- ode(y = initial_conditions, 
               times = times, 
               func = exponential_growth, 
               parms = list(r = parameters$r))

sol_log <- ode(y = initial_conditions, 
               times = times, 
               func = logistic_growth, 
               parms = list(r = parameters$r, K = parameters$K))

sol_harv <- ode(y = initial_conditions, 
                times = times, 
                func = growth_with_harvesting, 
                parms = parameters)

# Create data frames for plotting
df_exp <- data.frame(time = sol_exp[,1], population = sol_exp[,2], model = "Exponential")
df_log <- data.frame(time = sol_log[,1], population = sol_log[,2], model = "Logistic")
df_harv <- data.frame(time = sol_harv[,1], population = sol_harv[,2], model = "With Harvesting")

combined_df <- rbind(df_exp, df_log, df_harv)

# Analytical solutions for comparison
t_analytical <- times
P_exp_analytical <- initial_conditions[1] * exp(parameters$r * t_analytical)
P_log_analytical <- parameters$K / (1 + (parameters$K/initial_conditions[1] - 1) * 
                                   exp(-parameters$r * t_analytical))

# Plot results
p1 <- ggplot(combined_df, aes(x = time, y = population, color = model)) +
  geom_line(size = 1.2) +
  geom_hline(yintercept = parameters$K, linetype = "dashed", 
             color = "gray", alpha = 0.7) +
  labs(title = "Population Growth Models Comparison",
       x = "Time (years)",
       y = "Population",
       color = "Model") +
  theme_minimal() +
  theme(legend.position = "bottom") +
  scale_color_manual(values = c("red", "blue", "green"))

print(p1)

# Parameter estimation and model fitting
# Generate synthetic data with noise
set.seed(123)
t_data <- c(0, 2, 5, 10, 15, 20, 25, 30)
P_true <- parameters$K / (1 + (parameters$K/initial_conditions[1] - 1) * 
                         exp(-parameters$r * t_data))
P_data <- P_true + rnorm(length(t_data), 0, P_true * 0.05)  # 5% noise

# Fit logistic model using nls
logistic_model <- function(t, r, K, P0) {
  K / (1 + (K/P0 - 1) * exp(-r * t))
}

# Non-linear least squares fitting
fit_result <- nls(P_data ~ logistic_model(t_data, r, K, P0),
                  start = list(r = 0.1, K = 1200, P0 = 50),
                  algorithm = "port",
                  lower = c(0.01, max(P_data), 0),
                  upper = c(1, max(P_data)*3, max(P_data)))

# Extract fitted parameters
fitted_params <- summary(fit_result)
print("Fitted Parameters:")
print(fitted_params$parameters)

# Model predictions
t_pred <- seq(0, 40, length.out = 200)
P_pred <- predict(fit_result, newdata = list(t_data = t_pred))

# Plot fitted model
fitting_df <- data.frame(
  time = c(t_analytical, t_pred, t_data),
  population = c(P_log_analytical, P_pred, P_data),
  type = c(rep("True", length(t_analytical)), 
           rep("Fitted", length(t_pred)),
           rep("Data", length(t_data)))
)

p3 <- ggplot(fitting_df, aes(x = time, y = population)) +
  geom_line(data = subset(fitting_df, type == "True"), 
            aes(color = "True Model"), size = 1.2) +
  geom_line(data = subset(fitting_df, type == "Fitted"), 
            aes(color = "Fitted Model"), size = 1.2, linetype = "dashed") +
  geom_point(data = subset(fitting_df, type == "Data"), 
             aes(color = "Data Points"), size = 3) +
  labs(title = "Logistic Model Parameter Estimation",
       x = "Time (years)",
       y = "Population",
       color = "Legend") +
  theme_minimal() +
  scale_color_manual(values = c("blue", "red", "black"))

print(p3)
                        </code></pre>
                    </div>
                </div>
            </div>

            <!-- Interactive Population Dynamics Control Panel -->
            <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
                <h3 class="text-lg font-bold text-gray-800 mb-4">
                    <i class="fas fa-sliders-h text-purple-600 mr-2"></i>Interactive Population Dynamics Visualization
                </h3>
                <p class="text-gray-600 mb-4">Adjust the parameters below to see how they affect population growth models in real-time.</p>
                
                <div class="grid md:grid-cols-3 gap-6">
                    <div>
                        <label for="growthRate" class="block text-sm font-medium text-gray-700 mb-2">
                            Growth Rate (r): <span id="growthRateValue" class="font-bold text-blue-600">0.10</span>
                        </label>
                        <input type="range" id="growthRate" min="0.01" max="0.5" step="0.01" value="0.10" 
                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>0.01</span>
                            <span>0.5</span>
                        </div>
                    </div>
                    
                    <div>
                        <label for="carryingCapacity" class="block text-sm font-medium text-gray-700 mb-2">
                            Carrying Capacity (K): <span id="carryingCapacityValue" class="font-bold text-green-600">1000</span>
                        </label>
                        <input type="range" id="carryingCapacity" min="500" max="2000" step="50" value="1000" 
                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>500</span>
                            <span>2000</span>
                        </div>
                    </div>
                    
                    <div>
                        <label for="harvestRate" class="block text-sm font-medium text-gray-700 mb-2">
                            Harvest Rate (H): <span id="harvestRateValue" class="font-bold text-orange-600">25</span>
                        </label>
                        <input type="range" id="harvestRate" min="0" max="100" step="1" value="25" 
                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>0</span>
                            <span>100</span>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-700">Initial Population (P₀):</span>
                        <span class="font-bold text-purple-600">50 individuals</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mt-1">
                        <span class="text-gray-700">Maximum Sustainable Harvest:</span>
                        <span id="maxHarvest" class="font-bold text-red-600">25.0</span>
                    </div>
                </div>
            </div>

            <div class="chart-container">
                <canvas id="populationChart"></canvas>
            </div>
        </section>

        <!-- Economic Models -->
        <section id="economics" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-chart-line text-blue-600 mr-3"></i>Economic Models
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-dollar-sign mr-2"></i>Economic Growth and Decay</h3>
                <p class="mb-4">Economic systems often exhibit exponential or logistic behavior in investment, savings, and debt models.</p>
                
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Compound Interest</h4>
                        <p class="text-sm mb-2">$\frac{dA}{dt} = rA$</p>
                        <p class="text-sm">Solution: $A(t) = A_0 e^{rt}$</p>
                        <p class="text-xs mt-1">Continuous compounding</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Savings with Deposits</h4>
                        <p class="text-sm mb-2">$\frac{dS}{dt} = rS + D$</p>
                        <p class="text-sm">Regular contributions</p>
                        <p class="text-xs mt-1">Linear first-order ODE</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Debt Repayment</h4>
                        <p class="text-sm mb-2">$\frac{dD}{dt} = rD - P$</p>
                        <p class="text-sm">Interest vs payments</p>
                        <p class="text-xs mt-1">Amortization analysis</p>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 3: Loan Amortization Model</h3>
                <p class="mb-3">A loan of $100,000 at 5% annual interest requires monthly payments. Model the debt balance over time.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Debt Balance Equation:</h4>
                    <p class="text-center">$$\frac{dD}{dt} = rD - P$$</p>
                    <p class="mt-2">Where $D$ is debt balance, $r = 0.05$ is annual rate, $P$ is monthly payment rate</p>
                    
                    <p class="mt-3"><strong>This is linear first-order:</strong></p>
                    <p class="text-center">$$\frac{dD}{dt} - rD = -P$$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Solution:</h4>
                    <p>Using integrating factor $\mu(t) = e^{-rt}$:</p>
                    <p class="text-center mt-2">$$D(t) = \left(D_0 - \frac{P}{r}\right)e^{rt} + \frac{P}{r}$$</p>
                    <p class="mt-2"><strong>Key Insights:</strong></p>
                    <ul class="list-disc list-inside space-y-1 text-sm">
                        <li>If $P > rD_0$: Debt decreases to zero</li>
                        <li>If $P = rD_0$: Interest-only payments</li>
                        <li>If $P < rD_0$: Debt grows exponentially</li>
                        <li>Time to payoff: $t_{payoff} = \frac{1}{r}\ln\left(\frac{P}{P-rD_0}\right)$</li>
                    </ul>
                </div>
            </div>

            <!-- Interactive Economic Visualization -->
            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-chart-line text-blue-600 mr-2"></i>Economic Price Dynamics Visualization
                </h3>
                <p class="text-gray-600 mb-4">
                    This chart shows how prices adjust over time based on supply and demand imbalances.
                </p>
                <div class="chart-container">
                    <canvas id="economicPriceChart"></canvas>
                </div>
            </div>

            <!-- Economic Growth Model Visualization -->
            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-chart-area text-green-600 mr-2"></i>Solow Growth Model Visualization
                </h3>
                <p class="text-gray-600 mb-4">
                    This chart demonstrates capital accumulation dynamics in the Solow growth model.
                </p>
                <div class="chart-container">
                    <canvas id="economicGrowthChart"></canvas>
                </div>
            </div>

            <!-- Economic Investment Model Visualization -->
            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-chart-line text-purple-600 mr-2"></i>Investment Growth Model
                </h3>
                <p class="text-gray-600 mb-4">
                    This chart shows how different investment strategies affect account balance over time.
                </p>
                <div class="chart-container">
                    <canvas id="economicInvestmentChart"></canvas>
                </div>
            </div>

            <!-- Economic Code Examples -->
            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Python: Economic Models
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp

# Economic Model: Compound Interest with Regular Deposits
def compound_interest_model(t, y, r, D):
    """
    dy/dt = ry + D
    y = account balance
    r = interest rate
    D = regular deposit rate
    """
    return [r * y[0] + D]

# Parameters
r = 0.05  # 5% annual interest rate
D = 1200  # $1200 annual deposits
A0 = 1000  # Initial balance $1000

# Time span (years)
t_span = (0, 30)
t_eval = np.linspace(0, 30, 300)

# Solve ODE
sol = solve_ivp(compound_interest_model, t_span, [A0],
                t_eval=t_eval, args=(r, D))

# Analytical solution for comparison
t = t_eval
A_analytical = (A0 + D/r) * np.exp(r * t) - D/r

# Plot results
plt.figure(figsize=(12, 8))

# Account balance over time
plt.subplot(2, 2, 1)
plt.plot(sol.t, sol.y[0], 'b-', linewidth=2, label='Numerical')
plt.plot(t, A_analytical, 'r--', linewidth=2, label='Analytical')
plt.xlabel('Time (years)')
plt.ylabel('Account Balance ($)')
plt.title('Compound Interest with Regular Deposits')
plt.legend()
plt.grid(True, alpha=0.3)

# Debt repayment model
def debt_model(t, y, r, P):
    """
    dy/dt = ry - P
    y = debt balance
    r = interest rate
    P = payment rate
    """
    return [r * y[0] - P]

# Debt parameters
r_debt = 0.06  # 6% annual interest
P = 12000  # $12,000 annual payments
D0 = 100000  # Initial debt $100,000

# Solve debt model
sol_debt = solve_ivp(debt_model, t_span, [D0],
                     t_eval=t_eval, args=(r_debt, P))

# Analytical solution for debt
D_analytical = (D0 - P/r_debt) * np.exp(r_debt * t) + P/r_debt

plt.subplot(2, 2, 2)
plt.plot(sol_debt.t, sol_debt.y[0], 'b-', linewidth=2, label='Debt Balance')
plt.axhline(y=0, color='g', linestyle='--', alpha=0.7, label='Paid Off')
plt.xlabel('Time (years)')
plt.ylabel('Debt Balance ($)')
plt.title('Loan Repayment Model')
plt.legend()
plt.grid(True, alpha=0.3)

# Price adjustment model
def price_adjustment(t, y, k, p_eq):
    """
    dp/dt = k(p_eq - p)
    p = current price
    p_eq = equilibrium price
    k = adjustment speed
    """
    return [k * (p_eq - y[0])]

# Price parameters
k = 0.5  # Adjustment speed
p_eq = 100  # Equilibrium price
p0 = 80  # Initial price

# Solve price model
sol_price = solve_ivp(price_adjustment, (0, 10), [p0],
                      t_eval=np.linspace(0, 10, 100),
                      args=(k, p_eq))

plt.subplot(2, 2, 3)
plt.plot(sol_price.t, sol_price.y[0], 'b-', linewidth=2, label='Price')
plt.axhline(y=p_eq, color='r', linestyle='--', alpha=0.7,
            label='Equilibrium Price')
plt.xlabel('Time')
plt.ylabel('Price ($)')
plt.title('Price Adjustment to Equilibrium')
plt.legend()
plt.grid(True, alpha=0.3)

# Economic growth rate analysis
growth_rates = np.linspace(0.01, 0.08, 50)
final_values = []

for rate in growth_rates:
    final_val = (A0 + D/rate) * np.exp(rate * 30) - D/rate
    final_values.append(final_val)

plt.subplot(2, 2, 4)
plt.plot(growth_rates * 100, np.array(final_values) / 1000, 'b-', linewidth=2)
plt.xlabel('Interest Rate (%)')
plt.ylabel('Final Balance ($1000s)')
plt.title('Sensitivity to Interest Rate (30 years)')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Calculate key financial metrics
print("Financial Analysis Results:")
print(f"Final balance after 30 years: ${A_analytical[-1]:,.2f}")
print(f"Total deposits: ${D * 30:,.2f}")
print(f"Interest earned: ${A_analytical[-1] - A0 - D * 30:,.2f}")

# Debt payoff time
if P > r_debt * D0:
    payoff_time = np.log(P / (P - r_debt * D0)) / r_debt
    print(f"Debt payoff time: {payoff_time:.1f} years")
else:
    print("Payments insufficient to pay off debt")</code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-blue-800 mr-2"></i>R: Economic Models
                    </h3>
                    <div class="code-block r-code">
                        <pre><code>library(deSolve)
library(ggplot2)
library(dplyr)
library(gridExtra)

# Economic Model Functions
economic_models <- list(

  # Compound interest with deposits
  compound_interest = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      A <- state[1]  # Account balance
      dAdt <- r * A + D  # Interest + deposits
      return(list(dAdt))
    })
  },

  # Debt repayment model
  debt_repayment = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      D <- state[1]  # Debt balance
      dDdt <- r * D - P  # Interest - payments
      return(list(dDdt))
    })
  },

  # Price adjustment model
  price_adjustment = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      p <- state[1]  # Current price
      dpdt <- k * (p_eq - p)  # Adjustment to equilibrium
      return(list(dpdt))
    })
  }
)

# Parameters for compound interest
compound_parms <- list(
  r = 0.05,    # 5% annual interest
  D = 1200     # $1200 annual deposits
)

# Solve compound interest model
times <- seq(0, 30, by = 0.1)
compound_sol <- ode(y = c(A = 1000), times = times,
                    func = economic_models$compound_interest,
                    parms = compound_parms)

# Convert to data frame
compound_df <- as.data.frame(compound_sol)

# Analytical solution for comparison
compound_df$A_analytical <- with(compound_parms,
  (1000 + D/r) * exp(r * compound_df$time) - D/r)

# Plot compound interest
p1 <- ggplot(compound_df, aes(x = time)) +
  geom_line(aes(y = A, color = "Numerical"), size = 1.2) +
  geom_line(aes(y = A_analytical, color = "Analytical"),
            linetype = "dashed", size = 1.2) +
  labs(title = "Compound Interest with Regular Deposits",
       x = "Time (years)",
       y = "Account Balance ($)",
       color = "Solution") +
  theme_minimal() +
  scale_y_continuous(labels = scales::dollar_format())

# Debt repayment model
debt_parms <- list(
  r = 0.06,    # 6% annual interest
  P = 12000    # $12,000 annual payments
)

debt_sol <- ode(y = c(D = 100000), times = times,
                func = economic_models$debt_repayment,
                parms = debt_parms)

debt_df <- as.data.frame(debt_sol)

# Plot debt repayment
p2 <- ggplot(debt_df, aes(x = time, y = D)) +
  geom_line(color = "red", size = 1.2) +
  geom_hline(yintercept = 0, linetype = "dashed",
             color = "green", alpha = 0.7) +
  labs(title = "Loan Repayment Model",
       x = "Time (years)",
       y = "Debt Balance ($)") +
  theme_minimal() +
  scale_y_continuous(labels = scales::dollar_format())

# Price adjustment model
price_parms <- list(
  k = 0.5,     # Adjustment speed
  p_eq = 100   # Equilibrium price
)

price_times <- seq(0, 10, by = 0.1)
price_sol <- ode(y = c(p = 80), times = price_times,
                 func = economic_models$price_adjustment,
                 parms = price_parms)

price_df <- as.data.frame(price_sol)

# Plot price adjustment
p3 <- ggplot(price_df, aes(x = time, y = p)) +
  geom_line(color = "blue", size = 1.2) +
  geom_hline(yintercept = price_parms$p_eq,
             linetype = "dashed", color = "red", alpha = 0.7) +
  labs(title = "Price Adjustment to Equilibrium",
       x = "Time",
       y = "Price ($)") +
  theme_minimal() +
  scale_y_continuous(labels = scales::dollar_format())

# Sensitivity analysis
sensitivity_analysis <- function() {
  interest_rates <- seq(0.01, 0.08, by = 0.001)
  final_values <- numeric(length(interest_rates))

  for (i in seq_along(interest_rates)) {
    r <- interest_rates[i]
    D <- 1200
    A0 <- 1000
    t_final <- 30

    final_val <- (A0 + D/r) * exp(r * t_final) - D/r
    final_values[i] <- final_val
  }

  sensitivity_df <- data.frame(
    interest_rate = interest_rates * 100,
    final_balance = final_values / 1000
  )

  p4 <- ggplot(sensitivity_df, aes(x = interest_rate, y = final_balance)) +
    geom_line(color = "purple", size = 1.2) +
    labs(title = "Sensitivity to Interest Rate (30 years)",
         x = "Interest Rate (%)",
         y = "Final Balance ($1000s)") +
    theme_minimal()

  return(p4)
}

p4 <- sensitivity_analysis()

# Combine all plots
grid.arrange(p1, p2, p3, p4, ncol = 2)

# Calculate financial metrics
final_balance <- tail(compound_df$A_analytical, 1)
total_deposits <- compound_parms$D * 30
interest_earned <- final_balance - 1000 - total_deposits

cat("Financial Analysis Results:\n")
cat(sprintf("Final balance after 30 years: $%.2f\n", final_balance))
cat(sprintf("Total deposits: $%.2f\n", total_deposits))
cat(sprintf("Interest earned: $%.2f\n", interest_earned))

# Debt payoff calculation
with(debt_parms, {
  if (P > r * 100000) {
    payoff_time <- log(P / (P - r * 100000)) / r
    cat(sprintf("Debt payoff time: %.1f years\n", payoff_time))
  } else {
    cat("Payments insufficient to pay off debt\n")
  }
})</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Physics Applications -->
        <section id="physics" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-atom text-purple-600 mr-3"></i>Physics Applications
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-thermometer-half mr-2"></i>Newton's Law of Cooling</h3>
                <p class="mb-4">Temperature changes in objects follow Newton's law of cooling, a fundamental first-order ODE.</p>
                
                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Newton's Law</h4>
                        <p class="text-sm mb-2">$\frac{dT}{dt} = -k(T - T_{env})$</p>
                        <p class="text-sm">Rate ∝ temperature difference</p>
                        <p class="text-xs mt-1">Separable equation</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Solution</h4>
                        <p class="text-sm mb-2">$T(t) = T_{env} + (T_0 - T_{env})e^{-kt}$</p>
                        <p class="text-sm">Exponential approach</p>
                        <p class="text-xs mt-1">To environmental temperature</p>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 4: Coffee Cooling Problem</h3>
                <p class="mb-3">A cup of coffee at 90°C is placed in a 20°C room. After 5 minutes, it cools to 70°C. When will it reach 30°C?</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Setup:</h4>
                    <p>$\frac{dT}{dt} = -k(T - 20)$, with $T(0) = 90$, $T(5) = 70$</p>
                    
                    <p class="mt-3"><strong>Solution:</strong></p>
                    <p>$T(t) = 20 + 70e^{-kt}$</p>
                    <p>From $T(5) = 70$: $70 = 20 + 70e^{-5k}$</p>
                    <p>Solving: $k = \frac{1}{5}\ln\left(\frac{7}{5}\right) \approx 0.0671$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Finding when T = 30°C:</h4>
                    <p>$30 = 20 + 70e^{-0.0671t}$</p>
                    <p>$10 = 70e^{-0.0671t}$</p>
                    <p>$t = \frac{\ln(7)}{0.0671} \approx 29.0$ minutes</p>
                </div>
            </div>

            <!-- Interactive Physics Visualization -->
            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-radiation text-red-600 mr-2"></i>Radioactive Decay Visualization
                </h3>
                <p class="text-gray-600 mb-4">
                    This chart shows the exponential decay of different radioactive isotopes over time.
                </p>
                <div class="chart-container">
                    <canvas id="physicsDecayChart"></canvas>
                </div>
            </div>

            <!-- Circuit Response Visualization -->
            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-microchip text-blue-600 mr-2"></i>RL Circuit Response
                </h3>
                <p class="text-gray-600 mb-4">
                    This chart shows the current response in RL circuits to step voltage inputs.
                </p>
                <div class="chart-container">
                    <canvas id="physicsCircuitChart"></canvas>
                </div>
            </div>

            <!-- Newton's Cooling Law Visualization -->
            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-thermometer-half text-orange-600 mr-2"></i>Newton's Cooling Law Visualization
                </h3>
                <p class="text-gray-600 mb-4">
                    This chart demonstrates how objects cool down according to Newton's law of cooling.
                </p>
                <div class="chart-container">
                    <canvas id="physicsCoolingChart"></canvas>
                </div>
            </div>

            <!-- Physics Code Examples -->
            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Python: Physics Applications
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp
from scipy.optimize import curve_fit

# Physics Model: Newton's Law of Cooling
def newtons_cooling(t, y, k, T_env):
    """
    dT/dt = -k(T - T_env)
    T = temperature
    k = cooling constant
    T_env = environmental temperature
    """
    return [-k * (y[0] - T_env)]

# Radioactive decay model
def radioactive_decay(t, y, lam):
    """
    dN/dt = -λN
    N = number of atoms
    λ = decay constant
    """
    return [-lam * y[0]]

# RC Circuit model
def rc_circuit(t, y, R, C, V_in):
    """
    dV_C/dt = (V_in - V_C)/(RC)
    V_C = capacitor voltage
    R = resistance
    C = capacitance
    V_in = input voltage
    """
    return [(V_in - y[0]) / (R * C)]

# Terminal velocity model
def terminal_velocity(t, y, g, b, m):
    """
    dv/dt = g - (b/m)v
    v = velocity
    g = gravitational acceleration
    b = drag coefficient
    m = mass
    """
    return [g - (b/m) * y[0]]

# Newton's Cooling Example
print("=== Newton's Cooling Law ===")
k = 0.1  # Cooling constant (1/min)
T_env = 20  # Room temperature (°C)
T0 = 90  # Initial temperature (°C)

t_span = (0, 60)  # 60 minutes
t_eval = np.linspace(0, 60, 300)

# Solve cooling equation
sol_cooling = solve_ivp(newtons_cooling, t_span, [T0],
                        t_eval=t_eval, args=(k, T_env))

# Analytical solution
T_analytical = T_env + (T0 - T_env) * np.exp(-k * t_eval)

# Plot cooling results
plt.figure(figsize=(15, 10))

plt.subplot(2, 3, 1)
plt.plot(sol_cooling.t, sol_cooling.y[0], 'b-', linewidth=2, label='Numerical')
plt.plot(t_eval, T_analytical, 'r--', linewidth=2, label='Analytical')
plt.axhline(y=T_env, color='g', linestyle=':', alpha=0.7, label='Room Temp')
plt.xlabel('Time (min)')
plt.ylabel('Temperature (°C)')
plt.title('Newton\'s Law of Cooling')
plt.legend()
plt.grid(True, alpha=0.3)

# Radioactive Decay Example
print("\n=== Radioactive Decay ===")
# Half-lives for different isotopes (years)
isotopes = {
    'C-14': 5730,
    'U-238': 4.468e9,
    'Ra-226': 1600,
    'I-131': 8.02/365.25  # Convert days to years
}

plt.subplot(2, 3, 2)
t_decay = np.linspace(0, 50, 1000)  # 50 years

for isotope, half_life in isotopes.items():
    if half_life < 100:  # Only plot short-lived isotopes
        lam = np.log(2) / half_life  # Decay constant
        N0 = 1000  # Initial number of atoms

        # Analytical solution
        N_t = N0 * np.exp(-lam * t_decay)

        plt.plot(t_decay, N_t, linewidth=2, label=f'{isotope} (t₁/₂={half_life:.1f}y)')

plt.xlabel('Time (years)')
plt.ylabel('Number of Atoms')
plt.title('Radioactive Decay')
plt.legend()
plt.grid(True, alpha=0.3)
plt.yscale('log')

# RC Circuit Example
print("\n=== RC Circuit ===")
R = 1000  # Resistance (Ω)
C = 1e-6  # Capacitance (F)
V_in = 5  # Input voltage (V)
V0 = 0   # Initial capacitor voltage

t_circuit = np.linspace(0, 0.01, 1000)  # 10 ms

# Solve RC circuit
sol_rc = solve_ivp(rc_circuit, (0, 0.01), [V0],
                   t_eval=t_circuit, args=(R, C, V_in))

# Analytical solution
tau = R * C  # Time constant
V_analytical = V_in * (1 - np.exp(-t_circuit / tau))

plt.subplot(2, 3, 3)
plt.plot(sol_rc.t * 1000, sol_rc.y[0], 'b-', linewidth=2, label='Numerical')
plt.plot(t_circuit * 1000, V_analytical, 'r--', linewidth=2, label='Analytical')
plt.axhline(y=V_in, color='g', linestyle=':', alpha=0.7, label='Input Voltage')
plt.axhline(y=0.632*V_in, color='orange', linestyle=':', alpha=0.7, label='63.2% of V_in')
plt.xlabel('Time (ms)')
plt.ylabel('Capacitor Voltage (V)')
plt.title('RC Circuit Charging')
plt.legend()
plt.grid(True, alpha=0.3)

# Terminal Velocity Example
print("\n=== Terminal Velocity ===")
g = 9.81  # Gravitational acceleration (m/s²)
m = 0.1   # Mass (kg)
b = 0.01  # Drag coefficient (kg/s)

t_terminal = np.linspace(0, 10, 1000)

# Solve terminal velocity
sol_terminal = solve_ivp(terminal_velocity, (0, 10), [0],
                         t_eval=t_terminal, args=(g, b, m))

# Analytical solution
v_terminal = m * g / b  # Terminal velocity
v_analytical = v_terminal * (1 - np.exp(-b * t_terminal / m))

plt.subplot(2, 3, 4)
plt.plot(sol_terminal.t, sol_terminal.y[0], 'b-', linewidth=2, label='Numerical')
plt.plot(t_terminal, v_analytical, 'r--', linewidth=2, label='Analytical')
plt.axhline(y=v_terminal, color='g', linestyle=':', alpha=0.7,
            label=f'Terminal Velocity ({v_terminal:.1f} m/s)')
plt.xlabel('Time (s)')
plt.ylabel('Velocity (m/s)')
plt.title('Terminal Velocity')
plt.legend()
plt.grid(True, alpha=0.3)

# Parameter estimation from experimental data
print("\n=== Parameter Estimation ===")
# Simulate experimental cooling data with noise
t_exp = np.array([0, 5, 10, 15, 20, 30, 40, 50])
T_true = T_env + (T0 - T_env) * np.exp(-k * t_exp)
T_exp = T_true + np.random.normal(0, 1, len(t_exp))  # Add noise

# Define fitting function
def cooling_model(t, k_fit):
    return T_env + (T0 - T_env) * np.exp(-k_fit * t)

# Fit the model
popt, pcov = curve_fit(cooling_model, t_exp, T_exp, p0=[0.1])
k_fitted = popt[0]
k_error = np.sqrt(pcov[0, 0])

print(f"True cooling constant: {k:.3f} min⁻¹")
print(f"Fitted cooling constant: {k_fitted:.3f} ± {k_error:.3f} min⁻¹")

plt.subplot(2, 3, 5)
plt.plot(t_eval, T_analytical, 'b-', linewidth=2, label='True Model')
plt.plot(t_eval, cooling_model(t_eval, k_fitted), 'r--', linewidth=2,
         label='Fitted Model')
plt.scatter(t_exp, T_exp, color='red', s=50, zorder=5, label='Experimental Data')
plt.xlabel('Time (min)')
plt.ylabel('Temperature (°C)')
plt.title('Parameter Estimation')
plt.legend()
plt.grid(True, alpha=0.3)

# Phase portrait for damped motion
def damped_oscillator(t, y, gamma, omega0):
    """
    Second-order ODE as system of first-order ODEs
    d²x/dt² + 2γ(dx/dt) + ω₀²x = 0
    """
    x, v = y
    return [v, -2*gamma*v - omega0**2*x]

gamma = 0.5  # Damping coefficient
omega0 = 2   # Natural frequency

# Different initial conditions
initial_conditions = [[1, 0], [0, 2], [-1, 1], [1, -1]]
colors = ['blue', 'red', 'green', 'orange']

plt.subplot(2, 3, 6)
for i, (x0, v0) in enumerate(initial_conditions):
    sol_damped = solve_ivp(damped_oscillator, (0, 10), [x0, v0],
                           t_eval=np.linspace(0, 10, 1000),
                           args=(gamma, omega0))
    plt.plot(sol_damped.y[0], sol_damped.y[1], color=colors[i],
             linewidth=2, label=f'IC: ({x0}, {v0})')

plt.xlabel('Position (x)')
plt.ylabel('Velocity (dx/dt)')
plt.title('Phase Portrait: Damped Oscillator')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Summary of key physics constants
print("\n=== Physics Constants Summary ===")
print(f"RC time constant: τ = RC = {tau*1000:.2f} ms")
print(f"Terminal velocity: v_t = mg/b = {v_terminal:.2f} m/s")
print(f"Cooling time constant: τ = 1/k = {1/k:.1f} min")
print(f"Half-life relationship: t₁/₂ = ln(2)/λ")</code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-blue-800 mr-2"></i>R: Physics Applications
                    </h3>
                    <div class="code-block r-code">
                        <pre><code>library(deSolve)
library(ggplot2)
library(dplyr)
library(gridExtra)

# Physics Model Functions
physics_models <- list(

  # Newton's law of cooling
  newtons_cooling = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      T <- state[1]
      dTdt <- -k * (T - T_env)
      return(list(dTdt))
    })
  },

  # Radioactive decay
  radioactive_decay = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      N <- state[1]
      dNdt <- -lambda * N
      return(list(dNdt))
    })
  },

  # RC circuit
  rc_circuit = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      V_C <- state[1]
      dV_Cdt <- (V_in - V_C) / (R * C)
      return(list(dV_Cdt))
    })
  },

  # Terminal velocity
  terminal_velocity = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      v <- state[1]
      dvdt <- g - (b / m) * v
      return(list(dvdt))
    })
  }
)

# Newton's Cooling Analysis
cat("=== Newton's Cooling Law ===\n")
cooling_parms <- list(
  k = 0.1,      # Cooling constant (1/min)
  T_env = 20    # Environmental temperature (°C)
)

times <- seq(0, 60, by = 0.2)
cooling_sol <- ode(y = c(T = 90), times = times,
                   func = physics_models$newtons_cooling,
                   parms = cooling_parms)

cooling_df <- as.data.frame(cooling_sol)

# Analytical solution
cooling_df$T_analytical <- with(cooling_parms,
  T_env + (90 - T_env) * exp(-k * cooling_df$time))

# Plot cooling
p1 <- ggplot(cooling_df, aes(x = time)) +
  geom_line(aes(y = T, color = "Numerical"), size = 1.2) +
  geom_line(aes(y = T_analytical, color = "Analytical"),
            linetype = "dashed", size = 1.2) +
  geom_hline(yintercept = cooling_parms$T_env,
             linetype = "dotted", color = "green", alpha = 0.7) +
  labs(title = "Newton's Law of Cooling",
       x = "Time (min)",
       y = "Temperature (°C)",
       color = "Solution") +
  theme_minimal()

# Radioactive Decay Analysis
cat("\n=== Radioactive Decay ===\n")
isotopes <- data.frame(
  name = c("C-14", "Ra-226", "I-131"),
  half_life = c(5730, 1600, 8.02/365.25),  # years
  stringsAsFactors = FALSE
)

decay_results <- list()
times_decay <- seq(0, 50, by = 0.1)

for (i in 1:nrow(isotopes)) {
  isotope <- isotopes[i, ]
  if (isotope$half_life < 100) {  # Only short-lived isotopes
    decay_parms <- list(
      lambda = log(2) / isotope$half_life
    )

    decay_sol <- ode(y = c(N = 1000), times = times_decay,
                     func = physics_models$radioactive_decay,
                     parms = decay_parms)

    decay_df <- as.data.frame(decay_sol)
    decay_df$isotope <- isotope$name
    decay_df$half_life <- isotope$half_life

    decay_results[[i]] <- decay_df
  }
}

decay_combined <- do.call(rbind, decay_results)

p2 <- ggplot(decay_combined, aes(x = time, y = N, color = isotope)) +
  geom_line(size = 1.2) +
  scale_y_log10() +
  labs(title = "Radioactive Decay",
       x = "Time (years)",
       y = "Number of Atoms (log scale)",
       color = "Isotope") +
  theme_minimal()

# RC Circuit Analysis
cat("\n=== RC Circuit ===\n")
rc_parms <- list(
  R = 1000,     # Resistance (Ω)
  C = 1e-6,     # Capacitance (F)
  V_in = 5      # Input voltage (V)
)

times_rc <- seq(0, 0.01, by = 0.00001)  # 10 ms
rc_sol <- ode(y = c(V_C = 0), times = times_rc,
              func = physics_models$rc_circuit,
              parms = rc_parms)

rc_df <- as.data.frame(rc_sol)
rc_df$time_ms <- rc_df$time * 1000  # Convert to ms

# Analytical solution
tau <- rc_parms$R * rc_parms$C
rc_df$V_C_analytical <- with(rc_parms, V_in * (1 - exp(-rc_df$time / tau)))

p3 <- ggplot(rc_df, aes(x = time_ms)) +
  geom_line(aes(y = V_C, color = "Numerical"), size = 1.2) +
  geom_line(aes(y = V_C_analytical, color = "Analytical"),
            linetype = "dashed", size = 1.2) +
  geom_hline(yintercept = rc_parms$V_in,
             linetype = "dotted", color = "green", alpha = 0.7) +
  geom_hline(yintercept = 0.632 * rc_parms$V_in,
             linetype = "dotted", color = "orange", alpha = 0.7) +
  labs(title = "RC Circuit Charging",
       x = "Time (ms)",
       y = "Capacitor Voltage (V)",
       color = "Solution") +
  theme_minimal()

# Terminal Velocity Analysis
cat("\n=== Terminal Velocity ===\n")
terminal_parms <- list(
  g = 9.81,     # Gravitational acceleration (m/s²)
  m = 0.1,      # Mass (kg)
  b = 0.01      # Drag coefficient (kg/s)
)

times_terminal <- seq(0, 10, by = 0.01)
terminal_sol <- ode(y = c(v = 0), times = times_terminal,
                    func = physics_models$terminal_velocity,
                    parms = terminal_parms)

terminal_df <- as.data.frame(terminal_sol)

# Analytical solution
v_terminal <- with(terminal_parms, m * g / b)
terminal_df$v_analytical <- with(terminal_parms,
  v_terminal * (1 - exp(-b * terminal_df$time / m)))

p4 <- ggplot(terminal_df, aes(x = time)) +
  geom_line(aes(y = v, color = "Numerical"), size = 1.2) +
  geom_line(aes(y = v_analytical, color = "Analytical"),
            linetype = "dashed", size = 1.2) +
  geom_hline(yintercept = v_terminal,
             linetype = "dotted", color = "green", alpha = 0.7) +
  labs(title = "Terminal Velocity",
       x = "Time (s)",
       y = "Velocity (m/s)",
       color = "Solution") +
  theme_minimal()

# Combine all plots
grid.arrange(p1, p2, p3, p4, ncol = 2)

# Parameter estimation example
cat("\n=== Parameter Estimation ===\n")
set.seed(123)
t_exp <- c(0, 5, 10, 15, 20, 30, 40, 50)
T_true <- with(cooling_parms, T_env + (90 - T_env) * exp(-k * t_exp))
T_exp <- T_true + rnorm(length(t_exp), 0, 1)  # Add noise

# Fit cooling model
cooling_model <- function(t, k_fit) {
  cooling_parms$T_env + (90 - cooling_parms$T_env) * exp(-k_fit * t)
}

fit_result <- nls(T_exp ~ cooling_model(t_exp, k_fit),
                  start = list(k_fit = 0.1))

k_fitted <- coef(fit_result)
k_error <- summary(fit_result)$coefficients[1, 2]

cat(sprintf("True cooling constant: %.3f min⁻¹\n", cooling_parms$k))
cat(sprintf("Fitted cooling constant: %.3f ± %.3f min⁻¹\n", k_fitted, k_error))

# Summary of physics constants
cat("\n=== Physics Constants Summary ===\n")
cat(sprintf("RC time constant: τ = RC = %.2f ms\n", tau * 1000))
cat(sprintf("Terminal velocity: v_t = mg/b = %.2f m/s\n", v_terminal))
cat(sprintf("Cooling time constant: τ = 1/k = %.1f min\n", 1/cooling_parms$k))
cat("Half-life relationship: t₁/₂ = ln(2)/λ\n")</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Clinical Pharmacology -->
        <section id="pharmacology" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-pills text-green-600 mr-3"></i>Clinical Pharmacological Applications
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-heartbeat mr-2"></i>Pharmacokinetic Modeling with First-Order ODEs</h3>
                <p class="mb-4">Pharmacokinetics describes how drugs move through the body using ADME processes: Absorption, Distribution, Metabolism, and Elimination. These processes often follow first-order kinetics.</p>

                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">First-Order Elimination</h4>
                        <p class="text-sm mb-2">$\frac{dC}{dt} = -k_e C$</p>
                        <p class="text-xs">Rate proportional to concentration</p>
                        <p class="text-xs mt-1">Half-life: $t_{1/2} = \frac{\ln(2)}{k_e}$</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">One-Compartment Model</h4>
                        <p class="text-sm mb-2">$\frac{dA}{dt} = -k_e A$</p>
                        <p class="text-xs">A = amount in body</p>
                        <p class="text-xs mt-1">$C(t) = C_0 e^{-k_e t}$</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Clearance Concept</h4>
                        <p class="text-sm mb-2">$CL = k_e \cdot V_d$</p>
                        <p class="text-xs">Volume cleared per unit time</p>
                        <p class="text-xs mt-1">$k_e = \frac{CL}{V_d}$</p>
                    </div>
                </div>
            </div>

            <div class="theorem-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-flask mr-2"></i>Mathematical Framework for Pharmacokinetics</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-3">Single-Compartment Model</h4>
                        <div class="bg-white bg-opacity-20 p-4 rounded mb-3">
                            <p class="text-sm mb-2"><strong>Mass Balance Equation:</strong></p>
                            <p class="text-center">$$V_d \frac{dC}{dt} = \text{Input Rate} - CL \cdot C$$</p>
                            <p class="text-sm mt-2">Where:</p>
                            <ul class="text-xs space-y-1">
                                <li>$V_d$ = Volume of distribution</li>
                                <li>$C$ = Plasma concentration</li>
                                <li>$CL$ = Total body clearance</li>
                            </ul>
                        </div>

                        <div class="bg-white bg-opacity-20 p-4 rounded">
                            <p class="text-sm mb-2"><strong>IV Bolus Solution:</strong></p>
                            <p class="text-center">$$C(t) = \frac{D}{V_d} e^{-k_e t}$$</p>
                            <p class="text-sm mt-2">Where $k_e = \frac{CL}{V_d}$ and $D$ = dose</p>
                        </div>
                    </div>

                    <div>
                        <h4 class="font-bold mb-3">First-Order Absorption</h4>
                        <div class="bg-white bg-opacity-20 p-4 rounded mb-3">
                            <p class="text-sm mb-2"><strong>Absorption + Elimination:</strong></p>
                            <p class="text-center">$$\frac{dC}{dt} = \frac{k_a F D}{V_d} e^{-k_a t} - k_e C$$</p>
                            <p class="text-sm mt-2">Where:</p>
                            <ul class="text-xs space-y-1">
                                <li>$k_a$ = Absorption rate constant</li>
                                <li>$F$ = Bioavailability fraction</li>
                                <li>$D$ = Oral dose</li>
                            </ul>
                        </div>

                        <div class="bg-white bg-opacity-20 p-4 rounded">
                            <p class="text-sm mb-2"><strong>Analytical Solution:</strong></p>
                            <p class="text-center">$$C(t) = \frac{k_a F D}{V_d(k_a - k_e)} (e^{-k_e t} - e^{-k_a t})$$</p>
                            <p class="text-sm mt-2">Peak time: $t_{max} = \frac{\ln(k_a/k_e)}{k_a - k_e}$</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Clinical Example: Digoxin Pharmacokinetics</h3>
                <p class="mb-3">Digoxin is a cardiac glycoside with well-characterized first-order elimination kinetics. Model a 0.25 mg IV dose in a 70 kg patient.</p>

                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Patient Parameters:</h4>
                    <div class="grid md:grid-cols-2 gap-4">
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Weight: 70 kg</li>
                            <li>Dose: 0.25 mg IV</li>
                            <li>Volume of distribution: 7 L/kg</li>
                            <li>Clearance: 1.2 mL/min/kg</li>
                        </ul>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>$V_d = 7 \times 70 = 490$ L</li>
                            <li>$CL = 1.2 \times 70 = 84$ mL/min = 5.04 L/h</li>
                            <li>$k_e = \frac{5.04}{490} = 0.0103$ h⁻¹</li>
                            <li>$t_{1/2} = \frac{0.693}{0.0103} = 67.3$ hours</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Concentration-Time Profile:</h4>
                    <p class="text-center">$$C(t) = \frac{0.25 \text{ mg}}{490 \text{ L}} e^{-0.0103t} = 0.51 \times e^{-0.0103t} \text{ ng/mL}$$</p>
                    <p class="text-sm mt-2"><strong>Clinical Targets:</strong></p>
                    <ul class="text-sm space-y-1">
                        <li>Therapeutic range: 1.0-2.0 ng/mL</li>
                        <li>Toxic level: >2.5 ng/mL</li>
                        <li>Time to steady state: ~5 × t₁/₂ = 337 hours (14 days)</li>
                    </ul>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-chart-line text-green-600 mr-2"></i>Pharmacokinetic Visualization Dashboard
                </h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="chart-container">
                        <canvas id="pharmacologyIVChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="pharmacologyOralChart"></canvas>
                    </div>
                </div>
                <div class="chart-container mt-6">
                    <canvas id="pharmacologyMultiDoseChart"></canvas>
                </div>
                <div class="mt-4 grid md:grid-cols-3 gap-4 text-sm">
                    <div class="bg-blue-50 p-3 rounded">
                        <strong>IV Bolus Model:</strong><br>
                        Immediate distribution<br>
                        First-order elimination<br>
                        Exponential decay
                    </div>
                    <div class="bg-green-50 p-3 rounded">
                        <strong>Oral Absorption:</strong><br>
                        Flip-flop kinetics possible<br>
                        Peak concentration time<br>
                        Bioavailability effects
                    </div>
                    <div class="bg-orange-50 p-3 rounded">
                        <strong>Multiple Dosing:</strong><br>
                        Accumulation to steady state<br>
                        Loading dose calculations<br>
                        Dosing interval optimization
                    </div>
                </div>
            </div>
        </section>

        <!-- Engineering Systems -->
        <section id="engineering" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-cogs text-orange-600 mr-3"></i>Engineering Systems
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-bolt mr-2"></i>RC Circuits</h3>
                <p class="mb-4">Resistor-Capacitor circuits exhibit first-order behavior in charging and discharging.</p>
                
                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Kirchhoff's Law</h4>
                        <p class="text-sm mb-2">$RC\frac{dV_C}{dt} + V_C = V_{in}$</p>
                        <p class="text-sm">First-order linear ODE</p>
                        <p class="text-xs mt-1">Time constant τ = RC</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Charging Response</h4>
                        <p class="text-sm mb-2">$V_C(t) = V_{in}(1 - e^{-t/RC})$</p>
                        <p class="text-sm">Exponential approach</p>
                        <p class="text-xs mt-1">To input voltage</p>
                    </div>
                </div>
            </div>

            <!-- Interactive Engineering Visualization -->
            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-step-forward text-blue-600 mr-2"></i>First-Order System Step Response
                </h3>
                <p class="text-gray-600 mb-4">
                    This chart shows how first-order systems respond to step inputs with different time constants.
                </p>
                <div class="chart-container">
                    <canvas id="engineeringStepChart"></canvas>
                </div>
            </div>

            <!-- Frequency Response Visualization -->
            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-wave-square text-purple-600 mr-2"></i>Frequency Response Analysis
                </h3>
                <p class="text-gray-600 mb-4">
                    This chart shows the frequency response of first-order systems.
                </p>
                <div class="chart-container">
                    <canvas id="engineeringFrequencyChart"></canvas>
                </div>
            </div>

            <!-- Control System Visualization -->
            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-cogs text-green-600 mr-2"></i>Control System Response
                </h3>
                <p class="text-gray-600 mb-4">
                    This chart demonstrates control system behavior with different controllers.
                </p>
                <div class="chart-container">
                    <canvas id="engineeringControlChart"></canvas>
                </div>
            </div>

            <!-- Heat Transfer Visualization -->
            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-fire text-red-600 mr-2"></i>Heat Transfer Dynamics
                </h3>
                <p class="text-gray-600 mb-4">
                    This chart demonstrates thermal response in engineering systems.
                </p>
                <div class="chart-container">
                    <canvas id="engineeringHeatChart"></canvas>
                </div>
            </div>

            <!-- Engineering Code Examples -->
            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Python: Engineering Systems
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp
from scipy import signal
import control as ct

# Engineering Model: First-Order System Response
def first_order_system(t, y, tau, K, u_func):
    """
    τ(dy/dt) + y = Ku(t)
    Standard first-order system
    """
    u = u_func(t)
    return [(K * u - y[0]) / tau]

# Heat transfer model
def heat_transfer(t, y, h, A, m, cp, T_amb, Q_in_func):
    """
    mc_p(dT/dt) = Q_in - hA(T - T_amb)
    Heat transfer with thermal mass
    """
    T = y[0]
    Q_in = Q_in_func(t)
    return [(Q_in - h * A * (T - T_amb)) / (m * cp)]

# Control system analysis
def control_system_response():
    """Analyze first-order control system responses"""

    # System parameters
    tau_values = [1, 2, 5]  # Different time constants
    K = 1  # Steady-state gain

    # Time vector
    t = np.linspace(0, 20, 1000)

    # Input functions
    def step_input(t):
        return 1.0 if t >= 0 else 0.0

    def ramp_input(t):
        return t if t >= 0 else 0.0

    def impulse_input(t):
        return 1.0 if abs(t) < 0.01 else 0.0

    # Plot responses
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    # Step responses
    for i, tau in enumerate(tau_values):
        # Solve ODE
        sol = solve_ivp(first_order_system, (0, 20), [0],
                        t_eval=t, args=(tau, K, step_input))

        # Analytical solution for step input
        y_analytical = K * (1 - np.exp(-t / tau))

        axes[0, 0].plot(sol.t, sol.y[0], linewidth=2,
                       label=f'τ = {tau}s (Numerical)')
        axes[0, 0].plot(t, y_analytical, '--', linewidth=2,
                       label=f'τ = {tau}s (Analytical)')

    axes[0, 0].set_title('Step Response')
    axes[0, 0].set_xlabel('Time (s)')
    axes[0, 0].set_ylabel('Output')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # Ramp responses
    for i, tau in enumerate(tau_values):
        sol = solve_ivp(first_order_system, (0, 20), [0],
                        t_eval=t, args=(tau, K, ramp_input))

        axes[0, 1].plot(sol.t, sol.y[0], linewidth=2, label=f'τ = {tau}s')

    axes[0, 1].plot(t, t, 'k--', alpha=0.5, label='Input (ramp)')
    axes[0, 1].set_title('Ramp Response')
    axes[0, 1].set_xlabel('Time (s)')
    axes[0, 1].set_ylabel('Output')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # Frequency response (Bode plot)
    frequencies = np.logspace(-2, 2, 1000)

    for tau in tau_values:
        # Transfer function: G(s) = K / (τs + 1)
        s = 1j * frequencies
        G = K / (tau * s + 1)

        magnitude_db = 20 * np.log10(np.abs(G))
        phase_deg = np.angle(G) * 180 / np.pi

        axes[0, 2].semilogx(frequencies, magnitude_db, linewidth=2,
                           label=f'τ = {tau}s')
        axes[1, 2].semilogx(frequencies, phase_deg, linewidth=2,
                           label=f'τ = {tau}s')

    axes[0, 2].set_title('Bode Plot - Magnitude')
    axes[0, 2].set_xlabel('Frequency (rad/s)')
    axes[0, 2].set_ylabel('Magnitude (dB)')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)

    axes[1, 2].set_title('Bode Plot - Phase')
    axes[1, 2].set_xlabel('Frequency (rad/s)')
    axes[1, 2].set_ylabel('Phase (degrees)')
    axes[1, 2].legend()
    axes[1, 2].grid(True, alpha=0.3)

    return fig

# Heat transfer analysis
def heat_transfer_analysis():
    """Analyze heat transfer in engineering systems"""

    # Parameters
    h = 10      # Heat transfer coefficient (W/m²K)
    A = 0.1     # Surface area (m²)
    m = 0.5     # Mass (kg)
    cp = 500    # Specific heat capacity (J/kgK)
    T_amb = 25  # Ambient temperature (°C)

    # Different heat input scenarios
    def constant_heat(t):
        return 100  # 100 W constant

    def step_heat(t):
        return 200 if t >= 5 else 0

    def sinusoidal_heat(t):
        return 100 + 50 * np.sin(0.1 * t)

    t = np.linspace(0, 50, 1000)

    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    heat_scenarios = [
        (constant_heat, "Constant Heat Input"),
        (step_heat, "Step Heat Input"),
        (sinusoidal_heat, "Sinusoidal Heat Input")
    ]

    for i, (heat_func, title) in enumerate(heat_scenarios):
        sol = solve_ivp(heat_transfer, (0, 50), [T_amb],
                        t_eval=t, args=(h, A, m, cp, T_amb, heat_func))

        if i < 2:
            axes[0, i].plot(sol.t, sol.y[0], 'b-', linewidth=2, label='Temperature')
            axes[0, i].axhline(y=T_amb, color='g', linestyle='--',
                              alpha=0.7, label='Ambient Temp')
            axes[0, i].set_title(title)
            axes[0, i].set_xlabel('Time (s)')
            axes[0, i].set_ylabel('Temperature (°C)')
            axes[0, i].legend()
            axes[0, i].grid(True, alpha=0.3)
        else:
            axes[1, 0].plot(sol.t, sol.y[0], 'b-', linewidth=2, label='Temperature')
            axes[1, 0].axhline(y=T_amb, color='g', linestyle='--',
                              alpha=0.7, label='Ambient Temp')
            axes[1, 0].set_title(title)
            axes[1, 0].set_xlabel('Time (s)')
            axes[1, 0].set_ylabel('Temperature (°C)')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

    # Parameter sensitivity analysis
    h_values = np.linspace(5, 20, 50)
    steady_state_temps = []

    for h_val in h_values:
        # Steady state: Q_in = hA(T_ss - T_amb)
        T_ss = T_amb + 100 / (h_val * A)  # For 100W input
        steady_state_temps.append(T_ss)

    axes[1, 1].plot(h_values, steady_state_temps, 'r-', linewidth=2)
    axes[1, 1].set_title('Sensitivity to Heat Transfer Coefficient')
    axes[1, 1].set_xlabel('Heat Transfer Coefficient (W/m²K)')
    axes[1, 1].set_ylabel('Steady State Temperature (°C)')
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    return fig

# Process control example
def process_control_example():
    """PID control of first-order process"""

    # Process parameters
    tau_p = 5    # Process time constant
    K_p = 2      # Process gain

    # PID controller parameters
    Kp = 1.5     # Proportional gain
    Ki = 0.3     # Integral gain
    Kd = 0.1     # Derivative gain

    # Setpoint
    setpoint = 1.0

    def controlled_system(t, y, setpoint_func):
        """First-order process with PID control"""
        process_output = y[0]
        integral_error = y[1]
        previous_error = y[2] if len(y) > 2 else 0

        # Current error
        error = setpoint_func(t) - process_output

        # PID control law
        control_signal = (Kp * error +
                         Ki * integral_error +
                         Kd * (error - previous_error))

        # Process dynamics
        dprocess_dt = (K_p * control_signal - process_output) / tau_p

        # Integral of error
        dintegral_dt = error

        return [dprocess_dt, dintegral_dt, error]

    def step_setpoint(t):
        return setpoint if t >= 0 else 0

    t = np.linspace(0, 30, 1000)
    sol = solve_ivp(controlled_system, (0, 30), [0, 0, 0],
                    t_eval=t, args=(step_setpoint,))

    plt.figure(figsize=(12, 8))

    plt.subplot(2, 1, 1)
    plt.plot(sol.t, sol.y[0], 'b-', linewidth=2, label='Process Output')
    plt.axhline(y=setpoint, color='r', linestyle='--',
                alpha=0.7, label='Setpoint')
    plt.xlabel('Time (s)')
    plt.ylabel('Output')
    plt.title('PID Control Response')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Control signal
    control_signals = []
    for i in range(len(sol.t)):
        if i > 0:
            error = setpoint - sol.y[0, i]
            integral_error = sol.y[1, i]
            derivative_error = sol.y[2, i] - sol.y[2, i-1] if i > 1 else 0

            control = Kp * error + Ki * integral_error + Kd * derivative_error
            control_signals.append(control)
        else:
            control_signals.append(0)

    plt.subplot(2, 1, 2)
    plt.plot(sol.t, control_signals, 'g-', linewidth=2, label='Control Signal')
    plt.xlabel('Time (s)')
    plt.ylabel('Control Signal')
    plt.title('PID Controller Output')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    return plt.gcf()

# Run all analyses
print("=== Engineering Systems Analysis ===")

print("\n1. Control System Response Analysis")
fig1 = control_system_response()
plt.show()

print("\n2. Heat Transfer Analysis")
fig2 = heat_transfer_analysis()
plt.show()

print("\n3. Process Control Example")
fig3 = process_control_example()
plt.show()

# Performance metrics
print("\n=== Performance Metrics ===")
tau = 2  # Example time constant
print(f"Time constant: τ = {tau} s")
print(f"Rise time (10%-90%): t_r ≈ 2.2τ = {2.2*tau:.1f} s")
print(f"Settling time (2%): t_s ≈ 4τ = {4*tau:.1f} s")
print(f"Time to 63.2% of final value: t = τ = {tau} s")

# Frequency domain characteristics
print(f"\nFrequency domain:")
print(f"Bandwidth (-3dB): ω_b = 1/τ = {1/tau:.2f} rad/s")
print(f"Phase at ω_b: φ = -45°")
print(f"DC gain: K = 1 (0 dB)")</code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-blue-800 mr-2"></i>R: Engineering Systems
                    </h3>
                    <div class="code-block r-code">
                        <pre><code>library(deSolve)
library(ggplot2)
library(dplyr)
library(gridExtra)
library(signal)

# Engineering Model Functions
engineering_models <- list(

  # First-order system
  first_order_system = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      y <- state[1]
      u <- input_func(t)
      dydt <- (K * u - y) / tau
      return(list(dydt))
    })
  },

  # Heat transfer system
  heat_transfer = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      T <- state[1]
      Q_in <- heat_input_func(t)
      dTdt <- (Q_in - h * A * (T - T_amb)) / (m * cp)
      return(list(dTdt))
    })
  },

  # RC circuit (alternative formulation)
  rc_circuit_eng = function(t, state, parms) {
    with(as.list(c(state, parms)), {
      V_C <- state[1]
      V_in <- input_voltage_func(t)
      dV_Cdt <- (V_in - V_C) / (R * C)
      return(list(dV_Cdt))
    })
  }
)

# Control System Response Analysis
cat("=== Control System Response Analysis ===\n")

# Step response analysis
step_response_analysis <- function() {
  tau_values <- c(1, 2, 5)
  K <- 1
  times <- seq(0, 20, by = 0.1)

  results <- list()

  for (i in seq_along(tau_values)) {
    tau <- tau_values[i]

    # Define step input function
    input_func <- function(t) ifelse(t >= 0, 1, 0)

    parms <- list(tau = tau, K = K, input_func = input_func)

    sol <- ode(y = c(y = 0), times = times,
               func = engineering_models$first_order_system,
               parms = parms)

    sol_df <- as.data.frame(sol)
    sol_df$tau <- tau
    sol_df$y_analytical <- K * (1 - exp(-sol_df$time / tau))

    results[[i]] <- sol_df
  }

  combined_results <- do.call(rbind, results)

  p1 <- ggplot(combined_results, aes(x = time)) +
    geom_line(aes(y = y, color = factor(tau)), size = 1.2) +
    geom_line(aes(y = y_analytical, color = factor(tau)),
              linetype = "dashed", alpha = 0.7) +
    labs(title = "Step Response - First Order Systems",
         x = "Time (s)",
         y = "Output",
         color = "Time Constant (τ)") +
    theme_minimal()

  return(p1)
}

# Heat Transfer Analysis
heat_transfer_analysis <- function() {
  # Parameters
  parms_heat <- list(
    h = 10,       # Heat transfer coefficient (W/m²K)
    A = 0.1,      # Surface area (m²)
    m = 0.5,      # Mass (kg)
    cp = 500,     # Specific heat capacity (J/kgK)
    T_amb = 25    # Ambient temperature (°C)
  )

  times <- seq(0, 50, by = 0.1)

  # Different heat input scenarios
  scenarios <- list(
    constant = function(t) 100,  # Constant 100W
    step = function(t) ifelse(t >= 5, 200, 0),  # Step at t=5
    sinusoidal = function(t) 100 + 50 * sin(0.1 * t)  # Sinusoidal
  )

  results <- list()

  for (scenario_name in names(scenarios)) {
    parms_heat$heat_input_func <- scenarios[[scenario_name]]

    sol <- ode(y = c(T = parms_heat$T_amb), times = times,
               func = engineering_models$heat_transfer,
               parms = parms_heat)

    sol_df <- as.data.frame(sol)
    sol_df$scenario <- scenario_name

    results[[scenario_name]] <- sol_df
  }

  combined_heat <- do.call(rbind, results)

  p2 <- ggplot(combined_heat, aes(x = time, y = T, color = scenario)) +
    geom_line(size = 1.2) +
    geom_hline(yintercept = parms_heat$T_amb,
               linetype = "dashed", alpha = 0.7) +
    labs(title = "Heat Transfer Response",
         x = "Time (s)",
         y = "Temperature (°C)",
         color = "Heat Input") +
    theme_minimal()

  return(p2)
}

# Frequency Response Analysis
frequency_response_analysis <- function() {
  tau_values <- c(0.5, 1, 2, 5)
  K <- 1

  # Frequency range (rad/s)
  frequencies <- 10^seq(-2, 2, length.out = 100)

  results <- list()

  for (tau in tau_values) {
    # Transfer function: G(jω) = K / (jωτ + 1)
    s <- 1i * frequencies
    G <- K / (tau * s + 1)

    magnitude_db <- 20 * log10(abs(G))
    phase_deg <- Arg(G) * 180 / pi

    freq_df <- data.frame(
      frequency = frequencies,
      magnitude_db = magnitude_db,
      phase_deg = phase_deg,
      tau = tau
    )

    results[[as.character(tau)]] <- freq_df
  }

  combined_freq <- do.call(rbind, results)

  p3 <- ggplot(combined_freq, aes(x = frequency, y = magnitude_db,
                                  color = factor(tau))) +
    geom_line(size = 1.2) +
    scale_x_log10() +
    labs(title = "Bode Plot - Magnitude",
         x = "Frequency (rad/s)",
         y = "Magnitude (dB)",
         color = "Time Constant (τ)") +
    theme_minimal()

  p4 <- ggplot(combined_freq, aes(x = frequency, y = phase_deg,
                                  color = factor(tau))) +
    geom_line(size = 1.2) +
    scale_x_log10() +
    labs(title = "Bode Plot - Phase",
         x = "Frequency (rad/s)",
         y = "Phase (degrees)",
         color = "Time Constant (τ)") +
    theme_minimal()

  return(list(magnitude = p3, phase = p4))
}

# Parameter Sensitivity Analysis
sensitivity_analysis <- function() {
  # Analyze sensitivity of settling time to time constant
  tau_range <- seq(0.5, 10, by = 0.1)

  # Settling time (2% criterion) ≈ 4τ
  settling_times <- 4 * tau_range

  # Rise time (10% to 90%) ≈ 2.2τ
  rise_times <- 2.2 * tau_range

  sensitivity_df <- data.frame(
    tau = tau_range,
    settling_time = settling_times,
    rise_time = rise_times
  )

  p5 <- ggplot(sensitivity_df, aes(x = tau)) +
    geom_line(aes(y = settling_time, color = "Settling Time"), size = 1.2) +
    geom_line(aes(y = rise_time, color = "Rise Time"), size = 1.2) +
    labs(title = "Time Response Sensitivity",
         x = "Time Constant τ (s)",
         y = "Response Time (s)",
         color = "Metric") +
    theme_minimal()

  return(p5)
}

# Execute all analyses
p1 <- step_response_analysis()
p2 <- heat_transfer_analysis()
freq_plots <- frequency_response_analysis()
p5 <- sensitivity_analysis()

# Display plots
grid.arrange(p1, p2, ncol = 1)
grid.arrange(freq_plots$magnitude, freq_plots$phase, ncol = 1)
print(p5)

# Performance Metrics Calculation
cat("\n=== Performance Metrics ===\n")
tau_example <- 2
cat(sprintf("Time constant: τ = %g s\n", tau_example))
cat(sprintf("Rise time (10%%-90%%): t_r ≈ 2.2τ = %.1f s\n", 2.2 * tau_example))
cat(sprintf("Settling time (2%%): t_s ≈ 4τ = %.1f s\n", 4 * tau_example))
cat(sprintf("Time to 63.2%% of final value: t = τ = %g s\n", tau_example))

cat("\nFrequency domain:\n")
cat(sprintf("Bandwidth (-3dB): ω_b = 1/τ = %.2f rad/s\n", 1/tau_example))
cat("Phase at ω_b: φ = -45°\n")
cat("DC gain: K = 1 (0 dB)\n")

# Engineering Design Guidelines
cat("\n=== Engineering Design Guidelines ===\n")
cat("1. Faster response (smaller τ) → Higher bandwidth, more noise sensitivity\n")
cat("2. Slower response (larger τ) → Lower bandwidth, better noise rejection\n")
cat("3. Trade-off between speed and stability\n")
cat("4. Typical design: Choose τ based on required settling time\n")
cat("5. For control systems: τ should be much smaller than disturbance time scales\n")</code></pre>
                    </div>
                </div>
            </div>
        </section>



        <!-- Advanced Analysis -->
        <section id="advanced" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-microscope text-red-600 mr-3"></i>Advanced Analysis Techniques
            </h2>

            <div class="theorem-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-brain mr-2"></i>Qualitative Analysis</h3>
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <h4 class="font-bold mb-2">Equilibrium Points</h4>
                        <p class="text-sm mb-2">Solutions where $\frac{dy}{dt} = 0$</p>
                        <ul class="text-sm space-y-1">
                            <li>• Stable: Solutions approach equilibrium</li>
                            <li>• Unstable: Solutions move away</li>
                            <li>• Semi-stable: One-sided stability</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">Phase Line Analysis</h4>
                        <p class="text-sm mb-2">Graphical representation of solution behavior</p>
                        <ul class="text-sm space-y-1">
                            <li>• Direction field arrows</li>
                            <li>• Equilibrium classification</li>
                            <li>• Long-term behavior prediction</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-project-diagram text-red-600 mr-2"></i>Advanced Analysis Visualizations
                </h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="chart-container">
                        <canvas id="advancedPhaseChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="advancedStabilityChart"></canvas>
                    </div>
                </div>
                <div class="large-chart mt-6">
                    <canvas id="advancedDirectionChart"></canvas>
                </div>
            </div>

            <div class="example-box mt-6">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-search mr-2"></i>Bifurcation Analysis</h3>
                <p class="mb-3">Study how equilibrium behavior changes with parameter variation:</p>

                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Example: Harvesting Model</h4>
                    <p class="text-sm mb-2">$\frac{dP}{dt} = rP(1 - \frac{P}{K}) - H$</p>
                    <p class="text-sm">P = population, r = growth rate, K = carrying capacity, H = harvest rate</p>
                </div>

                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Critical Harvest Rate</h4>
                        <p class="text-sm">$H_{max} = \frac{rK}{4}$ (maximum sustainable harvest)</p>
                        <p class="text-sm">Two equilibria exist for H < H_max</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Saddle-Node Bifurcation</h4>
                        <p class="text-sm">At H = H_max, equilibria collide and disappear</p>
                        <p class="text-sm">Population crashes for H > H_max</p>
                    </div>
                </div>
            </div>

            <div class="chart-container mt-6">
                <canvas id="advancedBifurcationChart"></canvas>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Python: Advanced Analysis
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp
from scipy.optimize import fsolve, brentq
from matplotlib.patches import Circle
import sympy as sp

class AdvancedODEAnalysis:
    def __init__(self):
        pass

    def phase_line_analysis(self, f, y_range, title="Phase Line Analysis"):
        """Analyze phase line for autonomous ODE dy/dt = f(y)"""
        y = np.linspace(y_range[0], y_range[1], 1000)
        dydt = f(y)

        # Find equilibria (zeros of f(y))
        equilibria = []
        sign_changes = np.where(np.diff(np.sign(dydt)))[0]

        for i in sign_changes:
            try:
                eq = brentq(f, y[i], y[i+1])
                equilibria.append(eq)
            except:
                pass

        # Classify equilibria stability
        stable_eq = []
        unstable_eq = []

        for eq in equilibria:
            # Check derivative at equilibrium point
            h = 1e-6
            fprime = (f(eq + h) - f(eq - h)) / (2 * h)
            if fprime < 0:
                stable_eq.append(eq)
            else:
                unstable_eq.append(eq)

        # Plot phase line
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # Plot dy/dt vs y
        ax1.plot(y, dydt, 'b-', linewidth=2, label='dy/dt = f(y)')
        ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        ax1.axvline(x=0, color='k', linestyle='--', alpha=0.5)

        # Mark equilibria
        for eq in stable_eq:
            ax1.plot(eq, 0, 'go', markersize=10, label=f'Stable: y = {eq:.2f}')
        for eq in unstable_eq:
            ax1.plot(eq, 0, 'ro', markersize=10, label=f'Unstable: y = {eq:.2f}')

        ax1.set_xlabel('y')
        ax1.set_ylabel('dy/dt')
        ax1.set_title(title)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        return equilibria, stable_eq, unstable_eq

    def bifurcation_analysis(self, f, param_range, param_name='r'):
        """Analyze bifurcations as parameter varies"""
        params = np.linspace(param_range[0], param_range[1], 200)
        equilibria = []
        stability = []

        for p in params:
            # Find equilibria for this parameter value
            eq_for_p = []
            stab_for_p = []

            # Search for equilibria in reasonable range
            y_search = np.linspace(-10, 10, 1000)
            try:
                f_vals = f(y_search, p)
                sign_changes = np.where(np.diff(np.sign(f_vals)))[0]

                for i in sign_changes:
                    try:
                        eq = brentq(lambda y: f(y, p), y_search[i], y_search[i+1])
                        eq_for_p.append(eq)

                        # Check stability
                        h = 1e-6
                        fprime = (f(eq + h, p) - f(eq - h, p)) / (2 * h)
                        stab_for_p.append(fprime < 0)  # True if stable
                    except:
                        pass
            except:
                pass

            equilibria.append(eq_for_p)
            stability.append(stab_for_p)

        return params, equilibria, stability

# Example usage
analyzer = AdvancedODEAnalysis()

# Logistic equation with harvesting
def logistic_harvesting(y, r=1, K=10, H=2):
    """dy/dt = ry(1 - y/K) - H"""
    return r * y * (1 - y/K) - H

# Phase line analysis
y_range = (-2, 12)
equilibria, stable, unstable = analyzer.phase_line_analysis(
    lambda y: logistic_harvesting(y), y_range,
    "Logistic Growth with Harvesting"
)

# Bifurcation analysis
def logistic_H_param(y, H):
    return logistic_harvesting(y, H=H)

H_range = (0, 5)
params, eqs, stabs = analyzer.bifurcation_analysis(
    logistic_H_param, H_range, 'H (Harvest Rate)'
)</code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-blue-800 mr-2"></i>R: Advanced Analysis
                    </h3>
                    <div class="code-block r-code">
                        <pre><code>library(deSolve)
library(ggplot2)
library(dplyr)
library(phaseR)
library(rootSolve)

# Advanced ODE Analysis Tools in R
AdvancedAnalysis <- list(

  # Phase line analysis function
  phase_line_analysis = function(f, y_range, n_points = 1000) {
    y_vals <- seq(y_range[1], y_range[2], length.out = n_points)
    dydt_vals <- f(y_vals)

    # Find equilibria (sign changes)
    sign_changes <- which(diff(sign(dydt_vals)) != 0)
    equilibria <- c()

    for (i in sign_changes) {
      try({
        eq <- uniroot(f, c(y_vals[i], y_vals[i+1]))$root
        equilibria <- c(equilibria, eq)
      }, silent = TRUE)
    }

    # Create phase line data
    phase_data <- data.frame(
      y = y_vals,
      dydt = dydt_vals
    )

    return(list(data = phase_data, equilibria = equilibria))
  },

  # Bifurcation analysis
  bifurcation_analysis = function(f, param_range, n_params = 100) {
    params <- seq(param_range[1], param_range[2], length.out = n_params)
    bifurcation_data <- data.frame()

    for (p in params) {
      # Find equilibria for this parameter
      f_param <- function(y) f(y, p)

      # Search for equilibria in reasonable range
      y_search <- seq(-10, 10, by = 0.1)
      equilibria <- c()

      for (i in 1:(length(y_search)-1)) {
        if (sign(f_param(y_search[i])) != sign(f_param(y_search[i+1]))) {
          try({
            eq <- uniroot(f_param, c(y_search[i], y_search[i+1]))$root
            equilibria <- c(equilibria, eq)
          }, silent = TRUE)
        }
      }

      # Check stability of equilibria
      for (eq in equilibria) {
        # Numerical derivative for stability
        h <- 1e-6
        fprime <- (f_param(eq + h) - f_param(eq - h)) / (2 * h)
        stable <- fprime < 0

        bifurcation_data <- rbind(bifurcation_data,
                                data.frame(parameter = p,
                                         equilibrium = eq,
                                         stable = stable))
      }
    }

    return(bifurcation_data)
  }
)

# Example: Harvesting model analysis
harvesting_model <- function(P, H = 2) {
  r <- 1
  K <- 10
  return(r * P * (1 - P/K) - H)
}

# Phase line analysis for harvesting model
phase_result <- AdvancedAnalysis$phase_line_analysis(harvesting_model, c(-1, 12))

# Visualize phase line
phase_plot <- ggplot(phase_result$data, aes(x = y, y = dydt)) +
  geom_line(size = 1.2, color = "blue") +
  geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.7) +
  geom_vline(xintercept = 0, linetype = "dashed", alpha = 0.7) +
  labs(
    title = "Phase Line Analysis: Harvesting Model",
    x = "Population (P)",
    y = "dP/dt"
  ) +
  theme_minimal()

print(phase_plot)</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Case Studies -->
        <section id="case-studies" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-project-diagram text-indigo-600 mr-3"></i>Case Studies
            </h2>

            <div class="grid md:grid-cols-2 gap-6">
                <div class="definition-box">
                    <h3 class="text-xl font-bold mb-3"><i class="fas fa-virus mr-2"></i>Epidemic Modeling</h3>
                    <p class="mb-3">The SIR model uses a system of ODEs to model disease spread:</p>
                    <div class="text-sm space-y-2">
                        <p>$\frac{dS}{dt} = -\beta SI/N$ (Susceptible)</p>
                        <p>$\frac{dI}{dt} = \beta SI/N - \gamma I$ (Infected)</p>
                        <p>$\frac{dR}{dt} = \gamma I$ (Recovered)</p>
                    </div>
                    <p class="text-sm mt-3">For early stages, infected population follows: $\frac{dI}{dt} \approx (\beta - \gamma)I$</p>
                </div>

                <div class="application-box">
                    <h3 class="text-xl font-bold mb-3"><i class="fas fa-industry mr-2"></i>Chemical Reactor</h3>
                    <p class="mb-3">A continuous stirred tank reactor (CSTR) follows:</p>
                    <div class="text-sm space-y-2">
                        <p>$\frac{dC}{dt} = \frac{F}{V}(C_{in} - C) - kC^n$</p>
                        <p>Where: F = flow rate, V = volume, k = reaction rate constant</p>
                    </div>
                    <p class="text-sm mt-3">For first-order reactions (n=1), this becomes linear first-order ODE.</p>
                </div>
            </div>
        </section>

        <!-- Summary -->
        <section class="mb-12">
            <div class="bg-gradient-to-r from-blue-600 to-purple-700 text-white p-8 rounded-lg">
                <h2 class="text-2xl font-bold mb-4"><i class="fas fa-graduation-cap mr-3"></i>Chapter Summary</h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-bold mb-3">Key Modeling Steps</h3>
                        <ul class="space-y-2 text-sm">
                            <li>1. <strong>Identify variables:</strong> What changes over time?</li>
                            <li>2. <strong>Find relationships:</strong> How do variables relate?</li>
                            <li>3. <strong>Formulate ODE:</strong> Express rate of change</li>
                            <li>4. <strong>Solve equation:</strong> Use appropriate method</li>
                            <li>5. <strong>Validate model:</strong> Check against data</li>
                            <li>6. <strong>Interpret results:</strong> What do solutions mean?</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold mb-3">Common Applications</h3>
                        <ul class="space-y-2 text-sm">
                            <li>• <strong>Population dynamics:</strong> Growth and harvesting</li>
                            <li>• <strong>Economics:</strong> Interest, loans, investments</li>
                            <li>• <strong>Physics:</strong> Cooling, radioactive decay</li>
                            <li>• <strong>Engineering:</strong> RC circuits, control systems</li>
                            <li>• <strong>Biology:</strong> Drug concentration, enzyme kinetics</li>
                            <li>• <strong>Chemistry:</strong> Reaction rates, mixing problems</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script>
        // Configure Chart.js defaults before creating any charts
        function configureChartDefaults() {
            if (typeof Chart !== 'undefined') {
                Chart.defaults.responsive = true;
                Chart.defaults.maintainAspectRatio = false;
                Chart.defaults.devicePixelRatio = window.devicePixelRatio || 1;

                // Performance optimization for large datasets
                Chart.defaults.elements.point.radius = 0;
                Chart.defaults.elements.point.hoverRadius = 4;
                Chart.defaults.elements.line.tension = 0.4;

                // Accessibility improvements
                Chart.defaults.plugins.legend.labels.usePointStyle = true;
                Chart.defaults.plugins.tooltip.titleFont = { size: 14, weight: 'bold' };
                Chart.defaults.plugins.tooltip.bodyFont = { size: 12 };

                console.log('Chart.js defaults configured');
            }
        }

        // Initialize main charts (classification, mixing, population)
        function initializeMainCharts() {
            // Configure defaults first
            configureChartDefaults();

            // Classification Chart - Decision Tree for ODE Types
            const classificationCanvas = document.getElementById('classificationChart');
        if (classificationCanvas) {
            try {
                const classificationCtx = classificationCanvas.getContext('2d');
                const classificationChart = new Chart(classificationCtx, {
                    type: 'doughnut',
                    data: {
                labels: ['Separable', 'Linear First-Order', 'Exact', 'Other Methods'],
                datasets: [{
                    data: [35, 40, 15, 10],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'ODE Classification Decision Tree',
                        font: { size: 16 }
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
                classificationCanvas.closest('.chart-container')?.classList.add('loaded');
            } catch (error) {
                console.error('Error creating classificationChart:', error);
                classificationCanvas.closest('.chart-container')?.classList.add('load-error');
            }
        }

        // Mixing Chart - Salt Water Tank Problem
        const mixingCanvas = document.getElementById('mixingChart');
        if (mixingCanvas) {
            try {
                const mixingCtx = mixingCanvas.getContext('2d');
                // Generate mixing problem data
                const timePoints = Array.from({length: 501}, (_, i) => i);
        const concentration = timePoints.map(t => 0.5 * (1 - Math.exp(-0.01 * t)));
        const equilibrium = timePoints.map(t => 0.5);
        
        mixingChartInstance = new Chart(mixingCtx, {
            type: 'line',
            data: {
                labels: timePoints,
                datasets: [{
                    label: 'Concentration C(t)',
                    data: concentration,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    fill: true,
                    tension: 0.4,
                    borderWidth: 3
                }, {
                    label: 'Equilibrium (0.5 kg/L)',
                    data: equilibrium,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    borderDash: [5, 5],
                    fill: false,
                    tension: 0,
                    borderWidth: 2
                }, {
                    label: '95% of Equilibrium',
                    data: timePoints.map(t => 0.475),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    borderDash: [10, 5],
                    fill: false,
                    tension: 0,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Time (minutes)',
                            font: { size: 14 }
                        },
                        grid: {
                            alpha: 0.3
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Concentration (kg/L)',
                            font: { size: 14 }
                        },
                        min: 0,
                        max: 0.6,
                        grid: {
                            alpha: 0.3
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Salt Water Tank Mixing Problem',
                        font: { size: 16 }
                    },
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            afterLabel: function(context) {
                                if (context.datasetIndex === 0) {
                                    const t = context.parsed.x;
                                    const percentage = ((context.parsed.y / 0.5) * 100).toFixed(1);
                                    return `${percentage}% of equilibrium at t=${t} min`;
                                }
                                return null;
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 0,
                        hoverRadius: 5
                    }
                }
            }
        });
                mixingCanvas.closest('.chart-container')?.classList.add('loaded');
            } catch (error) {
                console.error('Error creating mixingChartInstance:', error);
                mixingCanvas.closest('.chart-container')?.classList.add('load-error');
            }
        }

        // Population Chart - Improved version with multiple scenarios
        const populationCanvas = document.getElementById('populationChart'); // Renamed from populationCtx for clarity
        
        if (!populationCanvas) {
            console.error('Population chart canvas element not found!');
            // Attempt to add load-error to container if possible, though less likely to exist
            const container = document.querySelector('canvas#populationChart')?.closest('.chart-container');
            container?.classList.add('load-error');
            return;
        }
        
        const ctx2d = populationCanvas.getContext('2d');
        if (!ctx2d) {
            console.error('Could not get 2D context for population chart!');
            populationCanvas.closest('.chart-container')?.classList.add('load-error');
            return;
        }
        
        // Check if Chart.js is loaded
        if (typeof Chart === 'undefined') {
            console.error('Chart.js library not loaded!');
            // Create fallback message
            const fallbackDiv = document.createElement('div');
            fallbackDiv.innerHTML = '<p class="text-center text-red-600 p-4">Chart.js library failed to load. Please check your internet connection.</p>';
            populationCanvas.parentNode.replaceChild(fallbackDiv, populationCanvas);
            return;
        }
        
        console.log('Initializing population chart...');
        
        // Generate population data - make variables global for update function
        window.timeRange = Array.from({length: 51}, (_, i) => i);
        window.P0 = 50;
        const r = 0.1, K = 1000;

        // Exponential growth
        const exponentialData = window.timeRange.map(t => window.P0 * Math.exp(r * t));
        
        // Logistic growth
        const logisticData = window.timeRange.map(t => K / (1 + (K/window.P0 - 1) * Math.exp(-r * t)));

        // Growth with harvesting (H = 25)
        const harvestingData = window.timeRange.map(t => {
            // Approximate solution for harvesting model
            const H = 25;
            const equilibrium = K * (1 - Math.sqrt(1 - 4*H/(r*K)));
            if (equilibrium > 0) {
                return equilibrium + (window.P0 - equilibrium) * Math.exp(-r * (1 - 2*equilibrium/K) * t);
            }
            return 0;
        });
        
        // Make populationChart global
        try {
            window.populationChart = new Chart(ctx2d, {
                type: 'line',
                data: {
                    labels: window.timeRange,
                    datasets: [{
                        label: 'Exponential Growth',
                        data: exponentialData,
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3
                    }, {
                        label: 'Logistic Growth',
                        data: logisticData,
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3
                    }, {
                        label: 'With Harvesting (H=25)',
                        data: harvestingData,
                        borderColor: 'rgb(255, 206, 86)',
                        backgroundColor: 'rgba(255, 206, 86, 0.1)',
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3
                    }, {
                    label: 'Carrying Capacity',
                    data: window.timeRange.map(t => K),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        borderDash: [10, 5],
                        fill: false,
                        tension: 0,
                        borderWidth: 2
                    }]
                },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Time (years)',
                            font: { size: 14 }
                        },
                        grid: {
                            alpha: 0.3
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Population',
                            font: { size: 14 }
                        },
                        min: 0,
                        max: 1200,
                        grid: {
                            alpha: 0.3
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Population Growth Models Comparison',
                        font: { size: 16 }
                    },
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            afterLabel: function(context) {
                                if (context.datasetIndex === 1) { // Logistic model
                                    const t = context.parsed.x;
                                    const P = context.parsed.y;
                                    const growthRate = (r * P * (1 - P/K)).toFixed(2);
                                    return `Growth rate: ${growthRate} individuals/year`;
                                }
                                return null;
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 0,
                        hoverRadius: 5
                    }
                }
            }
        });
        
        console.log('Population chart initialized successfully');
        populationCanvas.closest('.chart-container')?.classList.add('loaded');
        
        } catch (error) {
            console.error('Error initializing population chart:', error);
            populationCanvas.closest('.chart-container')?.classList.add('load-error');
            const errorDiv = document.createElement('div');
            errorDiv.innerHTML = `
                <div class="text-center p-8 bg-red-50 border border-red-200 rounded-lg">
                    <i class="fas fa-exclamation-triangle text-red-500 text-3xl mb-4"></i>
                    <h3 class="text-lg font-semibold text-red-700 mb-2">Chart Loading Error</h3>
                    <p class="text-red-600">Unable to load the population dynamics chart. Please refresh the page or check your internet connection.</p>
                    <details class="mt-4 text-left">
                        <summary class="cursor-pointer text-sm text-red-500">Technical Details</summary>
                        <pre class="mt-2 text-xs bg-gray-100 p-2 rounded">${error.message}</pre>
                    </details>
                </div>
            `;
            populationCanvas.parentNode.replaceChild(errorDiv, populationCanvas);
        }

        // Note: Cooling chart functionality is handled in the Physics section (physicsCoolingChart)
        }

        // Enhanced visualization creation functions
        function createEconomicCharts() {
            // Economic Price Dynamics Chart
            const priceCtx = document.getElementById('economicPriceChart');
            if (priceCtx) {
                try {
                    const timeRange = Array.from({length: 101}, (_, i) => i / 10);
                    const equilibriumPrice = 100;
                    const initialPrices = [80, 120, 90, 110];
                    const adjustmentRate = 0.3;
                    
                    const datasets = initialPrices.map((P0, index) => {
                        const prices = timeRange.map(t => 
                            equilibriumPrice + (P0 - equilibriumPrice) * Math.exp(-adjustmentRate * t)
                        );
                        return {
                            label: `Initial Price: $${P0}`,
                            data: prices,
                            borderColor: `hsl(${index * 60}, 70%, 50%)`,
                            backgroundColor: `hsl(${index * 60}, 70%, 50%, 0.1)`,
                            fill: false,
                            tension: 0.4
                        };
                    });

                    datasets.push({
                        label: 'Equilibrium Price',
                        data: timeRange.map(() => equilibriumPrice),
                        borderColor: '#333',
                        borderDash: [5, 5],
                        pointRadius: 0
                    });

                    new Chart(priceCtx, {
                        type: 'line',
                        data: {
                            labels: timeRange,
                            datasets: datasets
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'Market Price Adjustment Dynamics'
                                },
                                legend: { position: 'top' }
                            },
                            scales: {
                                x: { title: { display: true, text: 'Time (months)' } },
                                y: { title: { display: true, text: 'Price ($)' } }
                            }
                        }
                    });
                    console.log('Economic price chart created successfully');
                    priceCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating economic price chart:', error);
                    priceCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Economic Price Chart failed to load</div>';
                    priceCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }

            // Economic Growth Chart
            const growthCtx = document.getElementById('economicGrowthChart');
            if (growthCtx) {
                try {
                    const timeRange = Array.from({length: 51}, (_, i) => i);
                    const scenarios = [
                        { label: 'Low Growth (2%)', rate: 0.02, color: '#ff6b6b' },
                        { label: 'Moderate Growth (5%)', rate: 0.05, color: '#4ecdc4' },
                        { label: 'High Growth (8%)', rate: 0.08, color: '#45b7d1' }
                    ];
                    const initialValue = 1000;

                    const datasets = scenarios.map(scenario => ({
                        label: scenario.label,
                        data: timeRange.map(t => initialValue * Math.exp(scenario.rate * t)),
                        borderColor: scenario.color,
                        backgroundColor: scenario.color + '20',
                        fill: false,
                        tension: 0.4
                    }));

                    new Chart(growthCtx, {
                        type: 'line',
                        data: {
                            labels: timeRange,
                            datasets: datasets
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'Economic Growth Scenarios'
                                }
                            },
                            scales: {
                                x: { title: { display: true, text: 'Time (years)' } },
                                y: { 
                                    title: { display: true, text: 'Value ($)' },
                                    type: 'logarithmic'
                                }
                            }
                        }
                    });
                    console.log('Economic growth chart created successfully');
                    growthCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating economic growth chart:', error);
                    growthCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Economic Growth Chart failed to load</div>';
                    growthCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }

            // Investment Chart - Adding missing canvas element check
            const investmentCtx = document.getElementById('economicInvestmentChart');
            if (investmentCtx) {
                try {
                    const timeRange = Array.from({length: 101}, (_, i) => i / 2);
                const interestRate = 0.05;
                const initialAmount = 10000;
                const scenarios = [
                    { deposit: 0, label: 'No Deposits' },
                    { deposit: 500, label: 'Monthly $500 Deposits' },
                    { deposit: 1000, label: 'Monthly $1000 Deposits' },
                    { deposit: -300, label: 'Monthly $300 Withdrawals' }
                ];

                const datasets = scenarios.map((scenario, index) => {
                    const D = scenario.deposit * 12; // Annual equivalent
                    const data = timeRange.map(t => {
                        if (D === 0) {
                            return initialAmount * Math.exp(interestRate * t);
                        } else {
                            return D/interestRate + (initialAmount - D/interestRate) * Math.exp(interestRate * t);
                        }
                    });
                    
                    return {
                        label: scenario.label,
                        data: data,
                        borderColor: `hsl(${index * 90}, 70%, 50%)`,
                        backgroundColor: `hsl(${index * 90}, 70%, 50%, 0.1)`,
                        fill: false,
                        tension: 0.4
                    };
                });

                new Chart(investmentCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Investment Growth with Continuous Deposits/Withdrawals'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (years)' } },
                            y: { title: { display: true, text: 'Account Balance ($)' } }
                        }
                    }
                });
                    investmentCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating economic investment chart:', error);
                    investmentCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Economic Investment Chart failed to load</div>';
                    investmentCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }
        }

        function createPhysicsCharts() {
            // Radioactive Decay Chart
            const decayCtx = document.getElementById('physicsDecayChart');
            if (decayCtx) {
                try {
                    const timeRange = Array.from({length: 101}, (_, i) => i);
                    const isotopes = [
                        { name: 'Carbon-14', halfLife: 5730, color: '#ff6b6b' },
                        { name: 'Uranium-238', halfLife: 4468000, color: '#4ecdc4' },
                        { name: 'Tritium', halfLife: 12.3, color: '#45b7d1' },
                        { name: 'Iodine-131', halfLife: 8.02, color: '#f7dc6f' }
                    ];

                    const datasets = isotopes.map(isotope => {
                        const lambda = Math.log(2) / isotope.halfLife;
                        const data = timeRange.map(t => Math.exp(-lambda * t));
                        
                        return {
                            label: `${isotope.name} (t₁/₂ = ${isotope.halfLife} years)`,
                            data: data,
                            borderColor: isotope.color,
                            backgroundColor: isotope.color + '20',
                            fill: false,
                            tension: 0.4
                        };
                    });

                    new Chart(decayCtx, {
                        type: 'line',
                        data: {
                            labels: timeRange,
                            datasets: datasets
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'Radioactive Decay of Different Isotopes'
                                }
                            },
                            scales: {
                                x: { title: { display: true, text: 'Time (years)' } },
                                y: { 
                                    title: { display: true, text: 'Fraction Remaining' },
                                    type: 'logarithmic'
                                }
                            }
                        }
                    });
                    console.log('Physics decay chart created successfully');
                    decayCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating physics decay chart:', error);
                    decayCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Physics Decay Chart failed to load</div>';
                    decayCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }

            // Circuit Response Chart
            const circuitCtx = document.getElementById('physicsCircuitChart');
            if (circuitCtx) {
                try {
                    const timeRange = Array.from({length: 201}, (_, i) => i * 0.01);
                    const circuits = [
                        { R: 100, L: 0.1, label: 'Fast Circuit (τ = 1ms)', color: '#ff6b6b' },
                        { R: 1000, L: 1, label: 'Medium Circuit (τ = 1ms)', color: '#4ecdc4' },
                        { R: 10000, L: 10, label: 'Slow Circuit (τ = 1ms)', color: '#45b7d1' }
                    ];
                    const inputVoltage = 5;

                    const datasets = circuits.map(circuit => {
                        const tau = circuit.L / circuit.R;
                        const data = timeRange.map(t => inputVoltage * (1 - Math.exp(-t / tau)));
                        
                        return {
                            label: circuit.label,
                            data: data,
                            borderColor: circuit.color,
                            backgroundColor: circuit.color + '20',
                            fill: false,
                            tension: 0.4
                        };
                    });

                    datasets.push({
                        label: 'Input Voltage',
                        data: timeRange.map(() => inputVoltage),
                        borderColor: '#333',
                        borderDash: [5, 5],
                        pointRadius: 0
                    });

                    new Chart(circuitCtx, {
                        type: 'line',
                        data: {
                            labels: timeRange,
                            datasets: datasets
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'RL Circuit Step Response'
                                }
                            },
                            scales: {
                                x: { title: { display: true, text: 'Time (s)' } },
                                y: { title: { display: true, text: 'Current (A)' } }
                            }
                        }
                    });
                    console.log('Physics circuit chart created successfully');
                    circuitCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating physics circuit chart:', error);
                    circuitCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Physics Circuit Chart failed to load</div>';
                    circuitCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }

            // Enhanced Cooling Chart
            const coolingCtx = document.getElementById('physicsCoolingChart');
            if (coolingCtx) {
                try {
                    const timeRange = Array.from({length: 201}, (_, i) => i);
                    const roomTemp = 20;
                    const scenarios = [
                        { T0: 100, k: 0.05, material: 'Metal (fast cooling)', color: '#ff6b6b' },
                        { T0: 100, k: 0.02, material: 'Ceramic (medium cooling)', color: '#4ecdc4' },
                        { T0: 100, k: 0.01, material: 'Insulated (slow cooling)', color: '#45b7d1' },
                        { T0: 150, k: 0.03, material: 'Hot metal', color: '#f7dc6f' }
                    ];

                    const datasets = scenarios.map(scenario => {
                        const data = timeRange.map(t => 
                            roomTemp + (scenario.T0 - roomTemp) * Math.exp(-scenario.k * t)
                        );
                        
                        return {
                            label: scenario.material,
                            data: data,
                            borderColor: scenario.color,
                            backgroundColor: scenario.color + '20',
                            fill: false,
                            tension: 0.4
                        };
                    });

                    datasets.push({
                        label: 'Room Temperature',
                        data: timeRange.map(() => roomTemp),
                        borderColor: '#333',
                        borderDash: [5, 5],
                        pointRadius: 0
                    });

                    new Chart(coolingCtx, {
                        type: 'line',
                        data: {
                            labels: timeRange,
                            datasets: datasets
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'Newton\'s Law of Cooling'
                                }
                            },
                            scales: {
                                x: { title: { display: true, text: 'Time (minutes)' } },
                                y: { title: { display: true, text: 'Temperature (°C)' } }
                            }
                        }
                    });
                    console.log('Physics cooling chart created successfully');
                    coolingCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating physics cooling chart:', error);
                    coolingCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Physics Cooling Chart failed to load</div>';
                    coolingCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }
        }

        function createEngineeringCharts() {
            // Step Response Chart
            const stepCtx = document.getElementById('engineeringStepChart');
            if (stepCtx) {
                try {
                    const timeRange = Array.from({length: 201}, (_, i) => i * 0.05);
                    const systems = [
                        { tau: 0.5, K: 1, label: 'Fast System (τ = 0.5s)', color: '#ff6b6b' },
                        { tau: 1.0, K: 1, label: 'Medium System (τ = 1.0s)', color: '#4ecdc4' },
                        { tau: 2.0, K: 1, label: 'Slow System (τ = 2.0s)', color: '#45b7d1' },
                        { tau: 1.0, K: 2, label: 'High Gain (K = 2)', color: '#f7dc6f' }
                    ];

                    const datasets = systems.map(system => {
                        const data = timeRange.map(t => system.K * (1 - Math.exp(-t / system.tau)));
                        
                        return {
                            label: system.label,
                            data: data,
                            borderColor: system.color,
                            backgroundColor: system.color + '20',
                            fill: false,
                            tension: 0.4
                        };
                    });

                    // Add time constant markers
                    systems.forEach((system, index) => {
                        if (index < 3) { // Only for different time constants
                            const tauIndex = Math.round(system.tau / 0.05);
                            if (tauIndex < timeRange.length) {
                                datasets.push({
                                    label: `τ = ${system.tau}s marker`,
                                    data: timeRange.map((t, i) => i === tauIndex ? system.K * 0.632 : null),
                                    backgroundColor: system.color,
                                    borderColor: system.color,
                                    pointRadius: 8,
                                    showLine: false,
                                    pointStyle: 'circle'
                                });
                            }
                        }
                    });

                    new Chart(stepCtx, {
                        type: 'line',
                        data: {
                            labels: timeRange,
                            datasets: datasets
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'First-Order System Step Response'
                                }
                            },
                            scales: {
                                x: { title: { display: true, text: 'Time (s)' } },
                                y: { title: { display: true, text: 'Output' } }
                            }
                        }
                    });
                    console.log('Engineering step chart created successfully');
                    stepCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating engineering step chart:', error);
                    stepCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Engineering Step Chart failed to load</div>';
                    stepCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }

            // Frequency Response Chart
            const freqCtx = document.getElementById('engineeringFrequencyChart');
            if (freqCtx) {
                try {
                    const frequencies = Array.from({length: 100}, (_, i) => Math.pow(10, (i - 50) / 25));
                    const systems = [
                        { tau: 0.1, label: 'τ = 0.1s', color: '#ff6b6b' },
                        { tau: 1.0, label: 'τ = 1.0s', color: '#4ecdc4' },
                        { tau: 10.0, label: 'τ = 10.0s', color: '#45b7d1' }
                    ];

                    const datasets = systems.map(system => {
                        const magnitude = frequencies.map(w => 20 * Math.log10(1 / Math.sqrt(1 + Math.pow(w * system.tau, 2))));
                        
                        return {
                            label: system.label,
                            data: magnitude,
                            borderColor: system.color,
                            backgroundColor: system.color + '20',
                            fill: false,
                            tension: 0.4
                        };
                    });

                    new Chart(freqCtx, {
                        type: 'line',
                        data: {
                            labels: frequencies.map(f => f.toFixed(3)),
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Frequency Response (Bode Plot - Magnitude)'
                            }
                        },
                        scales: {
                            x: { 
                                title: { display: true, text: 'Frequency (rad/s)' },
                                type: 'logarithmic'
                            },
                            y: { title: { display: true, text: 'Magnitude (dB)' } }
                        }
                        }
                    });
                    console.log('Engineering frequency chart created successfully');
                    freqCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating engineering frequency chart:', error);
                    freqCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Engineering Frequency Chart failed to load</div>';
                    freqCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }

            // Control System Chart
            const controlCtx = document.getElementById('engineeringControlChart');
            if (controlCtx) {
                try {
                    const timeRange = Array.from({length: 301}, (_, i) => i * 0.1);
                    const setpoint = 1;
                    
                    // Simulate step response with different controllers
                const controllers = [
                    { Kp: 1, label: 'P Controller (Kp=1)', color: '#ff6b6b', type: 'proportional' },
                    { Kp: 5, label: 'P Controller (Kp=5)', color: '#4ecdc4', type: 'proportional' },
                    { Kp: 10, label: 'P Controller (Kp=10)', color: '#45b7d1', type: 'proportional' }
                ];

                const datasets = [];
                
                // Add setpoint reference
                datasets.push({
                    label: 'Setpoint',
                    data: timeRange.map(() => setpoint),
                    borderColor: '#333',
                    borderDash: [5, 5],
                    pointRadius: 0,
                    fill: false
                });

                // Add controller responses
                controllers.forEach(controller => {
                    const tau_cl = 1 / (1 + controller.Kp); // Closed-loop time constant
                    const ss_error = 1 / (1 + controller.Kp); // Steady-state error
                    const final_value = setpoint - ss_error;
                    
                    const data = timeRange.map(t => final_value * (1 - Math.exp(-t / tau_cl)));
                    
                    datasets.push({
                        label: controller.label,
                        data: data,
                        borderColor: controller.color,
                        backgroundColor: controller.color + '20',
                        fill: false,
                        tension: 0.4
                    });
                });

                new Chart(controlCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Closed-Loop Control System Response'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (s)' } },
                            y: { title: { display: true, text: 'Output' } }
                        }
                        }
                    });
                    console.log('Engineering control chart created successfully');
                    controlCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating engineering control chart:', error);
                    controlCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Engineering Control Chart failed to load</div>';
                    controlCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }

            // Heat Transfer Chart
            const heatCtx = document.getElementById('engineeringHeatChart');
            if (heatCtx) {
                try {
                    const timeRange = Array.from({length: 201}, (_, i) => i * 0.5); // 0 to 100 minutes
                    const scenarios = [
                    {
                        h: 10, A: 0.1, m: 0.5, cp: 500, T_amb: 25, Q_in: 100,
                        label: 'Constant Heat Input (100W)', color: '#ff6b6b'
                    },
                    {
                        h: 15, A: 0.1, m: 0.5, cp: 500, T_amb: 25, Q_in: 150,
                        label: 'Higher Heat Input (150W)', color: '#4ecdc4'
                    },
                    {
                        h: 5, A: 0.1, m: 0.5, cp: 500, T_amb: 25, Q_in: 100,
                        label: 'Poor Heat Transfer (h=5)', color: '#45b7d1'
                    },
                    {
                        h: 10, A: 0.2, m: 0.5, cp: 500, T_amb: 25, Q_in: 100,
                        label: 'Larger Surface Area', color: '#f7dc6f'
                    }
                ];

                const datasets = scenarios.map(scenario => {
                    // Heat transfer equation: mc_p(dT/dt) = Q_in - hA(T - T_amb)
                    // Steady state: T_ss = T_amb + Q_in/(hA)
                    // Time constant: τ = mc_p/(hA)
                    const T_ss = scenario.T_amb + scenario.Q_in / (scenario.h * scenario.A);
                    const tau = (scenario.m * scenario.cp) / (scenario.h * scenario.A);

                    const data = timeRange.map(t =>
                        T_ss + (scenario.T_amb - T_ss) * Math.exp(-t / tau)
                    );

                    return {
                        label: scenario.label,
                        data: data,
                        borderColor: scenario.color,
                        backgroundColor: scenario.color + '20',
                        fill: false,
                        tension: 0.4
                    };
                });

                // Add ambient temperature line
                datasets.push({
                    label: 'Ambient Temperature',
                    data: timeRange.map(() => 25),
                    borderColor: '#333',
                    borderDash: [5, 5],
                    pointRadius: 0,
                    fill: false
                });

                new Chart(heatCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Heat Transfer Response in Engineering Systems'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (minutes)' } },
                            y: { title: { display: true, text: 'Temperature (°C)' } }
                        }
                        }
                    });
                    console.log('Engineering heat chart created successfully');
                    heatCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating engineering heat chart:', error);
                    heatCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Engineering Heat Chart failed to load</div>';
                    heatCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }
        }

        function createPharmacologyCharts() {
            // IV Bolus Chart
            const ivCtx = document.getElementById('pharmacologyIVChart');
            if (ivCtx) {
                try {
                    const timeRange = Array.from({length: 121}, (_, i) => i); // 0 to 120 hours
                    const dose = 0.25; // mg
                    const Vd = 490; // L
                    const ke = 0.0103; // h^-1
                    const C0 = dose / Vd; // Initial concentration

                    const concentration = timeRange.map(t => C0 * Math.exp(-ke * t));

                    new Chart(ivCtx, {
                        type: 'line',
                        data: {
                            labels: timeRange,
                            datasets: [{
                                label: 'Digoxin Concentration',
                                data: concentration,
                                borderColor: '#10b981',
                                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                                fill: true,
                                tension: 0.4,
                                borderWidth: 3
                            }, {
                                label: 'Therapeutic Range (1-2 ng/mL)',
                                data: timeRange.map(() => 1.5),
                                borderColor: '#f59e0b',
                                borderDash: [5, 5],
                                fill: false,
                                borderWidth: 2
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                x: { title: { display: true, text: 'Time (hours)' } },
                                y: { title: { display: true, text: 'Concentration (ng/mL)' } }
                            },
                            plugins: {
                                title: { display: true, text: 'IV Bolus Pharmacokinetics' }
                            }
                        }
                    });
                    console.log('Pharmacology IV chart created successfully');
                    ivCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating pharmacology IV chart:', error);
                    ivCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Pharmacology IV Chart failed to load</div>';
                    ivCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }

            // Oral Absorption Chart
            const oralCtx = document.getElementById('pharmacologyOralChart');
            if (oralCtx) {
                try {
                    const timeRange = Array.from({length: 49}, (_, i) => i * 0.5); // 0 to 24 hours
                    const ka = 1.5; // absorption rate
                    const ke = 0.3; // elimination rate
                    const FD_Vd = 2; // F*D/Vd

                    const concentration = timeRange.map(t =>
                        (ka * FD_Vd / (ka - ke)) * (Math.exp(-ke * t) - Math.exp(-ka * t))
                    );

                    new Chart(oralCtx, {
                        type: 'line',
                        data: {
                            labels: timeRange,
                            datasets: [{
                                label: 'Oral Absorption Profile',
                                data: concentration,
                                borderColor: '#3b82f6',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                fill: true,
                                tension: 0.4,
                                borderWidth: 3
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                x: { title: { display: true, text: 'Time (hours)' } },
                                y: { title: { display: true, text: 'Concentration (mg/L)' } }
                            },
                            plugins: {
                                title: { display: true, text: 'Oral Absorption Kinetics' }
                            }
                        }
                    });
                    console.log('Pharmacology oral chart created successfully');
                    oralCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating pharmacology oral chart:', error);
                    oralCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Pharmacology Oral Chart failed to load</div>';
                    oralCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }

            // Multiple Dose Chart
            const multiCtx = document.getElementById('pharmacologyMultiDoseChart');
            if (multiCtx) {
                try {
                    const timeRange = Array.from({length: 169}, (_, i) => i); // 0 to 168 hours (1 week)
                    const tau = 24; // dosing interval (hours)
                    const ke = 0.0289; // elimination rate (h^-1)
                    const dose = 0.25;
                    const Vd = 490;

                    // Multiple dose accumulation
                    const concentration = timeRange.map(t => {
                        let C = 0;
                        const nDoses = Math.floor(t / tau) + 1;
                        for (let n = 0; n < nDoses; n++) {
                            const tSinceDose = t - n * tau;
                            if (tSinceDose >= 0) {
                                C += (dose / Vd) * Math.exp(-ke * tSinceDose);
                            }
                        }
                        return C;
                    });

                    new Chart(multiCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: [{
                            label: 'Multiple Dose Accumulation',
                            data: concentration,
                            borderColor: '#8b5cf6',
                            backgroundColor: 'rgba(139, 92, 246, 0.1)',
                            fill: true,
                            tension: 0.1,
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: { title: { display: true, text: 'Time (hours)' } },
                            y: { title: { display: true, text: 'Concentration (ng/mL)' } }
                        },
                        plugins: {
                            title: { display: true, text: 'Multiple Dosing to Steady State' }
                        }
                        }
                    });
                    console.log('Pharmacology multi-dose chart created successfully');
                    multiCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating pharmacology multi-dose chart:', error);
                    multiCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Pharmacology Multi-dose Chart failed to load</div>';
                    multiCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }
        }

        function createAdvancedCharts() {
            // Phase Line Analysis Chart
            const phaseCtx = document.getElementById('advancedPhaseChart');
            if (phaseCtx) {
                try {
                    const yRange = Array.from({length: 201}, (_, i) => (i - 100) / 10);
                
                // Example: dy/dt = y(y-2)(y+1)
                const dydt = yRange.map(y => y * (y - 2) * (y + 1));
                
                const datasets = [{
                    label: 'dy/dt = y(y-2)(y+1)',
                    data: dydt,
                    borderColor: '#ff6b6b',
                    backgroundColor: '#ff6b6b20',
                    fill: false,
                    tension: 0.4,
                    pointRadius: 0
                }];

                // Add equilibrium points
                const equilibria = [-1, 0, 2];
                equilibria.forEach((eq, index) => {
                    datasets.push({
                        label: `Equilibrium: y = ${eq}`,
                        data: yRange.map((y, i) => Math.abs(y - eq) < 0.1 ? 0 : null),
                        backgroundColor: index % 2 === 0 ? '#4ecdc4' : '#f7dc6f',
                        borderColor: index % 2 === 0 ? '#4ecdc4' : '#f7dc6f',
                        pointRadius: 8,
                        showLine: false,
                        pointStyle: 'circle'
                    });
                });

                new Chart(phaseCtx, {
                    type: 'line',
                    data: {
                        labels: yRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Phase Line Analysis: dy/dt vs y'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'y' } },
                            y: { 
                                title: { display: true, text: 'dy/dt' },
                                beginAtZero: true
                            }
                        }
                        }
                    });
                    console.log('Advanced phase chart created successfully');
                    phaseCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating advanced phase chart:', error);
                    phaseCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Advanced Phase Chart failed to load</div>';
                    phaseCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }

            // Stability Analysis Chart
            const stabilityCtx = document.getElementById('advancedStabilityChart');
            if (stabilityCtx) {
                try {
                    const timeRange = Array.from({length: 201}, (_, i) => i * 0.1);
                    
                    // Different initial conditions for dy/dt = y(y-2)(y+1)
                    const initialConditions = [
                        { y0: -2, label: 'y₀ = -2', color: '#ff6b6b' },
                        { y0: -0.5, label: 'y₀ = -0.5', color: '#4ecdc4' },
                        { y0: 0.5, label: 'y₀ = 0.5', color: '#45b7d1' },
                    { y0: 1.5, label: 'y₀ = 1.5', color: '#f7dc6f' },
                    { y0: 3, label: 'y₀ = 3', color: '#bb8fce' }
                ];

                const datasets = initialConditions.map(ic => {
                    // Approximate solution behavior based on equilibria
                    let data;
                    if (ic.y0 < -1) {
                        data = timeRange.map(t => -1 + (ic.y0 + 1) * Math.exp(-t));
                    } else if (ic.y0 > -1 && ic.y0 < 0) {
                        data = timeRange.map(t => -1 + (ic.y0 + 1) * Math.exp(t));
                    } else if (ic.y0 > 0 && ic.y0 < 2) {
                        data = timeRange.map(t => 0 + ic.y0 * Math.exp(-t));
                    } else {
                        data = timeRange.map(t => 2 + (ic.y0 - 2) * Math.exp(t));
                    }
                    
                    return {
                        label: ic.label,
                        data: data,
                        borderColor: ic.color,
                        backgroundColor: ic.color + '20',
                        fill: false,
                        tension: 0.4
                    };
                });

                // Add equilibrium lines
                [-1, 0, 2].forEach((eq, index) => {
                    datasets.push({
                        label: `Equilibrium y = ${eq}`,
                        data: timeRange.map(() => eq),
                        borderColor: '#333',
                        borderDash: [5, 5],
                        pointRadius: 0
                    });
                });

                new Chart(stabilityCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Solution Trajectories and Stability'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time' } },
                            y: { title: { display: true, text: 'y(t)' } }
                        }
                        }
                    });
                    console.log('Advanced stability chart created successfully');
                    stabilityCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating advanced stability chart:', error);
                    stabilityCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Advanced Stability Chart failed to load</div>';
                    stabilityCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }

            // Direction Field Chart
            const directionCtx = document.getElementById('advancedDirectionChart');
            if (directionCtx) {
                try {
                    // Create direction field for dy/dt = -2y + 1
                    const xRange = Array.from({length: 21}, (_, i) => i * 0.5);
                    const yRange = Array.from({length: 21}, (_, i) => (i - 10) * 0.2);
                    
                    const datasets = [];
                    
                    // Solution curves
                    const initialValues = [-1.5, -1, -0.5, 0, 0.5, 1, 1.5];
                    initialValues.forEach((y0, index) => {
                        const solution = xRange.map(t => 0.5 + (y0 - 0.5) * Math.exp(-2 * t));
                    datasets.push({
                        label: `y₀ = ${y0}`,
                        data: solution,
                        borderColor: `hsl(${index * 50}, 70%, 50%)`,
                        backgroundColor: `hsl(${index * 50}, 70%, 50%, 0.1)`,
                        fill: false,
                        tension: 0.4,
                        pointRadius: 0
                    });
                });

                // Equilibrium line
                datasets.push({
                    label: 'Equilibrium: y = 0.5',
                    data: xRange.map(() => 0.5),
                    borderColor: '#333',
                    borderDash: [5, 5],
                    pointRadius: 0
                });

                new Chart(directionCtx, {
                    type: 'line',
                    data: {
                        labels: xRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Direction Field and Solution Curves: dy/dt = -2y + 1'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (t)' } },
                            y: { title: { display: true, text: 'y(t)' } }
                        }
                        }
                    });
                    console.log('Advanced direction field chart created successfully');
                    directionCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating advanced direction field chart:', error);
                    directionCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Advanced Direction Field Chart failed to load</div>';
                    directionCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }

            // Bifurcation Chart
            const bifurcationCtx = document.getElementById('advancedBifurcationChart');
            if (bifurcationCtx) {
                try {
                    // Harvesting model bifurcation: dP/dt = rP(1-P/K) - H
                    const HRange = Array.from({length: 101}, (_, i) => i * 0.3);
                    const r = 1, K = 10;
                    const Hmax = r * K / 4;
                    
                    const datasets = [];
                    
                    // Stable and unstable equilibria
                const stableEq = [], unstableEq = [];
                HRange.forEach(H => {
                    if (H <= Hmax) {
                        const discriminant = r * r * K * K / 4 - 4 * r * H;
                        if (discriminant >= 0) {
                            const eq1 = (r * K + Math.sqrt(discriminant)) / (2 * r);
                            const eq2 = (r * K - Math.sqrt(discriminant)) / (2 * r);
                            stableEq.push(eq1);
                            unstableEq.push(eq2);
                        } else {
                            stableEq.push(null);
                            unstableEq.push(null);
                        }
                    } else {
                        stableEq.push(null);
                        unstableEq.push(null);
                    }
                });

                datasets.push({
                    label: 'Stable Equilibrium',
                    data: stableEq,
                    borderColor: '#4ecdc4',
                    backgroundColor: '#4ecdc4',
                    pointRadius: 2,
                    pointHoverRadius: 4,
                    showLine: true,
                    tension: 0.4
                });

                datasets.push({
                    label: 'Unstable Equilibrium',
                    data: unstableEq,
                    borderColor: '#ff6b6b',
                    backgroundColor: '#ff6b6b',
                    pointRadius: 2,
                    pointHoverRadius: 4,
                    showLine: true,
                    borderDash: [5, 5],
                    tension: 0.4
                });

                // Critical point
                datasets.push({
                    label: `Critical Point (H = ${Hmax.toFixed(2)})`,
                    data: HRange.map((H, i) => Math.abs(H - Hmax) < 0.1 ? K/2 : null),
                    backgroundColor: '#f7dc6f',
                    borderColor: '#f7dc6f',
                    pointRadius: 8,
                    showLine: false,
                    pointStyle: 'star'
                });

                new Chart(bifurcationCtx, {
                    type: 'line',
                    data: {
                        labels: HRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Bifurcation Diagram: Harvesting Model'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Harvest Rate (H)' } },
                            y: { title: { display: true, text: 'Population Equilibrium (P)' } }
                        }
                        }
                    });
                    console.log('Advanced bifurcation chart created successfully');
                    bifurcationCtx.closest('.chart-container')?.classList.add('loaded');
                } catch (error) {
                    console.error('Error creating advanced bifurcation chart:', error);
                    bifurcationCtx.parentElement.innerHTML = '<div class="text-center text-red-500 p-4">Advanced Bifurcation Chart failed to load</div>';
                    bifurcationCtx.closest('.chart-container')?.classList.add('load-error');
                }
            }
        }

        // Initialize all new charts when page loads
        function initializeAllCharts() {
            try {
                createEconomicCharts();
                console.log('Economic charts initialized successfully');
            } catch (error) {
                console.error('Error initializing economic charts:', error);
            }

            try {
                createPhysicsCharts();
                console.log('Physics charts initialized successfully');
            } catch (error) {
                console.error('Error initializing physics charts:', error);
            }

            try {
                createPharmacologyCharts();
                console.log('Pharmacology charts initialized successfully');
            } catch (error) {
                console.error('Error initializing pharmacology charts:', error);
            }

            try {
                createEngineeringCharts();
                console.log('Engineering charts initialized successfully');
            } catch (error) {
                console.error('Error initializing engineering charts:', error);
            }

            try {
                createAdvancedCharts();
                console.log('Advanced charts initialized successfully');
            } catch (error) {
                console.error('Error initializing advanced charts:', error);
            }
        }

        // Add interactivity to page elements
        function addInteractivity() {
            // Add hover effects to boxes
            const boxes = document.querySelectorAll('.definition-box, .theorem-box, .example-box, .application-box');
            boxes.forEach(box => {
                box.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                box.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Add click to expand functionality for code blocks
            const codeBlocks = document.querySelectorAll('.code-block');
            codeBlocks.forEach(block => {
                block.style.cursor = 'pointer';
                block.title = 'Click to toggle expansion';
                
                block.addEventListener('click', function() {
                    if (this.style.maxHeight && this.style.maxHeight !== 'none') {
                        this.style.maxHeight = 'none';
                        this.style.overflow = 'visible';
                    } else {
                        this.style.maxHeight = '300px';
                        this.style.overflow = 'auto';
                    }
                });
                
                // Initial state
                block.style.maxHeight = '300px';
                block.style.overflow = 'auto';
            });
        }

        // Chart update functions for dynamic content
        let mixingChartInstance = null;

        function updateMixingChart() {
            const volume = parseFloat(document.getElementById('volumeSlider').value);
            const flow = parseFloat(document.getElementById('flowSlider').value);
            const concentration = parseFloat(document.getElementById('concentrationSlider').value);

            // Update display values
            document.getElementById('volumeValue').textContent = volume + ' L';
            document.getElementById('flowValue').textContent = flow + ' L/min';
            document.getElementById('concentrationValue').textContent = concentration + ' kg/L';

            // Update current conditions
            document.getElementById('currentVolume').textContent = `Tank volume: ${volume} L`;
            document.getElementById('currentFlow').textContent = `Flow rate: ${flow} L/min`;
            document.getElementById('currentConcentration').textContent = `Inflow concentration: ${concentration} kg/L`;

            // Calculate parameters
            const timeConstant = volume / flow;
            const equilibriumTime = 5 * timeConstant;
            const halfTime = timeConstant * Math.log(2);

            document.getElementById('timeConstant').textContent = `Time constant: τ = ${timeConstant.toFixed(1)} min`;
            document.getElementById('equilibriumTime').textContent = `Equilibrium time: ~${equilibriumTime.toFixed(0)} min`;
            document.getElementById('halfTime').textContent = `Half-time: ~${halfTime.toFixed(1)} min`;

            // Generate new data
            const timePoints = Array.from({length: 501}, (_, i) => i);
            const concentrationData = timePoints.map(t => concentration * (1 - Math.exp(-t / timeConstant)));
            const equilibriumData = timePoints.map(t => concentration);

            // Update chart
            if (mixingChartInstance) {
                mixingChartInstance.data.datasets[0].data = concentrationData;
                mixingChartInstance.data.datasets[1].data = equilibriumData;
                mixingChartInstance.update('none'); // No animation for smooth updates
            }
        }

        function updateMixingChartOld(volumeParam, flowParam, concentrationParam) {
            const V = volumeParam || 1000;
            const r = flowParam || 10;
            const C_in = concentrationParam || 0.5;
            
            const newConcentration = timePoints.map(t => C_in * (1 - Math.exp(-r * t / V)));
            
            mixingChart.data.datasets[0].data = newConcentration;
            mixingChart.data.datasets[1].data = timePoints.map(t => C_in);
            mixingChart.update('active');
        }

        function updatePopulationChart(rParam, KParam, HParam) {
            if (!window.populationChart) {
                console.error('Population chart not initialized');
                return;
            }
            
            const newR = rParam || 0.1;
            const newK = KParam || 1000;
            const newH = HParam || 25;
            
            const newLogistic = window.timeRange.map(t => newK / (1 + (newK/window.P0 - 1) * Math.exp(-newR * t)));
            const newExponential = window.timeRange.map(t => window.P0 * Math.exp(newR * t));
            
            // Update harvesting model (simplified equilibrium approximation)
            const newHarvesting = window.timeRange.map(t => {
                const discriminant = 1 - 4*newH/(newR*newK);
                if (discriminant >= 0) {
                    const equilibrium = newK * (1 - Math.sqrt(discriminant));
                    if (equilibrium > 0) {
                        return equilibrium + (window.P0 - equilibrium) * Math.exp(-newR * (1 - 2*equilibrium/newK) * t);
                    }
                }
                return Math.max(0, window.P0 - newH * t); // Linear decline if unsustainable
            });
            
            window.populationChart.data.datasets[0].data = newExponential;
            window.populationChart.data.datasets[1].data = newLogistic;
            window.populationChart.data.datasets[2].data = newHarvesting;
            window.populationChart.data.datasets[3].data = window.timeRange.map(t => newK);
            window.populationChart.update('active');
        }

        // Population controls event listeners
        function initializePopulationControls() {
            const growthRateSlider = document.getElementById('growthRate');
            const carryingCapacitySlider = document.getElementById('carryingCapacity');
            const harvestRateSlider = document.getElementById('harvestRate');
            
            const growthRateValue = document.getElementById('growthRateValue');
            const carryingCapacityValue = document.getElementById('carryingCapacityValue');
            const harvestRateValue = document.getElementById('harvestRateValue');
            const maxHarvestDisplay = document.getElementById('maxHarvest');
            
            function updateParameters() {
                const r = parseFloat(growthRateSlider.value);
                const K = parseInt(carryingCapacitySlider.value);
                const H = parseInt(harvestRateSlider.value);
                
                // Update display values
                growthRateValue.textContent = r.toFixed(2);
                carryingCapacityValue.textContent = K.toString();
                harvestRateValue.textContent = H.toString();
                
                // Calculate and display maximum sustainable harvest
                const maxSustainableHarvest = (r * K) / 4;
                maxHarvestDisplay.textContent = maxSustainableHarvest.toFixed(1);
                
                // Update chart
                updatePopulationChart(r, K, H);
                
                // Update harvest rate color based on sustainability
                if (H > maxSustainableHarvest) {
                    harvestRateValue.className = 'font-bold text-red-600';
                    maxHarvestDisplay.parentElement.className = 'flex items-center justify-between text-sm mt-1 text-red-600 font-semibold';
                } else {
                    harvestRateValue.className = 'font-bold text-orange-600';
                    maxHarvestDisplay.parentElement.className = 'flex items-center justify-between text-sm mt-1';
                }
            }
            
            // Add event listeners
            growthRateSlider.addEventListener('input', updateParameters);
            carryingCapacitySlider.addEventListener('input', updateParameters);
            harvestRateSlider.addEventListener('input', updateParameters);
            
            // Initialize with default values
            updateParameters();
        }

        // Initialize population controls after page load
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a bit for charts to be fully initialized
            setTimeout(initializePopulationControls, 500);
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navigation highlighting
        function highlightActiveSection() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('nav a[href^="#"]');
            
            window.addEventListener('scroll', () => {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (pageYOffset >= sectionTop - 200) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('font-bold', 'text-blue-800');
                    if (link.getAttribute('href') === `#${current}`) {
                        link.classList.add('font-bold', 'text-blue-800');
                    }
                });
            });
        }

        // Loading state management
        function showLoadingState() {
            const chartContainers = document.querySelectorAll('.chart-container');
            chartContainers.forEach(container => {
                if (!container.querySelector('canvas')) return;

                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'loading-overlay';
                loadingDiv.innerHTML = `
                    <div class="flex items-center justify-center h-full">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <span class="ml-2 text-gray-600">Loading chart...</span>
                    </div>
                `;
                loadingDiv.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.9);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10;
                `;

                container.style.position = 'relative';
                container.appendChild(loadingDiv);
            });
        }

        function hideLoadingState() {
            const loadingOverlays = document.querySelectorAll('.loading-overlay');
            loadingOverlays.forEach(overlay => overlay.remove());
        }

        // Helper function to mark individual chart as loaded
        function markChartAsLoaded(chartId) {
            const container = document.getElementById(chartId)?.closest('.chart-container');
            if (container) {
                container.classList.add('loaded');
                console.log(`Chart ${chartId} marked as loaded`);
            }
        }

        // Function to check chart loading status
        function checkChartLoadingStatus() {
            const allChartContainers = document.querySelectorAll('.chart-container, .large-chart');
            const loadedCharts = document.querySelectorAll('.chart-container.loaded, .large-chart.loaded');
            const errorCharts = document.querySelectorAll('.chart-container.load-error, .large-chart.load-error');

            console.log(`Chart Loading Status:
                Total chart containers: ${allChartContainers.length}
                Successfully loaded: ${loadedCharts.length}
                Failed to load: ${errorCharts.length}
                Still loading: ${allChartContainers.length - loadedCharts.length - errorCharts.length}`);

            if (errorCharts.length > 0) {
                console.warn('Charts with errors:', Array.from(errorCharts).map(c => c.querySelector('canvas')?.id || 'unknown'));
            }

            return {
                total: allChartContainers.length,
                loaded: loadedCharts.length,
                errors: errorCharts.length,
                loading: allChartContainers.length - loadedCharts.length - errorCharts.length
            };
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing charts...');
            showLoadingState();

            // Use setTimeout to allow UI to update
            setTimeout(() => {
                // Initialize all charts with better error handling
                try {
                    // Initialize main charts first (classification, mixing, population)
                    initializeMainCharts();
                    console.log('Main charts initialized successfully');

                    // Then initialize additional charts
                    initializeAllCharts();
                    console.log('All charts initialization completed');
                } catch (error) {
                    console.error('Critical error during chart initialization:', error);
                    // Show global error message
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
                    errorDiv.innerHTML = `
                        <strong>Chart Loading Error:</strong><br>
                        Some visualizations may not display correctly. Please refresh the page.
                        <button onclick="this.parentElement.remove()" class="ml-2 font-bold">&times;</button>
                    `;
                    document.body.appendChild(errorDiv);
                }
                
                hideLoadingState();
                addInteractivity();
                highlightActiveSection();

                // Check chart loading status after a brief delay
                setTimeout(() => {
                    checkChartLoadingStatus();
                }, 1000);
            }, 100);

            // Enhanced error handling for charts
            window.addEventListener('error', function(e) {
                if (e.message.includes('Chart') || e.filename?.includes('chart')) {
                    console.warn('Chart rendering issue detected:', e.message);
                    // More gentle recovery - no automatic reload
                    const errorNotification = document.createElement('div');
                    errorNotification.className = 'fixed bottom-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded z-50 max-w-sm';
                    errorNotification.innerHTML = `
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-triangle mr-2 mt-1"></i>
                            <div>
                                <strong>Visualization Issue</strong><br>
                                Some charts may not display correctly.
                                <button onclick="location.reload()" class="ml-2 text-blue-600 underline">Refresh page</button>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 font-bold">&times;</button>
                        </div>
                    `;
                    document.body.appendChild(errorNotification);
                    
                    // Auto-remove notification after 10 seconds
                    setTimeout(() => {
                        if (errorNotification.parentElement) {
                            errorNotification.remove();
                        }
                    }, 10000);
                }
            });

            // Smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });

        // Expose update functions globally for potential external control
        window.chartUpdaters = {
            updateMixingChart,
            updatePopulationChart
        };

        // Expose debugging functions globally
        window.chartDebug = {
            checkStatus: checkChartLoadingStatus,
            reinitialize: function() {
                console.log('Reinitializing charts...');
                try {
                    initializeMainCharts();
                    initializeAllCharts();
                    setTimeout(checkChartLoadingStatus, 1000);
                } catch (error) {
                    console.error('Error during reinitialization:', error);
                }
            }
        };

        // Print-friendly chart settings
        window.addEventListener('beforeprint', function() {
            if (typeof Chart !== 'undefined' && Chart.helpers) {
                Chart.helpers.each(Chart.instances, function(chart) {
                    chart.resize();
                });
            }
        });
    </script>
</body>
</html>