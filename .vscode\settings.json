{"latex-workshop.latex.tools": [{"name": "pdflatex", "command": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\MiKTeX\\miktex\\bin\\x64\\pdflatex.exe", "args": ["-synctex=1", "-interaction=nonstopmode", "-file-line-error", "%DOCFILE%"]}, {"name": "bibtex", "command": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\MiKTeX\\miktex\\bin\\x64\\bibtex.exe", "args": ["%DOCFILE%"]}], "latex-workshop.latex.recipes": [{"name": "pdfLaTeX", "tools": ["pdflatex"]}, {"name": "pdfLaTeX ➞ pdfLaTeX", "tools": ["pdflatex", "pdflatex"]}, {"name": "pdfLaTeX ➞ BibTeX ➞ pdfLaTeX ➞ pdfLaTeX", "tools": ["pdflatex", "bibtex", "pdflatex", "pdflatex"]}], "latex-workshop.latex.recipe.default": "pdfLaTeX ➞ pdfLaTeX", "latex-workshop.latex.autoBuild.run": "never", "latex-workshop.latex.autoClean.run": "onBuilt", "latex-workshop.view.pdf.viewer": "tab", "latex-workshop.latex.outDir": "./", "latex-workshop.latex.clean.fileTypes": ["*.aux", "*.bbl", "*.blg", "*.idx", "*.ind", "*.lof", "*.lot", "*.out", "*.toc", "*.acn", "*.acr", "*.alg", "*.glg", "*.glo", "*.gls", "*.fls", "*.log", "*.fdb_latexmk", "*.snm", "*.nav", "*.vrb"]}