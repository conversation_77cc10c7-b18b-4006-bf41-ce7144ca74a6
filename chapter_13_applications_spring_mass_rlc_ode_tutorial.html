<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 13: Applications - Spring-Mass-Damper Systems and RLC Circuits - ODE Tutorial</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            }
        };
    </script>
    <style>
        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.375rem;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .python-code { border-left-color: #3776ab; }
        .r-code { border-left-color: #276dc3; }
        .math-box {
            background-color: #f0f8ff;
            border: 1px solid #b0d4f1;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
        }
        .highlight-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.375rem;
        }
        .info-box {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.375rem;
        }
        .warning-box {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.375rem;
        }
        .success-box {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.375rem;
        }
        table { border-collapse: collapse; width: 100%; margin: 1rem 0; }
        th, td { border: 1px solid #ddd; padding: 0.75rem; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .chart-container { height: 400px; margin: 2rem 0; }
        .section-divider { border-top: 3px solid #007bff; margin: 3rem 0 2rem 0; padding-top: 2rem; }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 leading-relaxed">
    <div class="container mx-auto max-w-6xl px-4 py-8">
        
        <!-- Header -->
        <header class="text-center mb-12 bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8 rounded-lg shadow-lg">
            <h1 class="text-4xl font-bold mb-4">📘 Chapter 13: Applications</h1>
            <h2 class="text-2xl mb-4">Spring-Mass-Damper Systems and RLC Circuits</h2>
            <p class="text-lg opacity-90">Comprehensive Real-World Applications of Second-Order Linear ODEs</p>
            <div class="mt-6 flex justify-center space-x-6 text-sm">
                <span><i class="fas fa-cogs mr-2"></i>Engineering Applications</span>
                <span><i class="fas fa-wave-square mr-2"></i>Oscillation Analysis</span>
                <span><i class="fas fa-code mr-2"></i>Python & R Implementation</span>
            </div>
        </header>

        <!-- Table of Contents -->
        <nav class="bg-white p-6 rounded-lg shadow-md mb-8">
            <h3 class="text-xl font-bold mb-4 text-blue-600"><i class="fas fa-list mr-2"></i>Chapter Contents</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <h4 class="font-semibold mb-2">Part I: Spring-Mass-Damper Systems</h4>
                    <ul class="space-y-1 text-gray-600">
                        <li>• Physical Modeling from First Principles</li>
                        <li>• Newton's Second Law Application</li>
                        <li>• Damping Mechanisms and Types</li>
                        <li>• Free Response Analysis</li>
                        <li>• Forced Response and Resonance</li>
                        <li>• Energy Analysis and Conservation</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-2">Part II: RLC Electrical Circuits</h4>
                    <ul class="space-y-1 text-gray-600">
                        <li>• Kirchhoff's Laws Application</li>
                        <li>• Impedance and Admittance</li>
                        <li>• AC Steady-State Analysis</li>
                        <li>• Frequency Response Functions</li>
                        <li>• Filter Design Applications</li>
                        <li>• Power and Energy Considerations</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-2">Part III: Advanced Topics</h4>
                    <ul class="space-y-1 text-gray-600">
                        <li>• Multi-Degree-of-Freedom Systems</li>
                        <li>• Coupled Oscillators</li>
                        <li>• Nonlinear Effects</li>
                        <li>• Parameter Estimation</li>
                        <li>• Experimental Validation</li>
                        <li>• Design Optimization</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-2">Part IV: Computational Methods</h4>
                    <ul class="space-y-1 text-gray-600">
                        <li>• Python Implementation</li>
                        <li>• R Statistical Analysis</li>
                        <li>• Simulation and Visualization</li>
                        <li>• Frequency Domain Analysis</li>
                        <li>• Interactive Examples</li>
                        <li>• Engineering Case Studies</li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Introduction -->
        <section class="bg-white p-8 rounded-lg shadow-md mb-8">
            <h2 class="text-3xl font-bold mb-6 text-blue-600"><i class="fas fa-rocket mr-3"></i>Introduction</h2>
            
            <p class="text-lg mb-6">
                This chapter represents the culmination of our study of second-order linear differential equations by exploring two fundamental engineering systems that exemplify the power and elegance of ODE theory: <strong>spring-mass-damper mechanical systems</strong> and <strong>RLC electrical circuits</strong>. These applications demonstrate how abstract mathematical concepts translate directly into practical engineering solutions.
            </p>

            <div class="highlight-box">
                <h4 class="font-bold mb-3"><i class="fas fa-lightbulb mr-2"></i>Why These Applications Matter</h4>
                <ul class="space-y-2">
                    <li><strong>Universal Analogies:</strong> Spring-mass-damper and RLC circuits are mathematically equivalent, demonstrating the universality of differential equations across disciplines</li>
                    <li><strong>Engineering Foundation:</strong> These systems form the basis for understanding vibrations, control systems, signal processing, and circuit design</li>
                    <li><strong>Design Insight:</strong> Mathematical analysis directly informs practical design decisions in automotive, aerospace, electronics, and structural engineering</li>
                    <li><strong>Physical Intuition:</strong> Abstract mathematical concepts like resonance, damping, and stability gain concrete physical meaning</li>
                </ul>
            </div>

            <div class="math-box">
                <h4 class="font-bold mb-3">Mathematical Framework</h4>
                <p class="mb-4">Both systems are governed by the same fundamental equation structure:</p>
                <p class="text-center text-lg">$$a\frac{d^2y}{dt^2} + b\frac{dy}{dt} + cy = f(t)$$</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div>
                        <h5 class="font-semibold mb-2">Spring-Mass-Damper:</h5>
                        <p>$$m\frac{d^2x}{dt^2} + c\frac{dx}{dt} + kx = F(t)$$</p>
                        <ul class="text-sm mt-2">
                            <li>• $m$: mass (inertia)</li>
                            <li>• $c$: damping coefficient</li>
                            <li>• $k$: spring stiffness</li>
                            <li>• $F(t)$: external force</li>
                        </ul>
                    </div>
                    <div>
                        <h5 class="font-semibold mb-2">RLC Circuit:</h5>
                        <p>$$L\frac{d^2q}{dt^2} + R\frac{dq}{dt} + \frac{q}{C} = V(t)$$</p>
                        <ul class="text-sm mt-2">
                            <li>• $L$: inductance (inertia)</li>
                            <li>• $R$: resistance (damping)</li>
                            <li>• $C$: capacitance (compliance)</li>
                            <li>• $V(t)$: external voltage</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Part I: Spring-Mass-Damper Systems -->
        <section class="section-divider">
            <h2 class="text-4xl font-bold mb-8 text-purple-600"><i class="fas fa-cogs mr-3"></i>Part I: Spring-Mass-Damper Systems</h2>

            <!-- Physical Modeling -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600"><i class="fas fa-atom mr-2"></i>1.1 Physical Modeling from First Principles</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-semibold mb-4">System Components</h4>
                        <div class="info-box">
                            <h5 class="font-bold mb-3">Mass (m)</h5>
                            <p class="mb-2"><strong>Physical Property:</strong> Inertia - resistance to acceleration</p>
                            <p class="mb-2"><strong>Mathematical Role:</strong> Coefficient of $\frac{d^2x}{dt^2}$ term</p>
                            <p><strong>Units:</strong> kg (SI), slug (Imperial)</p>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Spring (k)</h5>
                            <p class="mb-2"><strong>Physical Property:</strong> Elasticity - restoring force proportional to displacement</p>
                            <p class="mb-2"><strong>Hooke's Law:</strong> $F_s = -kx$</p>
                            <p><strong>Units:</strong> N/m (SI), lb/ft (Imperial)</p>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Damper (c)</h5>
                            <p class="mb-2"><strong>Physical Property:</strong> Viscous resistance - force proportional to velocity</p>
                            <p class="mb-2"><strong>Damping Law:</strong> $F_d = -c\frac{dx}{dt}$</p>
                            <p><strong>Units:</strong> N⋅s/m (SI), lb⋅s/ft (Imperial)</p>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-xl font-semibold mb-4">Free Body Diagram Analysis</h4>
                        <div class="bg-gray-100 p-6 rounded-lg">
                            <h5 class="font-bold mb-3">Forces Acting on Mass:</h5>
                            <ul class="space-y-2">
                                <li><strong>Spring Force:</strong> $F_s = -kx$ (restoring)</li>
                                <li><strong>Damping Force:</strong> $F_d = -c\dot{x}$ (dissipative)</li>
                                <li><strong>External Force:</strong> $F(t)$ (driving)</li>
                                <li><strong>Inertial Force:</strong> $F_i = m\ddot{x}$ (D'Alembert principle)</li>
                            </ul>
                        </div>

                        <div class="math-box mt-6">
                            <h5 class="font-bold mb-3">Newton's Second Law Application:</h5>
                            <p class="mb-2">$\sum F = ma$</p>
                            <p class="mb-2">$F(t) - kx - c\dot{x} = m\ddot{x}$</p>
                            <p class="mb-4">Rearranging to standard form:</p>
                            <p class="text-center text-lg font-bold">$$m\ddot{x} + c\dot{x} + kx = F(t)$$</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Damping Types -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600"><i class="fas fa-wave-square mr-2"></i>1.2 Damping Mechanisms and Classification</h3>
                
                <div class="math-box">
                    <h4 class="font-bold mb-4">Characteristic Equation Analysis:</h4>
                    <p class="mb-2">For the homogeneous equation $m\ddot{x} + c\dot{x} + kx = 0$:</p>
                    <p class="mb-2">Characteristic equation: $mr^2 + cr + k = 0$</p>
                    <p class="mb-4">Discriminant: $\Delta = c^2 - 4mk$</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                        <div class="bg-green-50 p-4 rounded">
                            <h5 class="font-bold text-green-700 mb-2">Underdamped ($\Delta < 0$)</h5>
                            <p class="text-sm mb-2">$c < 2\sqrt{mk}$</p>
                            <p class="text-sm">Oscillatory decay</p>
                        </div>
                        <div class="bg-blue-50 p-4 rounded">
                            <h5 class="font-bold text-blue-700 mb-2">Critically Damped ($\Delta = 0$)</h5>
                            <p class="text-sm mb-2">$c = 2\sqrt{mk}$</p>
                            <p class="text-sm">Fastest return to equilibrium</p>
                        </div>
                        <div class="bg-red-50 p-4 rounded">
                            <h5 class="font-bold text-red-700 mb-2">Overdamped ($\Delta > 0$)</h5>
                            <p class="text-sm mb-2">$c > 2\sqrt{mk}$</p>
                            <p class="text-sm">Exponential approach</p>
                        </div>
                    </div>
                </div>

                <div class="mt-8">
                    <h4 class="text-xl font-semibold mb-4">Damping Parameters</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="info-box">
                            <h5 class="font-bold mb-3">Natural Frequency</h5>
                            <p class="mb-2">$$\omega_n = \sqrt{\frac{k}{m}}$$</p>
                            <p class="text-sm">Frequency of undamped oscillation</p>
                        </div>
                        <div class="info-box">
                            <h5 class="font-bold mb-3">Damping Ratio</h5>
                            <p class="mb-2">$$\zeta = \frac{c}{2\sqrt{mk}} = \frac{c}{2m\omega_n}$$</p>
                            <p class="text-sm">Dimensionless damping parameter</p>
                        </div>
                        <div class="info-box">
                            <h5 class="font-bold mb-3">Damped Frequency</h5>
                            <p class="mb-2">$$\omega_d = \omega_n\sqrt{1-\zeta^2}$$</p>
                            <p class="text-sm">Frequency of damped oscillation</p>
                        </div>
                        <div class="info-box">
                            <h5 class="font-bold mb-3">Critical Damping</h5>
                            <p class="mb-2">$$c_{cr} = 2\sqrt{mk} = 2m\omega_n$$</p>
                            <p class="text-sm">Threshold between oscillatory and non-oscillatory</p>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <canvas id="dampingChart"></canvas>
                </div>
            </div>

            <!-- Free Response Analysis -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600"><i class="fas fa-chart-line mr-2"></i>1.3 Free Response Analysis</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-semibold mb-4">Solution Forms by Damping Type</h4>
                        
                        <div class="math-box">
                            <h5 class="font-bold mb-3 text-green-700">Underdamped ($\zeta < 1$):</h5>
                            <p class="mb-2">$$x(t) = e^{-\zeta\omega_n t}(A\cos(\omega_d t) + B\sin(\omega_d t))$$</p>
                            <p class="mb-2">Or in amplitude-phase form:</p>
                            <p>$$x(t) = Ce^{-\zeta\omega_n t}\cos(\omega_d t - \phi)$$</p>
                            <p class="text-sm mt-2">Where $C = \sqrt{A^2 + B^2}$ and $\phi = \tan^{-1}(B/A)$</p>
                        </div>

                        <div class="math-box">
                            <h5 class="font-bold mb-3 text-blue-700">Critically Damped ($\zeta = 1$):</h5>
                            <p>$$x(t) = (A + Bt)e^{-\omega_n t}$$</p>
                            <p class="text-sm mt-2">Fastest return to equilibrium without overshoot</p>
                        </div>

                        <div class="math-box">
                            <h5 class="font-bold mb-3 text-red-700">Overdamped ($\zeta > 1$):</h5>
                            <p class="mb-2">$$x(t) = Ae^{r_1 t} + Be^{r_2 t}$$</p>
                            <p class="mb-2">Where:</p>
                            <p>$$r_{1,2} = -\zeta\omega_n \pm \omega_n\sqrt{\zeta^2 - 1}$$</p>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-xl font-semibold mb-4">Performance Characteristics</h4>
                        
                        <div class="info-box">
                            <h5 class="font-bold mb-3">Logarithmic Decrement (Underdamped)</h5>
                            <p class="mb-2">$$\delta = \ln\left(\frac{x_n}{x_{n+1}}\right) = \frac{2\pi\zeta}{\sqrt{1-\zeta^2}}$$</p>
                            <p class="text-sm">Measure of decay rate between successive peaks</p>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Settling Time</h5>
                            <p class="mb-2">$$t_s \approx \frac{4}{\zeta\omega_n}$$ (2% criterion)</p>
                            <p class="text-sm">Time to reach within 2% of final value</p>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Overshoot (Underdamped)</h5>
                            <p class="mb-2">$$M_p = e^{-\pi\zeta/\sqrt{1-\zeta^2}}$$</p>
                            <p class="text-sm">Maximum overshoot as fraction of step input</p>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Peak Time (Underdamped)</h5>
                            <p class="mb-2">$$t_p = \frac{\pi}{\omega_d} = \frac{\pi}{\omega_n\sqrt{1-\zeta^2}}$$</p>
                            <p class="text-sm">Time to first maximum</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Python Implementation -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600"><i class="fab fa-python mr-2"></i>1.4 Python Implementation - Spring-Mass-Damper Analysis</h3>
                
                <div class="code-block python-code">
import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp
import sympy as sp
from sympy import symbols, dsolve, Eq, Function, I, exp, cos, sin, sqrt, pi

# Define symbolic variables
t, s = symbols('t s')
x = Function('x')
m, c, k, omega_n, zeta = symbols('m c k omega_n zeta', positive=True)

class SpringMassDamper:
    """
    Complete analysis of spring-mass-damper systems
    """
    
    def __init__(self, mass, damping, stiffness):
        self.m = mass          # Mass (kg)
        self.c = damping       # Damping coefficient (N⋅s/m)
        self.k = stiffness     # Spring stiffness (N/m)
        
        # Calculate system parameters
        self.omega_n = np.sqrt(self.k / self.m)  # Natural frequency
        self.zeta = self.c / (2 * np.sqrt(self.m * self.k))  # Damping ratio
        self.c_cr = 2 * np.sqrt(self.m * self.k)  # Critical damping
        
        # Determine damping type
        if self.zeta < 1:
            self.damping_type = "Underdamped"
            self.omega_d = self.omega_n * np.sqrt(1 - self.zeta**2)
        elif self.zeta == 1:
            self.damping_type = "Critically Damped"
            self.omega_d = 0
        else:
            self.damping_type = "Overdamped"
            self.omega_d = 0
    
    def characteristic_roots(self):
        """Calculate characteristic equation roots"""
        discriminant = self.c**2 - 4*self.m*self.k
        r1 = (-self.c + np.sqrt(discriminant)) / (2*self.m)
        r2 = (-self.c - np.sqrt(discriminant)) / (2*self.m)
        return r1, r2
    
    def free_response(self, x0, v0, t_span, t_eval=None):
        """
        Calculate free response for given initial conditions
        x0: initial displacement
        v0: initial velocity
        """
        if t_eval is None:
            t_eval = np.linspace(0, t_span, 1000)
        
        def system_ode(t, y):
            x, x_dot = y
            x_ddot = (-self.c * x_dot - self.k * x) / self.m
            return [x_dot, x_ddot]
        
        # Solve ODE
        sol = solve_ivp(system_ode, [0, t_span], [x0, v0], 
                       t_eval=t_eval, method='RK45', rtol=1e-8)
        
        return sol.t, sol.y[0], sol.y[1]  # time, displacement, velocity
    
    def analytical_free_response(self, x0, v0, t_array):
        """Analytical solution for free response"""
        if self.damping_type == "Underdamped":
            # Calculate constants from initial conditions
            A = x0
            B = (v0 + self.zeta * self.omega_n * x0) / self.omega_d
            
            # Underdamped solution
            envelope = np.exp(-self.zeta * self.omega_n * t_array)
            x_t = envelope * (A * np.cos(self.omega_d * t_array) + 
                             B * np.sin(self.omega_d * t_array))
            
        elif self.damping_type == "Critically Damped":
            # Calculate constants
            A = x0
            B = v0 + self.omega_n * x0
            
            # Critically damped solution
            x_t = (A + B * t_array) * np.exp(-self.omega_n * t_array)
            
        else:  # Overdamped
            r1, r2 = self.characteristic_roots()
            # Calculate constants from initial conditions
            A = (v0 - r2 * x0) / (r1 - r2)
            B = (r1 * x0 - v0) / (r1 - r2)
            
            # Overdamped solution
            x_t = A * np.exp(r1 * t_array) + B * np.exp(r2 * t_array)
        
        return x_t
    
    def forced_response(self, force_func, t_span, x0=0, v0=0, t_eval=None):
        """Calculate forced response to external force"""
        if t_eval is None:
            t_eval = np.linspace(0, t_span, 1000)
        
        def system_ode_forced(t, y):
            x, x_dot = y
            F_t = force_func(t)
            x_ddot = (F_t - self.c * x_dot - self.k * x) / self.m
            return [x_dot, x_ddot]
        
        sol = solve_ivp(system_ode_forced, [0, t_span], [x0, v0], 
                       t_eval=t_eval, method='RK45', rtol=1e-8)
        
        return sol.t, sol.y[0], sol.y[1]
    
    def frequency_response(self, freq_range):
        """Calculate frequency response function"""
        omega = 2 * np.pi * freq_range
        H = 1 / (self.k * (1 - (omega/self.omega_n)**2 + 
                          2j * self.zeta * (omega/self.omega_n)))
        
        magnitude = np.abs(H)
        phase = np.angle(H) * 180 / np.pi
        
        return magnitude, phase
    
    def plot_free_response_comparison(self, x0=1.0, v0=0.0, t_max=10):
        """Plot comparison of different damping types"""
        t = np.linspace(0, t_max, 1000)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Spring-Mass-Damper Free Response Comparison', fontsize=16)
        
        # Different damping scenarios
        scenarios = [
            {"zeta": 0.1, "name": "Underdamped (ζ=0.1)", "color": "blue"},
            {"zeta": 0.5, "name": "Underdamped (ζ=0.5)", "color": "green"},
            {"zeta": 1.0, "name": "Critically Damped (ζ=1.0)", "color": "red"},
            {"zeta": 2.0, "name": "Overdamped (ζ=2.0)", "color": "purple"}
        ]
        
        for i, scenario in enumerate(scenarios):
            # Create system with specified damping ratio
            c_temp = scenario["zeta"] * 2 * np.sqrt(self.m * self.k)
            system_temp = SpringMassDamper(self.m, c_temp, self.k)
            
            # Calculate response
            x_analytical = system_temp.analytical_free_response(x0, v0, t)
            
            # Plot
            row, col = i // 2, i % 2
            axes[row, col].plot(t, x_analytical, color=scenario["color"], 
                              linewidth=2, label=scenario["name"])
            axes[row, col].set_xlabel('Time (s)')
            axes[row, col].set_ylabel('Displacement (m)')
            axes[row, col].grid(True, alpha=0.3)
            axes[row, col].legend()
            axes[row, col].set_title(scenario["name"])
        
        plt.tight_layout()
        return fig
    
    def plot_frequency_response(self, freq_max=5):
        """Plot frequency response (Bode plot)"""
        freq = np.logspace(-2, np.log10(freq_max), 1000)
        magnitude, phase = self.frequency_response(freq)
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        fig.suptitle(f'Frequency Response (ζ = {self.zeta:.2f})', fontsize=16)
        
        # Magnitude plot
        ax1.loglog(freq/self.omega_n*(2*np.pi), magnitude)
        ax1.set_ylabel('Magnitude |H(jω)|')
        ax1.grid(True, which="both", alpha=0.3)
        ax1.axvline(x=1, color='red', linestyle='--', alpha=0.7, label='ωₙ')
        ax1.legend()
        
        # Phase plot
        ax2.semilogx(freq/self.omega_n*(2*np.pi), phase)
        ax2.set_xlabel('Frequency Ratio (ω/ωₙ)')
        ax2.set_ylabel('Phase (degrees)')
        ax2.grid(True, which="both", alpha=0.3)
        ax2.axvline(x=1, color='red', linestyle='--', alpha=0.7, label='ωₙ')
        ax2.legend()
        
        plt.tight_layout()
        return fig
    
    def system_info(self):
        """Display system information"""
        print(f"Spring-Mass-Damper System Analysis")
        print(f"{'='*40}")
        print(f"Mass (m): {self.m:.3f} kg")
        print(f"Damping (c): {self.c:.3f} N⋅s/m")
        print(f"Stiffness (k): {self.k:.3f} N/m")
        print(f"Natural Frequency (ωₙ): {self.omega_n:.3f} rad/s")
        print(f"Damping Ratio (ζ): {self.zeta:.3f}")
        print(f"Critical Damping (cᶜʳ): {self.c_cr:.3f} N⋅s/m")
        print(f"Damping Type: {self.damping_type}")
        if self.damping_type == "Underdamped":
            print(f"Damped Frequency (ωd): {self.omega_d:.3f} rad/s")
            print(f"Period of Oscillation: {2*np.pi/self.omega_d:.3f} s")

# Example usage and analysis
if __name__ == "__main__":
    # Create system: m=1kg, c=0.5N⋅s/m, k=4N/m
    system = SpringMassDamper(mass=1.0, damping=0.5, stiffness=4.0)
    system.system_info()
    
    # Plot comparisons
    fig1 = system.plot_free_response_comparison()
    fig2 = system.plot_frequency_response()
    
    plt.show()
                </div>

                <div class="success-box">
                    <h5 class="font-bold mb-2"><i class="fas fa-check-circle mr-2"></i>Key Features of Python Implementation:</h5>
                    <ul class="space-y-1">
                        <li>• Complete analytical and numerical solution methods</li>
                        <li>• Automatic classification of damping types</li>
                        <li>• Frequency response analysis with Bode plots</li>
                        <li>• Comparative visualization of different damping scenarios</li>
                        <li>• Parameter calculation and system characterization</li>
                    </ul>
                </div>
            </div>

            <!-- R Implementation -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600"><i class="fab fa-r-project mr-2"></i>1.5 R Implementation - Statistical Analysis and Visualization</h3>
                
                <div class="code-block r-code">
# Spring-Mass-Damper System Analysis in R
library(deSolve)    # For ODE solving
library(ggplot2)    # For advanced plotting
library(plotly)     # For interactive plots
library(dplyr)      # For data manipulation
library(tidyr)      # For data reshaping
library(signal)     # For signal processing

# Define SpringMassDamper class using R6
library(R6)

SpringMassDamperR6 <- R6Class("SpringMassDamper",
  public = list(
    # System parameters
    m = NULL,
    c = NULL, 
    k = NULL,
    omega_n = NULL,
    zeta = NULL,
    c_cr = NULL,
    damping_type = NULL,
    omega_d = NULL,
    
    # Constructor
    initialize = function(mass, damping, stiffness) {
      self$m <- mass
      self$c <- damping
      self$k <- stiffness
      
      # Calculate system parameters
      self$omega_n <- sqrt(self$k / self$m)
      self$zeta <- self$c / (2 * sqrt(self$m * self$k))
      self$c_cr <- 2 * sqrt(self$m * self$k)
      
      # Determine damping type
      if (self$zeta < 1) {
        self$damping_type <- "Underdamped"
        self$omega_d <- self$omega_n * sqrt(1 - self$zeta^2)
      } else if (abs(self$zeta - 1) < 1e-10) {
        self$damping_type <- "Critically Damped"
        self$omega_d <- 0
      } else {
        self$damping_type <- "Overdamped"
        self$omega_d <- 0
      }
    },
    
    # Free response using deSolve
    free_response = function(x0, v0, t_span, dt = 0.01) {
      # Define system of ODEs
      system_ode <- function(t, state, parameters) {
        x <- state[1]
        x_dot <- state[2]
        
        x_ddot <- (-parameters$c * x_dot - parameters$k * x) / parameters$m
        
        return(list(c(x_dot, x_ddot)))
      }
      
      # Time vector
      times <- seq(0, t_span, by = dt)
      
      # Parameters
      params <- list(m = self$m, c = self$c, k = self$k)
      
      # Solve ODE
      result <- ode(y = c(x0, v0), times = times, func = system_ode, 
                    parms = params, method = "rk4")
      
      # Return as data frame
      return(data.frame(
        time = result[, 1],
        displacement = result[, 2],
        velocity = result[, 3]
      ))
    },
    
    # Analytical free response
    analytical_free_response = function(x0, v0, t_array) {
      if (self$damping_type == "Underdamped") {
        # Constants from initial conditions
        A <- x0
        B <- (v0 + self$zeta * self$omega_n * x0) / self$omega_d
        
        # Solution
        envelope <- exp(-self$zeta * self$omega_n * t_array)
        x_t <- envelope * (A * cos(self$omega_d * t_array) + 
                          B * sin(self$omega_d * t_array))
        
      } else if (self$damping_type == "Critically Damped") {
        A <- x0
        B <- v0 + self$omega_n * x0
        x_t <- (A + B * t_array) * exp(-self$omega_n * t_array)
        
      } else { # Overdamped
        r1 <- (-self$c + sqrt(self$c^2 - 4*self$m*self$k)) / (2*self$m)
        r2 <- (-self$c - sqrt(self$c^2 - 4*self$m*self$k)) / (2*self$m)
        
        A <- (v0 - r2 * x0) / (r1 - r2)
        B <- (r1 * x0 - v0) / (r1 - r2)
        
        x_t <- A * exp(r1 * t_array) + B * exp(r2 * t_array)
      }
      
      return(x_t)
    },
    
    # Frequency response
    frequency_response = function(freq_range) {
      omega <- 2 * pi * freq_range
      
      # Transfer function H(jω) = 1/k / (1 - (ω/ωₙ)² + 2jζ(ω/ωₙ))
      omega_ratio <- omega / self$omega_n
      H <- 1 / (self$k * (1 - omega_ratio^2 + 2i * self$zeta * omega_ratio))
      
      magnitude <- abs(H)
      phase <- Arg(H) * 180 / pi
      
      return(list(magnitude = magnitude, phase = phase))
    },
    
    # Create comparison plot
    plot_damping_comparison = function(x0 = 1.0, v0 = 0.0, t_max = 10) {
      t <- seq(0, t_max, length.out = 1000)
      
      # Different damping scenarios
      zeta_values <- c(0.1, 0.5, 1.0, 2.0)
      damping_names <- c("Underdamped (ζ=0.1)", "Underdamped (ζ=0.5)", 
                        "Critically Damped (ζ=1.0)", "Overdamped (ζ=2.0)")
      
      # Create data for plotting
      plot_data <- data.frame()
      
      for (i in seq_along(zeta_values)) {
        c_temp <- zeta_values[i] * 2 * sqrt(self$m * self$k)
        system_temp <- SpringMassDamperR6$new(self$m, c_temp, self$k)
        
        x_response <- system_temp$analytical_free_response(x0, v0, t)
        
        temp_data <- data.frame(
          time = t,
          displacement = x_response,
          scenario = damping_names[i],
          zeta = zeta_values[i]
        )
        
        plot_data <- rbind(plot_data, temp_data)
      }
      
      # Create ggplot
      p <- ggplot(plot_data, aes(x = time, y = displacement, color = scenario)) +
        geom_line(size = 1.2) +
        facet_wrap(~scenario, scales = "free_y") +
        labs(
          title = "Spring-Mass-Damper Free Response Comparison",
          x = "Time (s)",
          y = "Displacement (m)"
        ) +
        theme_minimal() +
        theme(
          legend.position = "none",
          strip.text = element_text(size = 12, face = "bold"),
          plot.title = element_text(size = 16, face = "bold", hjust = 0.5)
        ) +
        scale_color_brewer(type = "qual", palette = "Set1")
      
      return(p)
    },
    
    # Bode plot
    plot_frequency_response = function(freq_max = 5) {
      freq <- 10^seq(-2, log10(freq_max), length.out = 1000)
      response <- self$frequency_response(freq)
      
      freq_ratio <- freq / (self$omega_n / (2*pi))
      
      # Magnitude plot data
      mag_data <- data.frame(
        freq_ratio = freq_ratio,
        magnitude = response$magnitude,
        magnitude_db = 20 * log10(response$magnitude)
      )
      
      # Phase plot data  
      phase_data <- data.frame(
        freq_ratio = freq_ratio,
        phase = response$phase
      )
      
      # Magnitude plot
      p1 <- ggplot(mag_data, aes(x = freq_ratio, y = magnitude)) +
        geom_line(color = "blue", size = 1) +
        scale_x_log10() +
        scale_y_log10() +
        geom_vline(xintercept = 1, color = "red", linetype = "dashed", alpha = 0.7) +
        labs(
          title = paste("Frequency Response (ζ =", round(self$zeta, 3), ")"),
          x = "Frequency Ratio (ω/ωₙ)",
          y = "Magnitude |H(jω)|"
        ) +
        theme_minimal() +
        annotation_logticks()
      
      # Phase plot
      p2 <- ggplot(phase_data, aes(x = freq_ratio, y = phase)) +
        geom_line(color = "red", size = 1) +
        scale_x_log10() +
        geom_vline(xintercept = 1, color = "red", linetype = "dashed", alpha = 0.7) +
        labs(
          x = "Frequency Ratio (ω/ωₙ)",
          y = "Phase (degrees)"
        ) +
        theme_minimal() +
        annotation_logticks(sides = "b")
      
      return(list(magnitude = p1, phase = p2))
    },
    
    # System information
    print_info = function() {
      cat("Spring-Mass-Damper System Analysis\n")
      cat("=====================================\n")
      cat(sprintf("Mass (m): %.3f kg\n", self$m))
      cat(sprintf("Damping (c): %.3f N⋅s/m\n", self$c))
      cat(sprintf("Stiffness (k): %.3f N/m\n", self$k))
      cat(sprintf("Natural Frequency (ωₙ): %.3f rad/s\n", self$omega_n))
      cat(sprintf("Damping Ratio (ζ): %.3f\n", self$zeta))
      cat(sprintf("Critical Damping (cᶜʳ): %.3f N⋅s/m\n", self$c_cr))
      cat(sprintf("Damping Type: %s\n", self$damping_type))
      if (self$damping_type == "Underdamped") {
        cat(sprintf("Damped Frequency (ωd): %.3f rad/s\n", self$omega_d))
        cat(sprintf("Period of Oscillation: %.3f s\n", 2*pi/self$omega_d))
      }
    }
  )
)

# Example usage
system <- SpringMassDamperR6$new(mass = 1.0, damping = 0.5, stiffness = 4.0)
system$print_info()

# Generate plots
damping_plot <- system$plot_damping_comparison()
freq_plots <- system$plot_frequency_response()

# Display plots
print(damping_plot)
print(freq_plots$magnitude)
print(freq_plots$phase)

# Parameter estimation from experimental data
estimate_parameters <- function(time_data, displacement_data, mass_known = NULL) {
  # If mass is known, estimate damping and stiffness
  # This is a simplified approach - in practice, use nonlinear least squares
  
  if (is.null(mass_known)) {
    stop("Mass must be provided for parameter estimation")
  }
  
  # Find peaks to estimate damped frequency and damping ratio
  library(pracma)
  
  peaks <- findpeaks(displacement_data, minpeakheight = max(displacement_data) * 0.1)
  
  if (nrow(peaks) < 2) {
    stop("Need at least 2 peaks for parameter estimation")
  }
  
  # Calculate damped period
  peak_times <- time_data[peaks[, 2]]
  T_d <- mean(diff(peak_times))
  omega_d <- 2 * pi / T_d
  
  # Calculate logarithmic decrement
  peak_amplitudes <- peaks[1:min(nrow(peaks), 5), 1]  # Use first 5 peaks
  if (length(peak_amplitudes) >= 2) {
    delta <- log(peak_amplitudes[1] / peak_amplitudes[2])
    zeta <- delta / sqrt((2*pi)^2 + delta^2)
  } else {
    zeta <- 0.1  # Default assumption
  }
  
  # Calculate natural frequency
  omega_n <- omega_d / sqrt(1 - zeta^2)
  
  # Calculate stiffness and damping
  k_est <- mass_known * omega_n^2
  c_est <- 2 * zeta * sqrt(mass_known * k_est)
  
  return(list(
    mass = mass_known,
    stiffness = k_est,
    damping = c_est,
    omega_n = omega_n,
    omega_d = omega_d,
    zeta = zeta,
    T_d = T_d
  ))
}
                </div>

                <div class="success-box">
                    <h5 class="font-bold mb-2"><i class="fas fa-chart-bar mr-2"></i>R Implementation Advantages:</h5>
                    <ul class="space-y-1">
                        <li>• Advanced statistical analysis and parameter estimation</li>
                        <li>• Professional publication-quality plots with ggplot2</li>
                        <li>• Integrated data manipulation with dplyr/tidyr</li>
                        <li>• Signal processing capabilities for experimental data</li>
                        <li>• Interactive visualizations with plotly</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Part II: RLC Electrical Circuits -->
        <section class="section-divider">
            <h2 class="text-4xl font-bold mb-8 text-purple-600"><i class="fas fa-bolt mr-3"></i>Part II: RLC Electrical Circuits</h2>

            <!-- Circuit Fundamentals -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600"><i class="fas fa-microchip mr-2"></i>2.1 Circuit Fundamentals and Kirchhoff's Laws</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-semibold mb-4">Circuit Elements</h4>
                        
                        <div class="info-box">
                            <h5 class="font-bold mb-3">Resistor (R)</h5>
                            <p class="mb-2"><strong>Voltage-Current Relationship:</strong> $V_R = IR$ (Ohm's Law)</p>
                            <p class="mb-2"><strong>Power Dissipation:</strong> $P_R = I^2R = V_R^2/R$</p>
                            <p class="mb-2"><strong>Energy:</strong> Dissipated as heat</p>
                            <p><strong>Units:</strong> Ohms (Ω)</p>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Inductor (L)</h5>
                            <p class="mb-2"><strong>Voltage-Current Relationship:</strong> $V_L = L\frac{dI}{dt}$</p>
                            <p class="mb-2"><strong>Energy Storage:</strong> $W_L = \frac{1}{2}LI^2$</p>
                            <p class="mb-2"><strong>Physical Property:</strong> Opposes changes in current</p>
                            <p><strong>Units:</strong> Henrys (H)</p>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Capacitor (C)</h5>
                            <p class="mb-2"><strong>Voltage-Current Relationship:</strong> $I = C\frac{dV_C}{dt}$</p>
                            <p class="mb-2"><strong>Energy Storage:</strong> $W_C = \frac{1}{2}CV_C^2$</p>
                            <p class="mb-2"><strong>Physical Property:</strong> Opposes changes in voltage</p>
                            <p><strong>Units:</strong> Farads (F)</p>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-xl font-semibold mb-4">Kirchhoff's Laws Application</h4>
                        
                        <div class="math-box">
                            <h5 class="font-bold mb-3">Kirchhoff's Voltage Law (KVL):</h5>
                            <p class="mb-2">For RLC series circuit:</p>
                            <p class="mb-2">$V_s(t) = V_R + V_L + V_C$</p>
                            <p class="mb-2">$V_s(t) = IR + L\frac{dI}{dt} + \frac{Q}{C}$</p>
                            <p class="mb-4">Since $I = \frac{dQ}{dt}$:</p>
                            <p class="text-center text-lg">$$L\frac{d^2Q}{dt^2} + R\frac{dQ}{dt} + \frac{Q}{C} = V_s(t)$$</p>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Alternative Form (Current):</h5>
                            <p class="mb-2">Differentiating the charge equation:</p>
                            <p class="text-center">$$L\frac{dI}{dt} + RI + \frac{1}{C}\int I \, dt = V_s(t)$$</p>
                        </div>

                        <div class="highlight-box">
                            <h5 class="font-bold mb-3">Circuit-Mechanical Analogy:</h5>
                            <table class="w-full text-sm">
                                <tr><th>Mechanical</th><th>Electrical</th></tr>
                                <tr><td>Mass (m)</td><td>Inductance (L)</td></tr>
                                <tr><td>Damping (c)</td><td>Resistance (R)</td></tr>
                                <tr><td>Spring (k)</td><td>1/Capacitance (1/C)</td></tr>
                                <tr><td>Displacement (x)</td><td>Charge (Q)</td></tr>
                                <tr><td>Velocity (v)</td><td>Current (I)</td></tr>
                                <tr><td>Force (F)</td><td>Voltage (V)</td></tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Circuit Analysis -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600"><i class="fas fa-calculator mr-2"></i>2.2 RLC Circuit Analysis</h3>
                
                <div class="math-box">
                    <h4 class="font-bold mb-4">Standard Form Analysis:</h4>
                    <p class="mb-2">RLC equation: $L\frac{d^2Q}{dt^2} + R\frac{dQ}{dt} + \frac{Q}{C} = V_s(t)$</p>
                    <p class="mb-2">Dividing by L: $\frac{d^2Q}{dt^2} + \frac{R}{L}\frac{dQ}{dt} + \frac{1}{LC}Q = \frac{V_s(t)}{L}$</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                        <div>
                            <h5 class="font-semibold mb-2">Circuit Parameters:</h5>
                            <ul class="space-y-1 text-sm">
                                <li>• Natural frequency: $\omega_0 = \frac{1}{\sqrt{LC}}$</li>
                                <li>• Damping coefficient: $\alpha = \frac{R}{2L}$</li>
                                <li>• Quality factor: $Q = \frac{\omega_0 L}{R} = \frac{1}{R}\sqrt{\frac{L}{C}}$</li>
                                <li>• Characteristic impedance: $Z_0 = \sqrt{\frac{L}{C}}$</li>
                            </ul>
                        </div>
                        <div>
                            <h5 class="font-semibold mb-2">Damping Classification:</h5>
                            <ul class="space-y-1 text-sm">
                                <li>• Underdamped: $R < 2\sqrt{\frac{L}{C}}$</li>
                                <li>• Critically damped: $R = 2\sqrt{\frac{L}{C}}$</li>
                                <li>• Overdamped: $R > 2\sqrt{\frac{L}{C}}$</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="mt-8">
                    <h4 class="text-xl font-semibold mb-4">Natural Response Solutions</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="math-box">
                            <h5 class="font-bold mb-3 text-green-700">Underdamped</h5>
                            <p class="mb-2">$R < 2\sqrt{L/C}$</p>
                            <p class="mb-2">$\omega_d = \sqrt{\omega_0^2 - \alpha^2}$</p>
                            <p class="text-sm">$$Q(t) = e^{-\alpha t}(A\cos(\omega_d t) + B\sin(\omega_d t))$$</p>
                            <p class="text-xs mt-2">Oscillatory decay</p>
                        </div>
                        
                        <div class="math-box">
                            <h5 class="font-bold mb-3 text-blue-700">Critically Damped</h5>
                            <p class="mb-2">$R = 2\sqrt{L/C}$</p>
                            <p class="mb-2">$\omega_d = 0$</p>
                            <p class="text-sm">$$Q(t) = (A + Bt)e^{-\alpha t}$$</p>
                            <p class="text-xs mt-2">Fastest return without overshoot</p>
                        </div>
                        
                        <div class="math-box">
                            <h5 class="font-bold mb-3 text-red-700">Overdamped</h5>
                            <p class="mb-2">$R > 2\sqrt{L/C}$</p>
                            <p class="mb-2">$s_1, s_2 = -\alpha \pm \sqrt{\alpha^2 - \omega_0^2}$</p>
                            <p class="text-sm">$$Q(t) = Ae^{s_1 t} + Be^{s_2 t}$$</p>
                            <p class="text-xs mt-2">Exponential approach</p>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <canvas id="rlcChart"></canvas>
                </div>
            </div>

            <!-- AC Analysis -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600"><i class="fas fa-wave-square mr-2"></i>2.3 AC Steady-State Analysis and Impedance</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-semibold mb-4">Complex Impedance Method</h4>
                        
                        <div class="math-box">
                            <h5 class="font-bold mb-3">Impedances:</h5>
                            <ul class="space-y-2">
                                <li><strong>Resistor:</strong> $Z_R = R$</li>
                                <li><strong>Inductor:</strong> $Z_L = j\omega L$</li>
                                <li><strong>Capacitor:</strong> $Z_C = \frac{1}{j\omega C} = \frac{-j}{\omega C}$</li>
                            </ul>
                            
                            <h5 class="font-bold mb-3 mt-4">Series RLC Impedance:</h5>
                            <p class="mb-2">$$Z_{total} = R + j\omega L + \frac{1}{j\omega C}$$</p>
                            <p>$$Z_{total} = R + j\left(\omega L - \frac{1}{\omega C}\right)$$</p>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Magnitude and Phase:</h5>
                            <p class="mb-2">$$|Z| = \sqrt{R^2 + \left(\omega L - \frac{1}{\omega C}\right)^2}$$</p>
                            <p>$$\phi = \tan^{-1}\left(\frac{\omega L - \frac{1}{\omega C}}{R}\right)$$</p>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-xl font-semibold mb-4">Resonance Phenomena</h4>
                        
                        <div class="highlight-box">
                            <h5 class="font-bold mb-3">Resonance Condition:</h5>
                            <p class="mb-2">At resonance: $\omega L = \frac{1}{\omega C}$</p>
                            <p class="mb-2">Resonant frequency: $\omega_0 = \frac{1}{\sqrt{LC}}$</p>
                            <p class="mb-2">At resonance: $Z = R$ (minimum impedance)</p>
                            <p>Phase shift: $\phi = 0°$</p>
                        </div>

                        <div class="math-box">
                            <h5 class="font-bold mb-3">Quality Factor Effects:</h5>
                            <p class="mb-2">$$Q = \frac{\omega_0 L}{R} = \frac{1}{R}\sqrt{\frac{L}{C}}$$</p>
                            <ul class="text-sm mt-2 space-y-1">
                                <li>• High Q: Sharp resonance, narrow bandwidth</li>
                                <li>• Low Q: Broad resonance, wide bandwidth</li>
                                <li>• Bandwidth: $BW = \frac{\omega_0}{Q}$</li>
                            </ul>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Power Analysis:</h5>
                            <p class="mb-2">Average power: $P = I^2 R = \frac{V^2 R}{|Z|^2}$</p>
                            <p class="mb-2">Reactive power: $Q = I^2 X$</p>
                            <p>Power factor: $\cos(\phi) = \frac{R}{|Z|}$</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Applications -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600"><i class="fas fa-filter mr-2"></i>2.4 Filter Applications and Frequency Response</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-semibold mb-4">Filter Types</h4>
                        
                        <div class="info-box">
                            <h5 class="font-bold mb-3 text-green-700">Low-Pass Filter (RC)</h5>
                            <p class="mb-2">Transfer function: $H(s) = \frac{1}{1 + sRC}$</p>
                            <p class="mb-2">Cutoff frequency: $f_c = \frac{1}{2\pi RC}$</p>
                            <p class="text-sm">Passes low frequencies, attenuates high frequencies</p>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3 text-blue-700">High-Pass Filter (RC)</h5>
                            <p class="mb-2">Transfer function: $H(s) = \frac{sRC}{1 + sRC}$</p>
                            <p class="mb-2">Cutoff frequency: $f_c = \frac{1}{2\pi RC}$</p>
                            <p class="text-sm">Passes high frequencies, attenuates low frequencies</p>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3 text-purple-700">Band-Pass Filter (RLC)</h5>
                            <p class="mb-2">Transfer function: $H(s) = \frac{sRC}{s^2LC + sRC + 1}$</p>
                            <p class="mb-2">Center frequency: $f_0 = \frac{1}{2\pi\sqrt{LC}}$</p>
                            <p class="text-sm">Passes frequencies near resonance</p>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-xl font-semibold mb-4">Design Considerations</h4>
                        
                        <div class="math-box">
                            <h5 class="font-bold mb-3">Second-Order RLC Filters:</h5>
                            <p class="mb-2">Standard form: $H(s) = \frac{K\omega_n^2}{s^2 + 2\zeta\omega_n s + \omega_n^2}$</p>
                            <ul class="text-sm mt-2 space-y-1">
                                <li>• $\omega_n = 1/\sqrt{LC}$: Natural frequency</li>
                                <li>• $\zeta = \frac{R}{2}\sqrt{\frac{C}{L}}$: Damping ratio</li>
                                <li>• K: DC gain factor</li>
                            </ul>
                        </div>

                        <div class="highlight-box">
                            <h5 class="font-bold mb-3">Filter Characteristics:</h5>
                            <ul class="space-y-1 text-sm">
                                <li><strong>Butterworth:</strong> $\zeta = 0.707$ (maximally flat)</li>
                                <li><strong>Chebyshev:</strong> $\zeta < 0.707$ (ripple in passband)</li>
                                <li><strong>Bessel:</strong> $\zeta > 0.707$ (linear phase)</li>
                                <li><strong>Critical:</strong> $\zeta = 1$ (no overshoot)</li>
                            </ul>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Performance Metrics:</h5>
                            <ul class="space-y-1 text-sm">
                                <li>• <strong>-3dB Bandwidth:</strong> $BW = 2\zeta\omega_n$</li>
                                <li>• <strong>Quality Factor:</strong> $Q = 1/(2\zeta)$</li>
                                <li>• <strong>Roll-off Rate:</strong> 40 dB/decade (2nd order)</li>
                                <li>• <strong>Group Delay:</strong> $\tau_g = -\frac{d\phi}{d\omega}$</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <canvas id="filterChart"></canvas>
                </div>
            </div>

            <!-- Python RLC Implementation -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600"><i class="fab fa-python mr-2"></i>2.5 Python Implementation - RLC Circuit Analysis</h3>
                
                <div class="code-block python-code">
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.integrate import solve_ivp
import sympy as sp
from sympy import symbols, I, exp, cos, sin, sqrt, pi, simplify

class RLCCircuit:
    """
    Comprehensive RLC circuit analysis class
    """
    
    def __init__(self, R, L, C):
        self.R = R          # Resistance (Ohms)
        self.L = L          # Inductance (Henrys) 
        self.C = C          # Capacitance (Farads)
        
        # Calculate circuit parameters
        self.omega_0 = 1 / np.sqrt(self.L * self.C)  # Natural frequency
        self.alpha = self.R / (2 * self.L)           # Damping coefficient
        self.Q_factor = self.omega_0 * self.L / self.R  # Quality factor
        self.Z_0 = np.sqrt(self.L / self.C)          # Characteristic impedance
        
        # Determine damping type
        if self.alpha < self.omega_0:
            self.damping_type = "Underdamped"
            self.omega_d = np.sqrt(self.omega_0**2 - self.alpha**2)
        elif abs(self.alpha - self.omega_0) < 1e-10:
            self.damping_type = "Critically Damped"
            self.omega_d = 0
        else:
            self.damping_type = "Overdamped"
            self.omega_d = 0
    
    def natural_response(self, Q0, I0, t_span, t_eval=None):
        """
        Calculate natural response (no external voltage source)
        Q0: initial charge
        I0: initial current
        """
        if t_eval is None:
            t_eval = np.linspace(0, t_span, 1000)
        
        def circuit_ode(t, y):
            Q, I = y
            # dQ/dt = I
            # dI/dt = -(R/L)I - (1/LC)Q
            dQ_dt = I
            dI_dt = -(self.R/self.L)*I - (1/(self.L*self.C))*Q
            return [dQ_dt, dI_dt]
        
        sol = solve_ivp(circuit_ode, [0, t_span], [Q0, I0], 
                       t_eval=t_eval, method='RK45', rtol=1e-8)
        
        return sol.t, sol.y[0], sol.y[1]  # time, charge, current
    
    def analytical_natural_response(self, Q0, I0, t_array):
        """Analytical solution for natural response"""
        if self.damping_type == "Underdamped":
            # Constants from initial conditions
            phi = np.arctan((I0 + self.alpha * Q0) / (self.omega_d * Q0))
            A = Q0 / np.cos(phi)
            
            # Solution
            envelope = np.exp(-self.alpha * t_array)
            Q_t = A * envelope * np.cos(self.omega_d * t_array + phi)
            I_t = -A * envelope * (self.alpha * np.cos(self.omega_d * t_array + phi) + 
                                  self.omega_d * np.sin(self.omega_d * t_array + phi))
            
        elif self.damping_type == "Critically Damped":
            # Constants
            A = Q0
            B = I0 + self.alpha * Q0
            
            # Solution
            Q_t = (A + B * t_array) * np.exp(-self.alpha * t_array)
            I_t = (B - self.alpha * (A + B * t_array)) * np.exp(-self.alpha * t_array)
            
        else:  # Overdamped
            s1 = -self.alpha + np.sqrt(self.alpha**2 - self.omega_0**2)
            s2 = -self.alpha - np.sqrt(self.alpha**2 - self.omega_0**2)
            
            # Constants from initial conditions
            A = (I0 - s2 * Q0) / (s1 - s2)
            B = (s1 * Q0 - I0) / (s1 - s2)
            
            # Solution
            Q_t = A * np.exp(s1 * t_array) + B * np.exp(s2 * t_array)
            I_t = A * s1 * np.exp(s1 * t_array) + B * s2 * np.exp(s2 * t_array)
        
        return Q_t, I_t
    
    def forced_response(self, voltage_func, t_span, Q0=0, I0=0, t_eval=None):
        """Calculate forced response to external voltage"""
        if t_eval is None:
            t_eval = np.linspace(0, t_span, 1000)
        
        def circuit_ode_forced(t, y):
            Q, I = y
            V_t = voltage_func(t)
            dQ_dt = I
            dI_dt = (V_t - self.R*I - Q/self.C) / self.L
            return [dQ_dt, dI_dt]
        
        sol = solve_ivp(circuit_ode_forced, [0, t_span], [Q0, I0], 
                       t_eval=t_eval, method='RK45', rtol=1e-8)
        
        return sol.t, sol.y[0], sol.y[1]
    
    def impedance(self, freq_array):
        """Calculate complex impedance as function of frequency"""
        omega = 2 * np.pi * freq_array
        Z = self.R + 1j * (omega * self.L - 1 / (omega * self.C))
        return Z
    
    def transfer_function(self, freq_array, output_type='voltage'):
        """
        Calculate transfer function
        output_type: 'voltage' (across capacitor), 'current'
        """
        omega = 2 * np.pi * freq_array
        s = 1j * omega
        
        if output_type == 'voltage':
            # H(s) = Vc/Vs = (1/LC) / (s^2 + (R/L)s + 1/LC)
            H = (1/(self.L*self.C)) / (s**2 + (self.R/self.L)*s + 1/(self.L*self.C))
        elif output_type == 'current':
            # H(s) = I/Vs = s / (Ls^2 + Rs + 1/C)
            H = s / (self.L*s**2 + self.R*s + 1/self.C)
        
        return H
    
    def bode_plot(self, freq_range=None, output_type='voltage'):
        """Generate Bode plot"""
        if freq_range is None:
            freq_range = np.logspace(-2, 2, 1000) * self.omega_0 / (2*np.pi)
        
        H = self.transfer_function(freq_range, output_type)
        magnitude_db = 20 * np.log10(np.abs(H))
        phase_deg = np.angle(H) * 180 / np.pi
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        fig.suptitle(f'RLC Circuit Bode Plot - {output_type.capitalize()} Output\n'
                    f'R={self.R}Ω, L={self.L}H, C={self.C}F, Q={self.Q_factor:.2f}', 
                    fontsize=14)
        
        # Magnitude plot
        ax1.semilogx(freq_range, magnitude_db, 'b-', linewidth=2)
        ax1.axvline(self.omega_0/(2*np.pi), color='red', linestyle='--', 
                   alpha=0.7, label=f'f₀ = {self.omega_0/(2*np.pi):.2f} Hz')
        ax1.set_ylabel('Magnitude (dB)')
        ax1.grid(True, which="both", alpha=0.3)
        ax1.legend()
        
        # Phase plot
        ax2.semilogx(freq_range, phase_deg, 'r-', linewidth=2)
        ax2.axvline(self.omega_0/(2*np.pi), color='red', linestyle='--', 
                   alpha=0.7, label=f'f₀ = {self.omega_0/(2*np.pi):.2f} Hz')
        ax2.set_xlabel('Frequency (Hz)')
        ax2.set_ylabel('Phase (degrees)')
        ax2.grid(True, which="both", alpha=0.3)
        ax2.legend()
        
        plt.tight_layout()
        return fig, (magnitude_db, phase_deg, freq_range)
    
    def step_response(self, step_amplitude=1.0, t_max=None):
        """Calculate and plot step response"""
        if t_max is None:
            t_max = 10 / (self.alpha if self.alpha > 0 else self.omega_0)
        
        t = np.linspace(0, t_max, 1000)
        
        # Step voltage function
        def step_voltage(t):
            return step_amplitude * np.ones_like(t)
        
        # Calculate response
        t_resp, Q_resp, I_resp = self.forced_response(step_voltage, t_max, t_eval=t)
        
        # Calculate voltage across capacitor
        V_C = Q_resp / self.C
        
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10))
        fig.suptitle(f'RLC Circuit Step Response (V₀ = {step_amplitude}V)\n'
                    f'Damping: {self.damping_type}, Q = {self.Q_factor:.2f}', 
                    fontsize=14)
        
        # Charge
        ax1.plot(t_resp, Q_resp*1000, 'b-', linewidth=2)
        ax1.set_ylabel('Charge (mC)')
        ax1.grid(True, alpha=0.3)
        
        # Current
        ax2.plot(t_resp, I_resp*1000, 'r-', linewidth=2)
        ax2.set_ylabel('Current (mA)')
        ax2.grid(True, alpha=0.3)
        
        # Capacitor voltage
        ax3.plot(t_resp, V_C, 'g-', linewidth=2)
        ax3.axhline(step_amplitude, color='k', linestyle='--', alpha=0.5, 
                   label='Input Step')
        ax3.set_xlabel('Time (s)')
        ax3.set_ylabel('Capacitor Voltage (V)')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        plt.tight_layout()
        return fig, (t_resp, Q_resp, I_resp, V_C)
    
    def plot_natural_response_comparison(self):
        """Compare different damping types"""
        # Time array
        t_max = 6 / self.omega_0
        t = np.linspace(0, t_max, 1000)
        
        # Different damping scenarios
        R_values = [0.5*self.Z_0, self.Z_0, 2*self.Z_0, 4*self.Z_0]
        labels = ['Underdamped (R < Z₀)', 'Underdamped (R = Z₀)', 
                 'Critically Damped (R = 2Z₀)', 'Overdamped (R = 4Z₀)']
        colors = ['blue', 'green', 'red', 'purple']
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('RLC Circuit Natural Response Comparison\n'
                    f'L = {self.L}H, C = {self.C}F, Initial: Q₀ = 1mC, I₀ = 0', 
                    fontsize=16)
        
        for i, (R_val, label, color) in enumerate(zip(R_values, labels, colors)):
            circuit_temp = RLCCircuit(R_val, self.L, self.C)
            Q_temp, I_temp = circuit_temp.analytical_natural_response(1e-3, 0, t)
            
            row, col = i // 2, i % 2
            axes[row, col].plot(t*1000, Q_temp*1000, color=color, linewidth=2, 
                              label='Charge')
            axes[row, col].plot(t*1000, I_temp*1000, '--', color=color, 
                              linewidth=2, alpha=0.7, label='Current')
            axes[row, col].set_xlabel('Time (ms)')
            axes[row, col].set_ylabel('Charge (mC) / Current (mA)')
            axes[row, col].set_title(f'{label}\nQ = {circuit_temp.Q_factor:.2f}')
            axes[row, col].grid(True, alpha=0.3)
            axes[row, col].legend()
        
        plt.tight_layout()
        return fig
    
    def circuit_info(self):
        """Display circuit information"""
        print(f"RLC Circuit Analysis")
        print(f"{'='*40}")
        print(f"Resistance (R): {self.R:.3f} Ω")
        print(f"Inductance (L): {self.L*1000:.3f} mH")
        print(f"Capacitance (C): {self.C*1e6:.3f} μF")
        print(f"Natural Frequency (f₀): {self.omega_0/(2*np.pi):.3f} Hz")
        print(f"Damping Coefficient (α): {self.alpha:.3f} rad/s")
        print(f"Quality Factor (Q): {self.Q_factor:.3f}")
        print(f"Characteristic Impedance (Z₀): {self.Z_0:.3f} Ω")
        print(f"Damping Type: {self.damping_type}")
        if self.damping_type == "Underdamped":
            print(f"Damped Frequency (fd): {self.omega_d/(2*np.pi):.3f} Hz")

# Example usage
if __name__ == "__main__":
    # Create RLC circuit: R=10Ω, L=10mH, C=100μF
    circuit = RLCCircuit(R=10, L=10e-3, C=100e-6)
    circuit.circuit_info()
    
    # Generate plots
    fig1 = circuit.plot_natural_response_comparison()
    fig2, _ = circuit.bode_plot()
    fig3, _ = circuit.step_response()
    
    plt.show()
                </div>
            </div>
        </section>

        <!-- Advanced Topics -->
        <section class="section-divider">
            <h2 class="text-4xl font-bold mb-8 text-purple-600"><i class="fas fa-rocket mr-3"></i>Part III: Advanced Topics and Extensions</h2>

            <!-- Multi-DOF Systems -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600"><i class="fas fa-project-diagram mr-2"></i>3.1 Multi-Degree-of-Freedom Systems</h3>
                
                <p class="text-lg mb-6">
                    Real engineering systems often involve multiple interacting components, leading to systems of coupled differential equations. These multi-degree-of-freedom (MDOF) systems exhibit rich dynamic behavior including multiple natural frequencies and mode shapes.
                </p>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-semibold mb-4">Two-Mass Spring System</h4>
                        
                        <div class="math-box">
                            <h5 class="font-bold mb-3">System Equations:</h5>
                            <p class="mb-2">$m_1\ddot{x}_1 + (k_1 + k_2)x_1 - k_2 x_2 = F_1(t)$</p>
                            <p class="mb-4">$m_2\ddot{x}_2 - k_2 x_1 + k_2 x_2 = F_2(t)$</p>
                            
                            <h5 class="font-bold mb-3">Matrix Form:</h5>
                            <p class="text-center">$$\mathbf{M}\ddot{\mathbf{x}} + \mathbf{K}\mathbf{x} = \mathbf{F}(t)$$</p>
                            
                            <p class="mt-4">Where:</p>
                            <p class="text-sm">
                                $\mathbf{M} = \begin{bmatrix} m_1 & 0 \\ 0 & m_2 \end{bmatrix}$, 
                                $\mathbf{K} = \begin{bmatrix} k_1+k_2 & -k_2 \\ -k_2 & k_2 \end{bmatrix}$
                            </p>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Natural Frequencies:</h5>
                            <p class="mb-2">Eigenvalue problem: $\det(\mathbf{K} - \omega^2\mathbf{M}) = 0$</p>
                            <ul class="text-sm space-y-1">
                                <li>• Two natural frequencies: $\omega_1, \omega_2$</li>
                                <li>• Corresponding mode shapes: $\phi_1, \phi_2$</li>
                                <li>• Modal orthogonality conditions</li>
                            </ul>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-xl font-semibold mb-4">Coupled RLC Networks</h4>
                        
                        <div class="math-box">
                            <h5 class="font-bold mb-3">Mutual Inductance Coupling:</h5>
                            <p class="mb-2">$L_1\frac{dI_1}{dt} + M\frac{dI_2}{dt} + R_1 I_1 = V_1(t)$</p>
                            <p class="mb-4">$L_2\frac{dI_2}{dt} + M\frac{dI_1}{dt} + R_2 I_2 = V_2(t)$</p>
                            
                            <p class="mb-2">Where M is the mutual inductance:</p>
                            <p class="text-center">$M = k\sqrt{L_1 L_2}$ (0 ≤ k ≤ 1)</p>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Coupling Effects:</h5>
                            <ul class="space-y-1 text-sm">
                                <li>• <strong>Weak Coupling (k ≪ 1):</strong> Nearly independent circuits</li>
                                <li>• <strong>Strong Coupling (k ≈ 1):</strong> Significant energy transfer</li>
                                <li>• <strong>Critical Coupling:</strong> Maximum power transfer</li>
                                <li>• <strong>Beat Phenomena:</strong> Energy oscillation between circuits</li>
                            </ul>
                        </div>

                        <div class="highlight-box">
                            <h5 class="font-bold mb-3">Applications:</h5>
                            <ul class="space-y-1 text-sm">
                                <li>• Wireless power transfer systems</li>
                                <li>• Transformer design and analysis</li>
                                <li>• Coupled antenna systems</li>
                                <li>• Building structural dynamics</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="code-block python-code">
# Example: Two-mass spring system analysis
import numpy as np
import matplotlib.pyplot as plt
from scipy.linalg import eig

class TwoMassSystem:
    def __init__(self, m1, m2, k1, k2):
        self.m1, self.m2 = m1, m2
        self.k1, self.k2 = k1, k2
        
        # Mass and stiffness matrices
        self.M = np.array([[m1, 0], [0, m2]])
        self.K = np.array([[k1+k2, -k2], [-k2, k2]])
    
    def natural_frequencies_and_modes(self):
        # Solve generalized eigenvalue problem
        eigenvals, eigenvecs = eig(self.K, self.M)
        
        # Natural frequencies (sorted)
        omega_n = np.sqrt(eigenvals)
        idx = np.argsort(omega_n)
        omega_n = omega_n[idx]
        mode_shapes = eigenvecs[:, idx]
        
        return omega_n, mode_shapes
    
    def plot_mode_shapes(self):
        omega_n, modes = self.natural_frequencies_and_modes()
        
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        
        for i in range(2):
            x_pos = [0, 1, 2]  # Positions along the system
            displacements = [0, modes[0, i], modes[1, i]]
            
            axes[i].plot(x_pos, displacements, 'o-', linewidth=3, markersize=8)
            axes[i].axhline(0, color='k', linestyle='--', alpha=0.3)
            axes[i].set_title(f'Mode {i+1}: f = {omega_n[i]/(2*np.pi):.2f} Hz')
            axes[i].set_xlabel('Position')
            axes[i].set_ylabel('Relative Displacement')
            axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig

# Example usage
system = TwoMassSystem(m1=1, m2=2, k1=1000, k2=500)
omega_n, modes = system.natural_frequencies_and_modes()
print(f"Natural frequencies: {omega_n/(2*np.pi)} Hz")
fig = system.plot_mode_shapes()
                </div>
            </div>

            <!-- Parameter Estimation -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600"><i class="fas fa-chart-line mr-2"></i>3.2 Parameter Estimation and System Identification</h3>
                
                <p class="text-lg mb-6">
                    In practice, system parameters (mass, damping, stiffness, resistance, inductance, capacitance) are often unknown and must be estimated from experimental data. This process, called system identification, is crucial for model validation and control system design.
                </p>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-semibold mb-4">Experimental Methods</h4>
                        
                        <div class="info-box">
                            <h5 class="font-bold mb-3">Free Decay Method</h5>
                            <ul class="space-y-2 text-sm">
                                <li>• <strong>Procedure:</strong> Displace system and release</li>
                                <li>• <strong>Measurement:</strong> Record displacement vs time</li>
                                <li>• <strong>Analysis:</strong> Extract damping from decay envelope</li>
                                <li>• <strong>Advantage:</strong> Simple, no external forcing</li>
                            </ul>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Logarithmic Decrement</h5>
                            <p class="mb-2">For underdamped systems:</p>
                            <p class="mb-2">$\delta = \ln\left(\frac{x_n}{x_{n+1}}\right) = \frac{2\pi\zeta}{\sqrt{1-\zeta^2}}$</p>
                            <p class="text-sm">From measured peak amplitudes</p>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Frequency Response Method</h5>
                            <ul class="space-y-2 text-sm">
                                <li>• <strong>Procedure:</strong> Apply sinusoidal input, vary frequency</li>
                                <li>• <strong>Measurement:</strong> Amplitude and phase vs frequency</li>
                                <li>• <strong>Analysis:</strong> Fit theoretical transfer function</li>
                                <li>• <strong>Advantage:</strong> Full frequency domain characterization</li>
                            </ul>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-xl font-semibold mb-4">Statistical Estimation</h4>
                        
                        <div class="math-box">
                            <h5 class="font-bold mb-3">Least Squares Estimation</h5>
                            <p class="mb-2">Minimize cost function:</p>
                            <p class="mb-2">$J(\theta) = \sum_{i=1}^N [y_i - \hat{y}_i(\theta)]^2$</p>
                            <p class="mb-2">Where $\theta$ = parameter vector</p>
                            <p class="text-sm">$\hat{y}_i$ = model prediction</p>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Maximum Likelihood</h5>
                            <p class="mb-2">For noisy measurements:</p>
                            <p class="mb-2">$\hat{\theta} = \arg\max_\theta \mathcal{L}(\theta | \text{data})$</p>
                            <p class="text-sm">Provides statistical confidence intervals</p>
                        </div>

                        <div class="highlight-box">
                            <h5 class="font-bold mb-3">Validation Metrics</h5>
                            <ul class="space-y-1 text-sm">
                                <li>• <strong>R²:</strong> Coefficient of determination</li>
                                <li>• <strong>RMSE:</strong> Root mean square error</li>
                                <li>• <strong>AIC/BIC:</strong> Information criteria</li>
                                <li>• <strong>Residual Analysis:</strong> Error pattern examination</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="code-block python-code">
# Parameter estimation example for spring-mass-damper system
import numpy as np
from scipy.optimize import minimize
from scipy.integrate import solve_ivp
import matplotlib.pyplot as plt

class ParameterEstimation:
    def __init__(self, time_data, displacement_data):
        self.t_data = time_data
        self.x_data = displacement_data
        
    def model_response(self, params, t_eval, x0=None, v0=None):
        """Simulate system response with given parameters"""
        m, c, k = params
        
        if x0 is None:
            x0 = self.x_data[0]
        if v0 is None:
            # Estimate initial velocity from data
            v0 = (self.x_data[1] - self.x_data[0]) / (self.t_data[1] - self.t_data[0])
        
        def system_ode(t, y):
            x, x_dot = y
            x_ddot = (-c * x_dot - k * x) / m
            return [x_dot, x_ddot]
        
        sol = solve_ivp(system_ode, [t_eval[0], t_eval[-1]], [x0, v0], 
                       t_eval=t_eval, method='RK45')
        
        return sol.y[0]  # Return displacement
    
    def objective_function(self, params):
        """Cost function for parameter estimation"""
        try:
            x_model = self.model_response(params, self.t_data)
            residuals = self.x_data - x_model
            return np.sum(residuals**2)  # Sum of squared errors
        except:
            return 1e10  # Large penalty for invalid parameters
    
    def estimate_parameters(self, initial_guess=[1.0, 0.1, 1.0]):
        """Estimate parameters using optimization"""
        # Bounds: m > 0, c >= 0, k > 0
        bounds = [(0.01, 100), (0, 10), (0.01, 1000)]
        
        result = minimize(self.objective_function, initial_guess, 
                         method='L-BFGS-B', bounds=bounds)
        
        if result.success:
            m_est, c_est, k_est = result.x
            return {
                'mass': m_est,
                'damping': c_est, 
                'stiffness': k_est,
                'omega_n': np.sqrt(k_est / m_est),
                'zeta': c_est / (2 * np.sqrt(m_est * k_est)),
                'cost': result.fun,
                'success': True
            }
        else:
            return {'success': False, 'message': result.message}
    
    def validate_fit(self, estimated_params):
        """Validate the estimated parameters"""
        params = [estimated_params['mass'], estimated_params['damping'], 
                 estimated_params['stiffness']]
        
        x_model = self.model_response(params, self.t_data)
        
        # Calculate metrics
        ss_res = np.sum((self.x_data - x_model)**2)
        ss_tot = np.sum((self.x_data - np.mean(self.x_data))**2)
        r_squared = 1 - (ss_res / ss_tot)
        rmse = np.sqrt(np.mean((self.x_data - x_model)**2))
        
        # Plot comparison
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        # Data vs model
        ax1.plot(self.t_data, self.x_data, 'o', label='Experimental Data', 
                markersize=4, alpha=0.7)
        ax1.plot(self.t_data, x_model, '-', label='Model Fit', linewidth=2)
        ax1.set_xlabel('Time (s)')
        ax1.set_ylabel('Displacement (m)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_title(f'Parameter Estimation Results\n'
                     f'R² = {r_squared:.4f}, RMSE = {rmse:.6f}')
        
        # Residuals
        residuals = self.x_data - x_model
        ax2.plot(self.t_data, residuals, 'r-', linewidth=1)
        ax2.axhline(0, color='k', linestyle='--', alpha=0.5)
        ax2.set_xlabel('Time (s)')
        ax2.set_ylabel('Residuals (m)')
        ax2.grid(True, alpha=0.3)
        ax2.set_title('Residual Analysis')
        
        plt.tight_layout()
        
        return {
            'r_squared': r_squared,
            'rmse': rmse,
            'residuals': residuals,
            'model_response': x_model,
            'figure': fig
        }

# Example: Generate synthetic data with noise and estimate parameters
np.random.seed(42)
t_true = np.linspace(0, 5, 100)
m_true, c_true, k_true = 1.0, 0.2, 4.0

# Generate "experimental" data
system_true = SpringMassDamper(m_true, c_true, k_true)
x_true = system_true.analytical_free_response(1.0, 0.0, t_true)
x_noisy = x_true + 0.02 * np.random.normal(size=len(x_true))

# Estimate parameters
estimator = ParameterEstimation(t_true, x_noisy)
estimated = estimator.estimate_parameters()

if estimated['success']:
    print("Parameter Estimation Results:")
    print(f"True: m={m_true}, c={c_true}, k={k_true}")
    print(f"Estimated: m={estimated['mass']:.3f}, c={estimated['damping']:.3f}, k={estimated['stiffness']:.3f}")
    print(f"Natural frequency: {estimated['omega_n']:.3f} rad/s")
    print(f"Damping ratio: {estimated['zeta']:.3f}")
    
    validation = estimator.validate_fit(estimated)
    plt.show()
                </div>
            </div>

            <!-- Design Optimization -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600"><i class="fas fa-cog mr-2"></i>3.3 Design Optimization and Performance Criteria</h3>
                
                <p class="text-lg mb-6">
                    Engineering design involves optimizing system parameters to meet performance specifications. This requires translating qualitative requirements into quantitative objectives and constraints that can be mathematically optimized.
                </p>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-semibold mb-4">Performance Metrics</h4>
                        
                        <div class="info-box">
                            <h5 class="font-bold mb-3">Time Domain Specifications</h5>
                            <ul class="space-y-2 text-sm">
                                <li>• <strong>Rise Time (tr):</strong> 10% to 90% of final value</li>
                                <li>• <strong>Settling Time (ts):</strong> Within 2% of final value</li>
                                <li>• <strong>Overshoot (Mp):</strong> Maximum overshoot percentage</li>
                                <li>• <strong>Peak Time (tp):</strong> Time to first maximum</li>
                            </ul>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Frequency Domain Specifications</h5>
                            <ul class="space-y-2 text-sm">
                                <li>• <strong>Bandwidth:</strong> Frequency range of operation</li>
                                <li>• <strong>Resonant Peak:</strong> Maximum amplification</li>
                                <li>• <strong>Phase Margin:</strong> Stability measure</li>
                                <li>• <strong>Gain Margin:</strong> Stability measure</li>
                            </ul>
                        </div>

                        <div class="math-box">
                            <h5 class="font-bold mb-3">Design Relationships</h5>
                            <p class="mb-2">For second-order systems:</p>
                            <ul class="text-sm space-y-1">
                                <li>$t_r \approx \frac{1.8}{\omega_n}$ (underdamped)</li>
                                <li>$t_s \approx \frac{4}{\zeta\omega_n}$ (2% criterion)</li>
                                <li>$M_p = e^{-\pi\zeta/\sqrt{1-\zeta^2}}$</li>
                                <li>$t_p = \frac{\pi}{\omega_n\sqrt{1-\zeta^2}}$</li>
                            </ul>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-xl font-semibold mb-4">Optimization Strategies</h4>
                        
                        <div class="highlight-box">
                            <h5 class="font-bold mb-3">Multi-Objective Optimization</h5>
                            <p class="mb-2">Typical competing objectives:</p>
                            <ul class="space-y-1 text-sm">
                                <li>• Fast response ↔ Low overshoot</li>
                                <li>• High bandwidth ↔ Low noise sensitivity</li>
                                <li>• Light weight ↔ High stiffness</li>
                                <li>• Low cost ↔ High performance</li>
                            </ul>
                        </div>

                        <div class="info-box">
                            <h5 class="font-bold mb-3">Pareto Optimization</h5>
                            <p class="mb-2">Find Pareto-optimal solutions where improving one objective requires degrading another.</p>
                            <p class="text-sm">Use techniques like weighted sum, ε-constraint, or genetic algorithms.</p>
                        </div>

                        <div class="math-box">
                            <h5 class="font-bold mb-3">Constraint Handling</h5>
                            <p class="mb-2">Typical constraints:</p>
                            <ul class="text-sm space-y-1">
                                <li>• $\zeta_{min} \leq \zeta \leq \zeta_{max}$ (damping bounds)</li>
                                <li>• $\omega_{n,min} \leq \omega_n \leq \omega_{n,max}$ (frequency bounds)</li>
                                <li>• $M_p \leq M_{p,max}$ (overshoot limit)</li>
                                <li>• $t_s \leq t_{s,max}$ (settling time limit)</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="code-block python-code">
# Design optimization example: Automotive suspension system
import numpy as np
from scipy.optimize import minimize, differential_evolution
import matplotlib.pyplot as plt

class SuspensionDesign:
    """
    Automotive suspension design optimization
    Objective: Minimize weighted combination of ride comfort and handling
    """
    
    def __init__(self, sprung_mass=300, unsprung_mass=50):
        self.ms = sprung_mass      # Sprung mass (kg) - car body
        self.mu = unsprung_mass    # Unsprung mass (kg) - wheel assembly
        
    def performance_metrics(self, k, c):
        """Calculate key performance metrics"""
        # Natural frequency and damping ratio
        omega_n = np.sqrt(k / self.ms)
        zeta = c / (2 * np.sqrt(k * self.ms))
        
        # Time domain metrics (step response)
        if zeta < 1:
            omega_d = omega_n * np.sqrt(1 - zeta**2)
            overshoot = np.exp(-np.pi * zeta / np.sqrt(1 - zeta**2))
            settling_time = 4 / (zeta * omega_n)
            peak_time = np.pi / omega_d
        else:
            overshoot = 0
            settling_time = 4 / omega_n  # Approximation
            peak_time = np.inf
        
        # Frequency domain metrics
        transmissibility_peak = 1 / (2 * zeta) if zeta < 0.707 else 1
        bandwidth = 2 * zeta * omega_n
        
        return {
            'omega_n': omega_n,
            'zeta': zeta,
            'overshoot': overshoot,
            'settling_time': settling_time,
            'peak_time': peak_time,
            'transmissibility_peak': transmissibility_peak,
            'bandwidth': bandwidth
        }
    
    def ride_comfort_index(self, k, c):
        """Ride comfort (lower is better)"""
        metrics = self.performance_metrics(k, c)
        
        # Comfort related to low acceleration transmission
        # and moderate damping
        comfort_penalty = 0
        
        # Penalize high transmissibility (harsh ride)
        if metrics['transmissibility_peak'] > 1.2:
            comfort_penalty += (metrics['transmissibility_peak'] - 1.2)**2
        
        # Penalize very low or very high damping
        optimal_zeta = 0.3
        comfort_penalty += 10 * (metrics['zeta'] - optimal_zeta)**2
        
        # Penalize high natural frequency (stiff suspension)
        if metrics['omega_n'] > 2.0:
            comfort_penalty += (metrics['omega_n'] - 2.0)**2
        
        return comfort_penalty
    
    def handling_index(self, k, c):
        """Vehicle handling (lower is better)"""
        metrics = self.performance_metrics(k, c)
        
        handling_penalty = 0
        
        # Penalize excessive body roll (low stiffness)
        if metrics['omega_n'] < 1.0:
            handling_penalty += (1.0 - metrics['omega_n'])**2
        
        # Penalize excessive overshoot (instability)
        if metrics['overshoot'] > 0.2:
            handling_penalty += 10 * (metrics['overshoot'] - 0.2)**2
        
        # Penalize slow response (high settling time)
        if metrics['settling_time'] > 3.0:
            handling_penalty += (metrics['settling_time'] - 3.0)**2
        
        return handling_penalty
    
    def objective_function(self, params, weights=[0.6, 0.4]):
        """Multi-objective function (comfort vs handling)"""
        k, c = params
        
        # Individual objectives
        comfort = self.ride_comfort_index(k, c)
        handling = self.handling_index(k, c)
        
        # Weighted combination
        total_cost = weights[0] * comfort + weights[1] * handling
        
        return total_cost
    
    def constraints(self, params):
        """Design constraints"""
        k, c = params
        metrics = self.performance_metrics(k, c)
        
        constraints = []
        
        # Stability constraint (damping ratio bounds)
        constraints.append(metrics['zeta'] - 0.1)   # zeta >= 0.1
        constraints.append(1.0 - metrics['zeta'])   # zeta <= 1.0
        
        # Performance constraints
        constraints.append(5.0 - metrics['settling_time'])  # ts <= 5s
        constraints.append(0.5 - metrics['overshoot'])      # Mp <= 50%
        
        return np.array(constraints)
    
    def optimize_design(self, method='differential_evolution'):
        """Optimize suspension parameters"""
        # Parameter bounds: k (N/m), c (N⋅s/m)
        bounds = [(5000, 50000), (500, 5000)]
        
        if method == 'differential_evolution':
            # Global optimization
            result = differential_evolution(
                self.objective_function,
                bounds,
                maxiter=100,
                seed=42
            )
        else:
            # Local optimization
            x0 = [20000, 2000]  # Initial guess
            result = minimize(
                self.objective_function,
                x0,
                method='L-BFGS-B',
                bounds=bounds
            )
        
        if result.success:
            k_opt, c_opt = result.x
            metrics = self.performance_metrics(k_opt, c_opt)
            
            return {
                'stiffness': k_opt,
                'damping': c_opt,
                'cost': result.fun,
                'metrics': metrics,
                'success': True
            }
        else:
            return {'success': False, 'message': result.message}
    
    def pareto_analysis(self, n_points=50):
        """Generate Pareto front for comfort vs handling"""
        # Range of weight combinations
        w1_range = np.linspace(0.1, 0.9, n_points)
        
        pareto_solutions = []
        
        for w1 in w1_range:
            w2 = 1 - w1
            weights = [w1, w2]
            
            # Optimize for this weight combination
            bounds = [(5000, 50000), (500, 5000)]
            result = differential_evolution(
                lambda params: self.objective_function(params, weights),
                bounds,
                maxiter=50,
                seed=42
            )
            
            if result.success:
                k, c = result.x
                comfort = self.ride_comfort_index(k, c)
                handling = self.handling_index(k, c)
                metrics = self.performance_metrics(k, c)
                
                pareto_solutions.append({
                    'weights': weights,
                    'stiffness': k,
                    'damping': c,
                    'comfort': comfort,
                    'handling': handling,
                    'metrics': metrics
                })
        
        return pareto_solutions
    
    def plot_pareto_front(self, pareto_solutions):
        """Plot Pareto front and design space"""
        comfort_values = [sol['comfort'] for sol in pareto_solutions]
        handling_values = [sol['handling'] for sol in pareto_solutions]
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Suspension Design Optimization Results', fontsize=16)
        
        # Pareto front
        ax1.plot(comfort_values, handling_values, 'ro-', linewidth=2, markersize=4)
        ax1.set_xlabel('Ride Comfort Index (lower = better)')
        ax1.set_ylabel('Handling Index (lower = better)')
        ax1.set_title('Pareto Front: Comfort vs Handling')
        ax1.grid(True, alpha=0.3)
        
        # Parameter space
        stiffness_values = [sol['stiffness']/1000 for sol in pareto_solutions]
        damping_values = [sol['damping']/1000 for sol in pareto_solutions]
        
        scatter = ax2.scatter(stiffness_values, damping_values, 
                            c=comfort_values, cmap='viridis', s=50)
        ax2.set_xlabel('Stiffness (kN/m)')
        ax2.set_ylabel('Damping (kN⋅s/m)')
        ax2.set_title('Design Parameter Space')
        ax2.grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=ax2, label='Comfort Index')
        
        # Performance metrics
        zeta_values = [sol['metrics']['zeta'] for sol in pareto_solutions]
        omega_n_values = [sol['metrics']['omega_n'] for sol in pareto_solutions]
        
        ax3.plot(omega_n_values, zeta_values, 'bo-', linewidth=2, markersize=4)
        ax3.set_xlabel('Natural Frequency (rad/s)')
        ax3.set_ylabel('Damping Ratio')
        ax3.set_title('System Characteristics')
        ax3.grid(True, alpha=0.3)
        
        # Trade-off visualization
        weights_comfort = [sol['weights'][0] for sol in pareto_solutions]
        ax4.plot(weights_comfort, comfort_values, 'g-', label='Comfort', linewidth=2)
        ax4.plot(weights_comfort, handling_values, 'r-', label='Handling', linewidth=2)
        ax4.set_xlabel('Comfort Weight')
        ax4.set_ylabel('Objective Value')
        ax4.set_title('Objective Trade-off')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig

# Example usage
suspension = SuspensionDesign(sprung_mass=300, unsprung_mass=50)

# Single optimization
optimal = suspension.optimize_design()
if optimal['success']:
    print("Optimal Suspension Design:")
    print(f"Stiffness: {optimal['stiffness']:.0f} N/m")
    print(f"Damping: {optimal['damping']:.0f} N⋅s/m")
    print(f"Natural frequency: {optimal['metrics']['omega_n']:.2f} rad/s")
    print(f"Damping ratio: {optimal['metrics']['zeta']:.3f}")
    print(f"Overshoot: {optimal['metrics']['overshoot']*100:.1f}%")
    print(f"Settling time: {optimal['metrics']['settling_time']:.2f} s")

# Pareto analysis
print("\nGenerating Pareto front...")
pareto_solutions = suspension.pareto_analysis(20)
fig = suspension.plot_pareto_front(pareto_solutions)
plt.show()
                </div>
            </div>
        </section>

        <!-- Summary and Applications -->
        <section class="section-divider">
            <h2 class="text-4xl font-bold mb-8 text-purple-600"><i class="fas fa-graduation-cap mr-3"></i>Chapter Summary and Engineering Applications</h2>

            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600"><i class="fas fa-check-circle mr-2"></i>Key Learning Outcomes</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-semibold mb-4">Mathematical Mastery</h4>
                        <ul class="space-y-2">
                            <li class="flex items-start"><i class="fas fa-check text-green-500 mt-1 mr-2"></i><span>Complete solution methodology for second-order linear ODEs</span></li>
                            <li class="flex items-start"><i class="fas fa-check text-green-500 mt-1 mr-2"></i><span>Characteristic equation method and root analysis</span></li>
                            <li class="flex items-start"><i class="fas fa-check text-green-500 mt-1 mr-2"></i><span>Forced response analysis and resonance phenomena</span></li>
                            <li class="flex items-start"><i class="fas fa-check text-green-500 mt-1 mr-2"></i><span>Complex impedance and frequency domain analysis</span></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-xl font-semibold mb-4">Engineering Applications</h4>
                        <ul class="space-y-2">
                            <li class="flex items-start"><i class="fas fa-check text-green-500 mt-1 mr-2"></i><span>Spring-mass-damper system design and optimization</span></li>
                            <li class="flex items-start"><i class="fas fa-check text-green-500 mt-1 mr-2"></i><span>RLC circuit analysis and filter design</span></li>
                            <li class="flex items-start"><i class="fas fa-check text-green-500 mt-1 mr-2"></i><span>Multi-degree-of-freedom systems and coupling</span></li>
                            <li class="flex items-start"><i class="fas fa-check text-green-500 mt-1 mr-2"></i><span>Parameter estimation and system identification</span></li>
                        </ul>
                    </div>
                </div>
                
                <div class="highlight-box mt-8">
                    <h4 class="text-xl font-semibold mb-4"><i class="fas fa-lightbulb mr-2"></i>Key Insights and Connections</h4>
                    <ul class="space-y-2">
                        <li><strong>Mathematical Universality:</strong> The same differential equation form describes diverse physical systems, demonstrating the power of mathematical abstraction</li>
                        <li><strong>Design Trade-offs:</strong> Engineering design involves balancing competing objectives through mathematical optimization</li>
                        <li><strong>Frequency Domain Power:</strong> Complex analysis and frequency domain methods provide powerful tools for system analysis and design</li>
                        <li><strong>Computational Integration:</strong> Modern engineering combines analytical understanding with computational tools for comprehensive analysis</li>
                    </ul>
                </div>
                
                <div class="success-box mt-8">
                    <h4 class="text-xl font-semibold mb-4"><i class="fas fa-trophy mr-2"></i>Real-World Impact</h4>
                    <p class="mb-4">The concepts and methods presented in this chapter form the foundation for numerous engineering applications:</p>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h5 class="font-bold mb-2">Mechanical Engineering:</h5>
                            <ul class="text-sm space-y-1">
                                <li>• Automotive suspension design</li>
                                <li>• Building seismic isolation</li>
                                <li>• Machine vibration control</li>
                                <li>• Robotics and mechatronics</li>
                            </ul>
                        </div>
                        <div>
                            <h5 class="font-bold mb-2">Electrical Engineering:</h5>
                            <ul class="text-sm space-y-1">
                                <li>• Power system stability</li>
                                <li>• Signal processing filters</li>
                                <li>• Control system design</li>
                                <li>• Wireless communication</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Final Notes -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold mb-6 text-blue-600"><i class="fas fa-star mr-2"></i>Final Thoughts and Next Steps</h3>
                
                <p class="text-lg mb-6">
                    This chapter has demonstrated how fundamental differential equation theory translates directly into practical engineering solutions. The mathematical elegance of second-order linear ODEs provides a unified framework for understanding and designing complex dynamic systems across multiple engineering disciplines.
                </p>
                
                <div class="info-box">
                    <h4 class="font-bold mb-3">Continuing Your Journey</h4>
                    <ul class="space-y-2">
                        <li><strong>Advanced Topics:</strong> Explore partial differential equations, nonlinear dynamics, and control theory</li>
                        <li><strong>Specialized Applications:</strong> Study field-specific applications in your area of interest</li>
                        <li><strong>Computational Methods:</strong> Develop expertise in numerical methods and simulation tools</li>
                        <li><strong>Experimental Skills:</strong> Combine theoretical knowledge with hands-on laboratory experience</li>
                    </ul>
                </div>
                
                <div class="warning-box">
                    <h4 class="font-bold mb-3"><i class="fas fa-exclamation-triangle mr-2"></i>Remember</h4>
                    <p>While mathematical models provide powerful insights, always validate theoretical predictions with experimental results and consider the limitations and assumptions of your models in real-world applications.</p>
                </div>
            </div>
        </section>
    </div>

    <!-- Chart.js Implementations -->
    <script>
        // Damping Chart Implementation
        const dampingCtx = document.getElementById('dampingChart').getContext('2d');
        const dampingChart = new Chart(dampingCtx, {
            type: 'line',
            data: {
                labels: Array.from({length: 100}, (_, i) => (i * 0.1).toFixed(1)),
                datasets: [
                    {
                        label: 'Underdamped (ζ = 0.2)',
                        data: Array.from({length: 100}, (_, i) => {
                            const t = i * 0.1;
                            const zeta = 0.2, omega_n = 2;
                            const omega_d = omega_n * Math.sqrt(1 - zeta*zeta);
                            return Math.exp(-zeta * omega_n * t) * Math.cos(omega_d * t);
                        }),
                        borderColor: 'blue',
                        backgroundColor: 'transparent',
                        borderWidth: 2
                    },
                    {
                        label: 'Critically Damped (ζ = 1.0)',
                        data: Array.from({length: 100}, (_, i) => {
                            const t = i * 0.1;
                            const omega_n = 2;
                            return (1 + omega_n * t) * Math.exp(-omega_n * t);
                        }),
                        borderColor: 'red',
                        backgroundColor: 'transparent',
                        borderWidth: 2
                    },
                    {
                        label: 'Overdamped (ζ = 2.0)',
                        data: Array.from({length: 100}, (_, i) => {
                            const t = i * 0.1;
                            const zeta = 2.0, omega_n = 2;
                            const s1 = -zeta * omega_n + omega_n * Math.sqrt(zeta*zeta - 1);
                            const s2 = -zeta * omega_n - omega_n * Math.sqrt(zeta*zeta - 1);
                            const A = -s2 / (s1 - s2);
                            const B = s1 / (s1 - s2);
                            return A * Math.exp(s1 * t) + B * Math.exp(s2 * t);
                        }),
                        borderColor: 'green',
                        backgroundColor: 'transparent',
                        borderWidth: 2
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Time (s)'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Amplitude'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Spring-Mass-Damper Response Comparison'
                    }
                }
            }
        });

        // RLC Chart Implementation
        const rlcCtx = document.getElementById('rlcChart').getContext('2d');
        const rlcChart = new Chart(rlcCtx, {
            type: 'line',
            data: {
                labels: Array.from({length: 100}, (_, i) => (i * 0.05).toFixed(2)),
                datasets: [
                    {
                        label: 'Underdamped RLC',
                        data: Array.from({length: 100}, (_, i) => {
                            const t = i * 0.05;
                            const alpha = 1, omega_0 = 3;
                            const omega_d = Math.sqrt(omega_0*omega_0 - alpha*alpha);
                            return Math.exp(-alpha * t) * Math.cos(omega_d * t);
                        }),
                        borderColor: 'purple',
                        backgroundColor: 'transparent',
                        borderWidth: 2
                    },
                    {
                        label: 'Critically Damped RLC',
                        data: Array.from({length: 100}, (_, i) => {
                            const t = i * 0.05;
                            const alpha = 2;
                            return (1 + alpha * t) * Math.exp(-alpha * t);
                        }),
                        borderColor: 'orange',
                        backgroundColor: 'transparent',
                        borderWidth: 2
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Time (s)'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Normalized Response'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'RLC Circuit Natural Response'
                    }
                }
            }
        });

        // Filter Chart Implementation
        const filterCtx = document.getElementById('filterChart').getContext('2d');
        const frequencies = Array.from({length: 200}, (_, i) => Math.pow(10, -1 + i * 3 / 199));
        
        const filterChart = new Chart(filterCtx, {
            type: 'line',
            data: {
                labels: frequencies.map(f => f.toFixed(3)),
                datasets: [
                    {
                        label: 'Low-Pass Filter',
                        data: frequencies.map(f => {
                            const omega = 2 * Math.PI * f;
                            const fc = 1; // Cutoff frequency
                            const omega_c = 2 * Math.PI * fc;
                            const H = 1 / Math.sqrt(1 + Math.pow(omega / omega_c, 2));
                            return 20 * Math.log10(H);
                        }),
                        borderColor: 'blue',
                        backgroundColor: 'transparent',
                        borderWidth: 2
                    },
                    {
                        label: 'High-Pass Filter',
                        data: frequencies.map(f => {
                            const omega = 2 * Math.PI * f;
                            const fc = 1; // Cutoff frequency
                            const omega_c = 2 * Math.PI * fc;
                            const H = (omega / omega_c) / Math.sqrt(1 + Math.pow(omega / omega_c, 2));
                            return 20 * Math.log10(H);
                        }),
                        borderColor: 'red',
                        backgroundColor: 'transparent',
                        borderWidth: 2
                    },
                    {
                        label: 'Band-Pass Filter',
                        data: frequencies.map(f => {
                            const omega = 2 * Math.PI * f;
                            const omega_0 = 2 * Math.PI; // Center frequency
                            const Q = 5; // Quality factor
                            const H = 1 / Math.sqrt(1 + Math.pow(Q * (omega / omega_0 - omega_0 / omega), 2));
                            return 20 * Math.log10(H);
                        }),
                        borderColor: 'green',
                        backgroundColor: 'transparent',
                        borderWidth: 2
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'logarithmic',
                        title: {
                            display: true,
                            text: 'Frequency (Hz)'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Magnitude (dB)'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Filter Frequency Response Comparison'
                    }
                }
            }
        });
    </script>
</body>
</html>
    