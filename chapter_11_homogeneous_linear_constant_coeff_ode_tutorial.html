<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 11: Homogeneous Linear Equations with Constant Coefficients - ODE Tutorial</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/r.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true
            }
        };
    </script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .code-block {
            background: #0d1117;
            color: #f0f6fc;
            padding: 1.5rem;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            border: 1px solid #30363d;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .code-block pre {
            margin: 0;
            padding: 0;
            background: transparent;
            border: none;
        }
        .code-block code {
            font-family: inherit;
            font-size: inherit;
            background: transparent;
            padding: 0;
        }
        .python { 
            border-left: 4px solid #3776ab; 
        }
        .r { 
            border-left: 4px solid #276dc3; 
        }
        .hljs {
            background: transparent !important;
        }
        .math-container {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .theorem-box {
            background: #eff6ff;
            border: 2px solid #3b82f6;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .example-box {
            background: #f0fff4;
            border: 2px solid #10b981;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .application-box {
            background: #fefce8;
            border: 2px solid #eab308;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .chart-container {
            height: 400px;
            margin: 1rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }
        .chart-container canvas {
            max-height: 100% !important;
            max-width: 100% !important;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900 leading-relaxed">
    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Header -->
        <header class="text-center mb-12 border-b-4 border-blue-600 pb-8">
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-6 px-8 rounded-lg shadow-lg">
                <h1 class="text-4xl font-bold mb-2">
                    <i class="fas fa-calculator mr-3"></i>
                    Chapter 11: Homogeneous Linear Equations with Constant Coefficients
                </h1>
                <p class="text-xl opacity-90">Part 3: Second-Order and Higher-Order Linear ODEs</p>
                <p class="text-lg opacity-80 mt-2">Foundation of Second-Order Differential Equations</p>
            </div>
        </header>

        <!-- Learning Objectives -->
        <section class="mb-12">
            <div class="bg-blue-50 border-l-4 border-blue-500 p-6 rounded-r-lg">
                <h2 class="text-2xl font-bold text-blue-800 mb-4">
                    <i class="fas fa-bullseye mr-2"></i>Learning Objectives
                </h2>
                <ul class="space-y-2 text-blue-700">
                    <li><i class="fas fa-check mr-2"></i>Understand the structure and properties of homogeneous linear ODEs with constant coefficients</li>
                    <li><i class="fas fa-check mr-2"></i>Master the characteristic equation method for finding solutions</li>
                    <li><i class="fas fa-check mr-2"></i>Analyze different root cases: real distinct, complex conjugate, and repeated roots</li>
                    <li><i class="fas fa-check mr-2"></i>Apply concepts of linear independence and the Wronskian determinant</li>
                    <li><i class="fas fa-check mr-2"></i>Model and solve real-world applications in mechanics and electrical circuits</li>
                    <li><i class="fas fa-check mr-2"></i>Implement computational solutions using Python and R</li>
                </ul>
            </div>
        </section>

        <!-- Introduction -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-gray-300 pb-2">
                <i class="fas fa-play-circle mr-2 text-blue-600"></i>Introduction to Second-Order Linear ODEs
            </h2>
            
            <div class="prose max-w-none">
                <p class="text-lg mb-6">
                    We now enter the rich world of second-order differential equations, where phenomena like oscillations, 
                    vibrations, and wave-like behavior become central. Second-order linear differential equations with 
                    constant coefficients form the foundation for understanding mechanical systems, electrical circuits, 
                    and many other physical phenomena.
                </p>

                <div class="theorem-box">
                    <h3 class="text-xl font-bold text-blue-800 mb-3">
                        <i class="fas fa-star mr-2"></i>Standard Form
                    </h3>
                    <p>A second-order linear homogeneous differential equation with constant coefficients has the form:</p>
                    <div class="math-container text-center">
                        $$ay'' + by' + cy = 0$$
                    </div>
                    <p>where $a$, $b$, and $c$ are real constants with $a \neq 0$.</p>
                </div>

                <p class="mb-4">
                    The word "homogeneous" means that every term contains the dependent variable $y$ or its derivatives. 
                    The word "linear" means that $y$ and its derivatives appear to the first power only, with no products 
                    of $y$ and its derivatives.
                </p>
            </div>
        </section>

        <!-- Characteristic Equation Method -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-gray-300 pb-2">
                <i class="fas fa-cogs mr-2 text-blue-600"></i>The Characteristic Equation Method
            </h2>

            <div class="prose max-w-none">
                <p class="text-lg mb-6">
                    The key insight for solving homogeneous linear equations with constant coefficients is to look for 
                    solutions of the form $y = e^{rx}$, where $r$ is a constant to be determined.
                </p>

                <div class="theorem-box">
                    <h3 class="text-xl font-bold text-blue-800 mb-3">
                        <i class="fas fa-lightbulb mr-2"></i>Method Derivation
                    </h3>
                    <p>If we assume $y = e^{rx}$, then:</p>
                    <ul class="list-disc ml-6 mb-4">
                        <li>$y' = re^{rx}$</li>
                        <li>$y'' = r^2e^{rx}$</li>
                    </ul>
                    <p>Substituting into $ay'' + by' + cy = 0$:</p>
                    <div class="math-container text-center">
                        $$a(r^2e^{rx}) + b(re^{rx}) + c(e^{rx}) = 0$$
                        $$e^{rx}(ar^2 + br + c) = 0$$
                    </div>
                    <p>Since $e^{rx} \neq 0$ for any finite $r$, we must have:</p>
                    <div class="math-container text-center">
                        $$ar^2 + br + c = 0$$
                    </div>
                    <p>This is called the <strong>characteristic equation</strong> or <strong>auxiliary equation</strong>.</p>
                </div>
            </div>
        </section>

        <!-- Case Analysis -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-gray-300 pb-2">
                <i class="fas fa-sitemap mr-2 text-blue-600"></i>Case Analysis: Types of Roots
            </h2>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <div class="bg-green-50 border-2 border-green-500 rounded-lg p-4">
                    <h3 class="text-lg font-bold text-green-800 mb-2">Case 1: Real Distinct Roots</h3>
                    <p class="text-green-700">$b^2 - 4ac > 0$</p>
                    <p class="text-sm text-green-600">Two different real solutions</p>
                </div>
                <div class="bg-blue-50 border-2 border-blue-500 rounded-lg p-4">
                    <h3 class="text-lg font-bold text-blue-800 mb-2">Case 2: Complex Conjugate</h3>
                    <p class="text-blue-700">$b^2 - 4ac < 0$</p>
                    <p class="text-sm text-blue-600">Oscillatory solutions</p>
                </div>
                <div class="bg-purple-50 border-2 border-purple-500 rounded-lg p-4">
                    <h3 class="text-lg font-bold text-purple-800 mb-2">Case 3: Repeated Root</h3>
                    <p class="text-purple-700">$b^2 - 4ac = 0$</p>
                    <p class="text-sm text-purple-600">One repeated solution</p>
                </div>
            </div>

            <!-- Case 1: Real Distinct Roots -->
            <div class="example-box">
                <h3 class="text-xl font-bold text-green-800 mb-4">
                    <i class="fas fa-chart-line mr-2"></i>Case 1: Real Distinct Roots
                </h3>
                <p class="mb-4">When $b^2 - 4ac > 0$, the characteristic equation has two distinct real roots $r_1$ and $r_2$:</p>
                <div class="math-container text-center">
                    $$r_{1,2} = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$$
                </div>
                <p class="mb-4">The general solution is:</p>
                <div class="math-container text-center">
                    $$y = C_1 e^{r_1 x} + C_2 e^{r_2 x}$$
                </div>

                <div class="bg-white p-4 rounded border-l-4 border-green-500 mt-4">
                    <h4 class="font-bold text-green-800 mb-2">Example:</h4>
                    <p>Solve: $y'' - 5y' + 6y = 0$</p>
                    <p class="mt-2"><strong>Solution:</strong></p>
                    <p>Characteristic equation: $r^2 - 5r + 6 = 0$</p>
                    <p>Factoring: $(r-2)(r-3) = 0$, so $r_1 = 2$, $r_2 = 3$</p>
                    <p>General solution: $y = C_1 e^{2x} + C_2 e^{3x}$</p>
                </div>
            </div>

            <!-- Case 2: Complex Conjugate Roots -->
            <div class="example-box">
                <h3 class="text-xl font-bold text-blue-800 mb-4">
                    <i class="fas fa-wave-square mr-2"></i>Case 2: Complex Conjugate Roots
                </h3>
                <p class="mb-4">When $b^2 - 4ac < 0$, the characteristic equation has complex conjugate roots:</p>
                <div class="math-container text-center">
                    $$r_{1,2} = \frac{-b \pm i\sqrt{4ac - b^2}}{2a} = \alpha \pm i\beta$$
                </div>
                <p class="mb-4">where $\alpha = -\frac{b}{2a}$ and $\beta = \frac{\sqrt{4ac - b^2}}{2a}$</p>
                <p class="mb-4">The general solution is:</p>
                <div class="math-container text-center">
                    $$y = e^{\alpha x}(C_1 \cos(\beta x) + C_2 \sin(\beta x))$$
                </div>

                <div class="bg-white p-4 rounded border-l-4 border-blue-500 mt-4">
                    <h4 class="font-bold text-blue-800 mb-2">Example:</h4>
                    <p>Solve: $y'' + 4y' + 13y = 0$</p>
                    <p class="mt-2"><strong>Solution:</strong></p>
                    <p>Characteristic equation: $r^2 + 4r + 13 = 0$</p>
                    <p>Using quadratic formula: $r = \frac{-4 \pm \sqrt{16-52}}{2} = \frac{-4 \pm 6i}{2} = -2 \pm 3i$</p>
                    <p>So $\alpha = -2$, $\beta = 3$</p>
                    <p>General solution: $y = e^{-2x}(C_1 \cos(3x) + C_2 \sin(3x))$</p>
                </div>
            </div>

            <!-- Case 3: Repeated Root -->
            <div class="example-box">
                <h3 class="text-xl font-bold text-purple-800 mb-4">
                    <i class="fas fa-equals mr-2"></i>Case 3: Repeated Root
                </h3>
                <p class="mb-4">When $b^2 - 4ac = 0$, the characteristic equation has one repeated root:</p>
                <div class="math-container text-center">
                    $$r = \frac{-b}{2a}$$
                </div>
                <p class="mb-4">The general solution is:</p>
                <div class="math-container text-center">
                    $$y = (C_1 + C_2 x) e^{rx}$$
                </div>

                <div class="bg-white p-4 rounded border-l-4 border-purple-500 mt-4">
                    <h4 class="font-bold text-purple-800 mb-2">Example:</h4>
                    <p>Solve: $y'' - 6y' + 9y = 0$</p>
                    <p class="mt-2"><strong>Solution:</strong></p>
                    <p>Characteristic equation: $r^2 - 6r + 9 = 0$</p>
                    <p>Factoring: $(r-3)^2 = 0$, so $r = 3$ (repeated)</p>
                    <p>General solution: $y = (C_1 + C_2 x) e^{3x}$</p>
                </div>
            </div>
        </section>

        <!-- Linear Independence and Wronskian -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-gray-300 pb-2">
                <i class="fas fa-project-diagram mr-2 text-blue-600"></i>Linear Independence and the Wronskian
            </h2>

            <div class="prose max-w-none">
                <div class="theorem-box">
                    <h3 class="text-xl font-bold text-blue-800 mb-3">
                        <i class="fas fa-balance-scale mr-2"></i>Linear Independence
                    </h3>
                    <p class="mb-4">Two functions $y_1(x)$ and $y_2(x)$ are <strong>linearly independent</strong> on an interval $I$ if:</p>
                    <div class="math-container text-center">
                        $$C_1 y_1(x) + C_2 y_2(x) = 0 \text{ for all } x \in I$$
                    </div>
                    <p>implies $C_1 = C_2 = 0$.</p>
                </div>

                <div class="theorem-box">
                    <h3 class="text-xl font-bold text-blue-800 mb-3">
                        <i class="fas fa-calculator mr-2"></i>The Wronskian Determinant
                    </h3>
                    <p class="mb-4">The Wronskian of two functions $y_1$ and $y_2$ is:</p>
                    <div class="math-container text-center">
                        $$W(y_1, y_2) = \begin{vmatrix} y_1 & y_2 \\ y_1' & y_2' \end{vmatrix} = y_1 y_2' - y_1' y_2$$
                    </div>
                    <p class="mb-4"><strong>Key Property:</strong> If $W(y_1, y_2) \neq 0$ at some point in an interval, then $y_1$ and $y_2$ are linearly independent on that interval.</p>
                </div>

                <div class="example-box">
                    <h3 class="text-xl font-bold text-green-800 mb-4">
                        <i class="fas fa-calculator mr-2"></i>Wronskian Example
                    </h3>
                    <p>For the equation $y'' - 5y' + 6y = 0$ with solutions $y_1 = e^{2x}$ and $y_2 = e^{3x}$:</p>
                    <div class="math-container">
                        $$W(e^{2x}, e^{3x}) = \begin{vmatrix} e^{2x} & e^{3x} \\ 2e^{2x} & 3e^{3x} \end{vmatrix}$$
                        $$= e^{2x} \cdot 3e^{3x} - e^{3x} \cdot 2e^{2x} = 3e^{5x} - 2e^{5x} = e^{5x} \neq 0$$
                    </div>
                    <p>Since $W \neq 0$, the solutions are linearly independent.</p>
                </div>
            </div>
        </section>

        <!-- Applications -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-gray-300 pb-2">
                <i class="fas fa-cog mr-2 text-blue-600"></i>Physical Applications
            </h2>

            <!-- Simple Harmonic Motion -->
            <div class="application-box">
                <h3 class="text-xl font-bold text-yellow-800 mb-4">
                    <i class="fas fa-wave-square mr-2"></i>Simple Harmonic Motion
                </h3>
                <p class="mb-4">A mass attached to a spring undergoes simple harmonic motion described by:</p>
                <div class="math-container text-center">
                    $$m\frac{d^2x}{dt^2} + kx = 0$$
                </div>
                <p class="mb-4">where $m$ is mass and $k$ is the spring constant.</p>
                <p class="mb-4">Dividing by $m$: $\frac{d^2x}{dt^2} + \frac{k}{m}x = 0$</p>
                <p class="mb-4">Let $\omega^2 = \frac{k}{m}$. The characteristic equation is $r^2 + \omega^2 = 0$</p>
                <p class="mb-4">Roots: $r = \pm i\omega$ (complex conjugate)</p>
                <p class="mb-4">General solution: $x(t) = C_1 \cos(\omega t) + C_2 \sin(\omega t)$</p>
                <div class="bg-yellow-100 p-4 rounded mt-4">
                    <p><strong>Physical Interpretation:</strong></p>
                    <ul class="list-disc ml-6">
                        <li>$\omega = \sqrt{\frac{k}{m}}$ is the natural frequency</li>
                        <li>Period: $T = \frac{2\pi}{\omega}$</li>
                        <li>The motion is purely oscillatory (no damping)</li>
                    </ul>
                </div>
            </div>

            <!-- Damped Oscillations -->
            <div class="application-box">
                <h3 class="text-xl font-bold text-yellow-800 mb-4">
                    <i class="fas fa-chart-line mr-2"></i>Damped Harmonic Motion
                </h3>
                <p class="mb-4">With damping (friction/resistance), the equation becomes:</p>
                <div class="math-container text-center">
                    $$m\frac{d^2x}{dt^2} + c\frac{dx}{dt} + kx = 0$$
                </div>
                <p class="mb-4">where $c$ is the damping coefficient.</p>
                <p class="mb-4">Standard form: $\frac{d^2x}{dt^2} + 2\gamma\frac{dx}{dt} + \omega_0^2 x = 0$</p>
                <p class="mb-4">where $\gamma = \frac{c}{2m}$ and $\omega_0^2 = \frac{k}{m}$</p>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    <div class="bg-red-50 p-3 rounded border-l-4 border-red-400">
                        <h4 class="font-bold text-red-800">Overdamped</h4>
                        <p class="text-sm text-red-700">$\gamma^2 > \omega_0^2$</p>
                        <p class="text-xs">Two real roots, no oscillation</p>
                    </div>
                    <div class="bg-yellow-50 p-3 rounded border-l-4 border-yellow-400">
                        <h4 class="font-bold text-yellow-800">Critically Damped</h4>
                        <p class="text-sm text-yellow-700">$\gamma^2 = \omega_0^2$</p>
                        <p class="text-xs">Repeated root, fastest return to equilibrium</p>
                    </div>
                    <div class="bg-blue-50 p-3 rounded border-l-4 border-blue-400">
                        <h4 class="font-bold text-blue-800">Underdamped</h4>
                        <p class="text-sm text-blue-700">$\gamma^2 < \omega_0^2$</p>
                        <p class="text-xs">Complex roots, damped oscillation</p>
                    </div>
                </div>
            </div>

            <!-- RLC Circuits -->
            <div class="application-box">
                <h3 class="text-xl font-bold text-yellow-800 mb-4">
                    <i class="fas fa-bolt mr-2"></i>RLC Electrical Circuits
                </h3>
                <p class="mb-4">An RLC circuit (Resistor-Inductor-Capacitor) follows Kirchhoff's voltage law:</p>
                <div class="math-container text-center">
                    $$L\frac{d^2q}{dt^2} + R\frac{dq}{dt} + \frac{q}{C} = 0$$
                </div>
                <p class="mb-4">where:</p>
                <ul class="list-disc ml-6 mb-4">
                    <li>$q(t)$ is the charge on the capacitor</li>
                    <li>$L$ is inductance</li>
                    <li>$R$ is resistance</li>
                    <li>$C$ is capacitance</li>
                </ul>
                <p class="mb-4">This has the same form as the damped harmonic oscillator!</p>
                <div class="bg-yellow-100 p-4 rounded mt-4">
                    <p><strong>Circuit-Mechanical Analogy:</strong></p>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <p class="font-semibold">Mechanical</p>
                            <ul class="list-disc ml-4">
                                <li>Mass $m$ ↔ Inductance $L$</li>
                                <li>Damping $c$ ↔ Resistance $R$</li>
                                <li>Spring constant $k$ ↔ $1/C$</li>
                            </ul>
                        </div>
                        <div>
                            <p class="font-semibold">Electrical</p>
                            <ul class="list-disc ml-4">
                                <li>Position $x$ ↔ Charge $q$</li>
                                <li>Velocity $v$ ↔ Current $i$</li>
                                <li>Force $F$ ↔ Voltage $V$</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Computational Implementation -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-gray-300 pb-2">
                <i class="fas fa-code mr-2 text-blue-600"></i>Computational Implementation
            </h2>

            <!-- Python Implementation -->
            <div class="mb-8">
                <h3 class="text-2xl font-bold text-gray-700 mb-4">
                    <i class="fab fa-python mr-2 text-blue-500"></i>Python Implementation
                </h3>

                <div class="code-block python mb-6">
                    <pre><code class="language-python">import numpy as np
import matplotlib.pyplot as plt
import sympy as sp
from sympy import symbols, Function, Eq, dsolve, I, exp, cos, sin, sqrt

# Define symbolic variables
x, r = symbols('x r')
y = Function('y')

def solve_homogeneous_constant_coeff(a, b, c):
    """
    Solve ay'' + by' + cy = 0 using characteristic equation method
    """
    print(f"Solving: {a}y'' + {b}y' + {c}y = 0")
    
    # Characteristic equation: ar² + br + c = 0
    char_eq = a*r**2 + b*r + c
    print(f"Characteristic equation: {char_eq} = 0")
    
    # Solve characteristic equation
    roots = sp.solve(char_eq, r)
    print(f"Roots: {roots}")
    
    # Analyze root types and construct solution
    if len(roots) == 2:
        r1, r2 = roots
        
        # Check if roots are real and distinct
        if r1.is_real and r2.is_real and r1 != r2:
            print("Case: Real distinct roots")
            solution = "C1*exp({0}*x) + C2*exp({1}*x)".format(r1, r2)
            
        # Check if roots are complex conjugates
        elif r1.is_real == False:
            print("Case: Complex conjugate roots")
            alpha = sp.re(r1)
            beta = sp.im(r1)
            print(f"α = {alpha}, β = {beta}")
            solution = "exp({0}*x)*(C1*cos({1}*x) + C2*sin({1}*x))".format(alpha, beta)
            
    else:  # Repeated root
        print("Case: Repeated root")
        r_repeated = roots[0]
        solution = "(C1 + C2*x)*exp({0}*x)".format(r_repeated)
    
    print(f"General solution: y = {solution}")
    return solution

# Example 1: Real distinct roots
print("=== Example 1: Real Distinct Roots ===")
solution1 = solve_homogeneous_constant_coeff(1, -5, 6)

print("\n=== Example 2: Complex Conjugate Roots ===")
solution2 = solve_homogeneous_constant_coeff(1, 4, 13)

print("\n=== Example 3: Repeated Root ===")
solution3 = solve_homogeneous_constant_coeff(1, -6, 9)

# Visualization function
def plot_solutions():
    """Plot different types of solutions"""
    x_vals = np.linspace(0, 3, 1000)
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # Real distinct roots: y = e^(2x) + e^(3x)
    y1 = np.exp(2*x_vals) + np.exp(3*x_vals)
    axes[0,0].plot(x_vals, y1, 'b-', linewidth=2, label='$y = e^{2x} + e^{3x}$')
    axes[0,0].set_title('Real Distinct Roots', fontsize=14, fontweight='bold')
    axes[0,0].set_xlabel('x')
    axes[0,0].set_ylabel('y')
    axes[0,0].grid(True, alpha=0.3)
    axes[0,0].legend()
    
    # Complex conjugate roots: y = e^(-2x)cos(3x)
    y2 = np.exp(-2*x_vals) * np.cos(3*x_vals)
    axes[0,1].plot(x_vals, y2, 'r-', linewidth=2, label='$y = e^{-2x}\cos(3x)$')
    axes[0,1].set_title('Complex Conjugate Roots', fontsize=14, fontweight='bold')
    axes[0,1].set_xlabel('x')
    axes[0,1].set_ylabel('y')
    axes[0,1].grid(True, alpha=0.3)
    axes[0,1].legend()
    
    # Repeated root: y = (1 + x)e^(3x)
    y3 = (1 + x_vals) * np.exp(3*x_vals)
    axes[1,0].plot(x_vals, y3, 'g-', linewidth=2, label='$y = (1 + x)e^{3x}$')
    axes[1,0].set_title('Repeated Root', fontsize=14, fontweight='bold')
    axes[1,0].set_xlabel('x')
    axes[1,0].set_ylabel('y')
    axes[1,0].grid(True, alpha=0.3)
    axes[1,0].legend()
    
    # Damped oscillation comparison
    x_vals_long = np.linspace(0, 5, 1000)
    y_underdamped = np.exp(-0.5*x_vals_long) * np.cos(2*x_vals_long)
    y_overdamped = np.exp(-x_vals_long) + 0.5*np.exp(-2*x_vals_long)
    y_critical = (1 + x_vals_long) * np.exp(-x_vals_long)
    
    axes[1,1].plot(x_vals_long, y_underdamped, 'b-', linewidth=2, label='Underdamped')
    axes[1,1].plot(x_vals_long, y_overdamped, 'r-', linewidth=2, label='Overdamped')
    axes[1,1].plot(x_vals_long, y_critical, 'g-', linewidth=2, label='Critical')
    axes[1,1].set_title('Damping Comparison', fontsize=14, fontweight='bold')
    axes[1,1].set_xlabel('x')
    axes[1,1].set_ylabel('y')
    axes[1,1].grid(True, alpha=0.3)
    axes[1,1].legend()
    
    plt.tight_layout()
    plt.show()

# Calculate Wronskian
def wronskian(f1, f2, var):
    """Calculate Wronskian determinant"""
    f1_prime = sp.diff(f1, var)
    f2_prime = sp.diff(f2, var)
    W = f1 * f2_prime - f1_prime * f2
    return sp.simplify(W)

# Example: Wronskian for real distinct roots
x = symbols('x')
f1 = exp(2*x)
f2 = exp(3*x)
W = wronskian(f1, f2, x)
print(f"\nWronskian of e^(2x) and e^(3x): {W}")
</code></pre>

                <div class="bg-green-50 border-l-4 border-green-500 p-4 rounded">
                    <h4 class="font-bold text-green-800 mb-2">Key Python Features:</h4>
                    <ul class="list-disc ml-6 text-green-700">
                        <li><strong>SymPy</strong>: Symbolic computation for exact solutions</li>
                        <li><strong>Automatic root classification</strong>: Determines solution type</li>
                        <li><strong>Wronskian calculation</strong>: Tests linear independence</li>
                        <li><strong>Visualization</strong>: Plots different solution behaviors</li>
                    </ul>
                </div>
            </div>

            <!-- R Implementation -->
            <div class="mb-8">
                <h3 class="text-2xl font-bold text-gray-700 mb-4">
                    <i class="fab fa-r-project mr-2 text-blue-600"></i>R Implementation
                </h3>

                <div class="code-block r mb-6">
                    <pre><code class="language-r"># Load required libraries
library(pracma)
library(ggplot2)
library(gridExtra)

# Function to solve homogeneous constant coefficient ODEs
solve_homogeneous_ode <- function(a, b, c) {
  cat("Solving:", a, "y'' +", b, "y' +", c, "y = 0\n")
  
  # Characteristic equation coefficients
  coeffs <- c(a, b, c)
  
  # Solve characteristic equation
  roots <- polyroot(coeffs)
  cat("Roots:", roots, "\n")
  
  # Analyze root types
  if (length(roots) == 2) {
    r1 <- roots[1]
    r2 <- roots[2]
    
    # Check if roots are real
    if (abs(Im(r1)) < 1e-10 && abs(Im(r2)) < 1e-10) {
      r1 <- Re(r1)
      r2 <- Re(r2)
      
      if (abs(r1 - r2) > 1e-10) {
        cat("Case: Real distinct roots\n")
        cat("General solution: y = C1*exp(", r1, "*x) + C2*exp(", r2, "*x)\n")
        return(list(type = "real_distinct", r1 = r1, r2 = r2))
      } else {
        cat("Case: Repeated root\n")
        cat("General solution: y = (C1 + C2*x)*exp(", r1, "*x)\n")
        return(list(type = "repeated", r = r1))
      }
    } else {
      # Complex conjugate roots
      alpha <- Re(r1)
      beta <- abs(Im(r1))
      cat("Case: Complex conjugate roots\n")
      cat("α =", alpha, ", β =", beta, "\n")
      cat("General solution: y = exp(", alpha, "*x)*(C1*cos(", beta, "*x) + C2*sin(", beta, "*x))\n")
      return(list(type = "complex", alpha = alpha, beta = beta))
    }
  }
}

# Examples
cat("=== Example 1: Real Distinct Roots ===\n")
result1 <- solve_homogeneous_ode(1, -5, 6)

cat("\n=== Example 2: Complex Conjugate Roots ===\n")
result2 <- solve_homogeneous_ode(1, 4, 13)

cat("\n=== Example 3: Repeated Root ===\n")
result3 <- solve_homogeneous_ode(1, -6, 9)

# Visualization function
plot_solutions <- function() {
  x <- seq(0, 3, length.out = 1000)
  
  # Real distinct roots
  y1 <- exp(2*x) + exp(3*x)
  df1 <- data.frame(x = x, y = y1, type = "Real Distinct Roots\ny = e^(2x) + e^(3x)")
  
  # Complex conjugate roots
  y2 <- exp(-2*x) * cos(3*x)
  df2 <- data.frame(x = x, y = y2, type = "Complex Conjugate Roots\ny = e^(-2x)cos(3x)")
  
  # Repeated root
  y3 <- (1 + x) * exp(3*x)
  df3 <- data.frame(x = x, y = y3, type = "Repeated Root\ny = (1 + x)e^(3x)")
  
  # Extended range for damping comparison
  x_long <- seq(0, 5, length.out = 1000)
  y_under <- exp(-0.5*x_long) * cos(2*x_long)
  y_over <- exp(-x_long) + 0.5*exp(-2*x_long)
  y_crit <- (1 + x_long) * exp(-x_long)
  
  df4 <- data.frame(
    x = rep(x_long, 3),
    y = c(y_under, y_over, y_crit),
    Damping = rep(c("Underdamped", "Overdamped", "Critical"), each = length(x_long))
  )
  
  # Create plots
  p1 <- ggplot(df1, aes(x = x, y = y)) +
    geom_line(color = "blue", size = 1.2) +
    labs(title = "Real Distinct Roots", subtitle = expression(y == e^{2*x} + e^{3*x})) +
    theme_minimal() +
    theme(plot.title = element_text(face = "bold"))
  
  p2 <- ggplot(df2, aes(x = x, y = y)) +
    geom_line(color = "red", size = 1.2) +
    labs(title = "Complex Conjugate Roots", subtitle = expression(y == e^{-2*x}*cos(3*x))) +
    theme_minimal() +
    theme(plot.title = element_text(face = "bold"))
  
  p3 <- ggplot(df3, aes(x = x, y = y)) +
    geom_line(color = "green", size = 1.2) +
    labs(title = "Repeated Root", subtitle = expression(y == (1 + x)*e^{3*x})) +
    theme_minimal() +
    theme(plot.title = element_text(face = "bold"))
  
  p4 <- ggplot(df4, aes(x = x, y = y, color = Damping)) +
    geom_line(size = 1.2) +
    labs(title = "Damping Comparison", x = "t", y = "x(t)") +
    scale_color_manual(values = c("blue", "red", "green")) +
    theme_minimal() +
    theme(plot.title = element_text(face = "bold"))
  
  # Arrange plots
  grid.arrange(p1, p2, p3, p4, ncol = 2)
}

# Wronskian function
wronskian <- function(f1, f2, x) {
  # Numerical derivatives
  h <- 1e-8
  f1_prime <- (f1(x + h) - f1(x - h)) / (2 * h)
  f2_prime <- (f2(x + h) - f2(x - h)) / (2 * h)
  
  # Wronskian determinant
  W <- f1(x) * f2_prime - f1_prime * f2(x)
  return(W)
}

# Example: Wronskian for real distinct roots
f1 <- function(x) exp(2*x)
f2 <- function(x) exp(3*x)
x_test <- 1
W_value <- wronskian(f1, f2, x_test)
cat("\nWronskian of e^(2x) and e^(3x) at x =", x_test, ":", W_value, "\n")

# Spring-mass system simulation
simulate_spring_mass <- function(m = 1, k = 4, c = 0, x0 = 1, v0 = 0, t_max = 10) {
  # Calculate system parameters
  omega0 <- sqrt(k/m)
  gamma <- c / (2*m)
  
  t <- seq(0, t_max, length.out = 1000)
  
  if (c == 0) {
    # Undamped
    x <- x0 * cos(omega0 * t) + (v0/omega0) * sin(omega0 * t)
    title <- "Undamped Oscillation"
  } else {
    discriminant <- gamma^2 - omega0^2
    
    if (discriminant < 0) {
      # Underdamped
      omega_d <- sqrt(omega0^2 - gamma^2)
      A <- x0
      B <- (v0 + gamma*x0) / omega_d
      x <- exp(-gamma*t) * (A * cos(omega_d*t) + B * sin(omega_d*t))
      title <- "Underdamped Oscillation"
    } else if (discriminant > 0) {
      # Overdamped
      r1 <- -gamma + sqrt(discriminant)
      r2 <- -gamma - sqrt(discriminant)
      A <- (v0 - r2*x0) / (r1 - r2)
      B <- (r1*x0 - v0) / (r1 - r2)
      x <- A * exp(r1*t) + B * exp(r2*t)
      title <- "Overdamped Motion"
    } else {
      # Critically damped
      A <- x0
      B <- v0 + gamma*x0
      x <- (A + B*t) * exp(-gamma*t)
      title <- "Critically Damped Motion"
    }
  }
  
  df <- data.frame(t = t, x = x)
  
  ggplot(df, aes(x = t, y = x)) +
    geom_line(color = "blue", size = 1.2) +
    labs(title = title, x = "Time (s)", y = "Position (m)") +
    theme_minimal() +
    theme(plot.title = element_text(face = "bold", size = 14))
}

# Examples of different damping scenarios
p_undamped <- simulate_spring_mass(m = 1, k = 4, c = 0)
p_underdamped <- simulate_spring_mass(m = 1, k = 4, c = 1)
p_critical <- simulate_spring_mass(m = 1, k = 4, c = 4)
p_overdamped <- simulate_spring_mass(m = 1, k = 4, c = 6)

grid.arrange(p_undamped, p_underdamped, p_critical, p_overdamped, ncol = 2)
</code></pre>

                <div class="bg-blue-50 border-l-4 border-blue-500 p-4 rounded">
                    <h4 class="font-bold text-blue-800 mb-2">Key R Features:</h4>
                    <ul class="list-disc ml-6 text-blue-700">
                        <li><strong>polyroot()</strong>: Solves polynomial equations for characteristic roots</li>
                        <li><strong>ggplot2</strong>: Professional statistical graphics</li>
                        <li><strong>Spring-mass simulation</strong>: Complete physical system modeling</li>
                        <li><strong>Parameter studies</strong>: Exploring different damping scenarios</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Interactive Visualization -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-gray-300 pb-2">
                <i class="fas fa-chart-line mr-2 text-blue-600"></i>Interactive Solution Behavior
            </h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">Real Distinct Roots Behavior</h3>
                    <div class="chart-container">
                        <canvas id="realDistinctChart" width="400" height="300"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-bold mb-4">Complex Conjugate Roots (Oscillatory)</h3>
                    <div class="chart-container">
                        <canvas id="complexChart" width="400" height="300"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-bold mb-4">Repeated Root Behavior</h3>
                    <div class="chart-container">
                        <canvas id="repeatedChart" width="400" height="300"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-bold mb-4">Damping Comparison</h3>
                    <div class="chart-container">
                        <canvas id="dampingChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <!-- Higher-Order Extensions -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-gray-300 pb-2">
                <i class="fas fa-layer-group mr-2 text-blue-600"></i>Higher-Order Extensions
            </h2>

            <div class="prose max-w-none">
                <div class="theorem-box">
                    <h3 class="text-xl font-bold text-blue-800 mb-3">
                        <i class="fas fa-expand-arrows-alt mr-2"></i>nth-Order Linear Homogeneous Equations
                    </h3>
                    <p class="mb-4">For an nth-order equation:</p>
                    <div class="math-container text-center">
                        $$a_n y^{(n)} + a_{n-1} y^{(n-1)} + \cdots + a_1 y' + a_0 y = 0$$
                    </div>
                    <p class="mb-4">The characteristic equation is:</p>
                    <div class="math-container text-center">
                        $$a_n r^n + a_{n-1} r^{n-1} + \cdots + a_1 r + a_0 = 0$$
                    </div>
                    <p>This polynomial has $n$ roots (counting multiplicities), leading to $n$ linearly independent solutions.</p>
                </div>

                <div class="example-box">
                    <h3 class="text-xl font-bold text-green-800 mb-4">
                        <i class="fas fa-cube mr-2"></i>Third-Order Example
                    </h3>
                    <p>Consider: $y''' - 6y'' + 11y' - 6y = 0$</p>
                    <p class="mt-2">Characteristic equation: $r^3 - 6r^2 + 11r - 6 = 0$</p>
                    <p>Factoring: $(r-1)(r-2)(r-3) = 0$</p>
                    <p>Roots: $r_1 = 1$, $r_2 = 2$, $r_3 = 3$</p>
                    <p>General solution: $y = C_1 e^x + C_2 e^{2x} + C_3 e^{3x}$</p>
                </div>

                <div class="bg-gray-100 p-6 rounded-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">Root Multiplicity Rules</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-semibold text-gray-700 mb-2">Simple Root r:</h4>
                            <p class="text-sm">Contributes $Ce^{rx}$ to solution</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-700 mb-2">Root r of multiplicity k:</h4>
                            <p class="text-sm">Contributes $(C_1 + C_2x + \cdots + C_kx^{k-1})e^{rx}$</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-700 mb-2">Complex conjugate α ± iβ:</h4>
                            <p class="text-sm">Contributes $e^{\alpha x}(C_1\cos(\beta x) + C_2\sin(\beta x))$</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-700 mb-2">Repeated complex pairs:</h4>
                            <p class="text-sm">Similar pattern with polynomial multipliers</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Problem-Solving Strategy -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-gray-300 pb-2">
                <i class="fas fa-puzzle-piece mr-2 text-blue-600"></i>Problem-Solving Strategy
            </h2>

            <div class="bg-indigo-50 border-2 border-indigo-500 rounded-lg p-6">
                <h3 class="text-xl font-bold text-indigo-800 mb-4">
                    <i class="fas fa-list-ol mr-2"></i>Step-by-Step Solution Method
                </h3>
                <ol class="list-decimal ml-6 space-y-3 text-indigo-700">
                    <li><strong>Identify the equation type:</strong> Verify it's homogeneous linear with constant coefficients</li>
                    <li><strong>Write the characteristic equation:</strong> Replace $y^{(n)}$ with $r^n$, $y'$ with $r$, etc.</li>
                    <li><strong>Solve for the roots:</strong> Use factoring, quadratic formula, or numerical methods</li>
                    <li><strong>Classify the roots:</strong> Real distinct, complex conjugate, or repeated</li>
                    <li><strong>Write the general solution:</strong> Use the appropriate form for each root type</li>
                    <li><strong>Apply initial conditions:</strong> If given, solve for the arbitrary constants</li>
                    <li><strong>Verify solution:</strong> Substitute back into original equation</li>
                </ol>
            </div>

            <div class="mt-8 bg-yellow-50 border-2 border-yellow-500 rounded-lg p-6">
                <h3 class="text-xl font-bold text-yellow-800 mb-4">
                    <i class="fas fa-lightbulb mr-2"></i>Common Mistakes to Avoid
                </h3>
                <ul class="list-disc ml-6 space-y-2 text-yellow-700">
                    <li>Forgetting to check for repeated roots when discriminant is zero</li>
                    <li>Incorrectly handling complex roots (using exponential instead of trigonometric form)</li>
                    <li>Missing the polynomial multiplier for repeated roots</li>
                    <li>Sign errors in the characteristic equation</li>
                    <li>Not simplifying complex expressions to real form</li>
                </ul>
            </div>
        </section>

        <!-- Practice Problems -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-gray-300 pb-2">
                <i class="fas fa-dumbbell mr-2 text-blue-600"></i>Practice Problems
            </h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-white border-2 border-gray-300 rounded-lg p-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">Basic Problems</h3>
                    <ol class="list-decimal ml-6 space-y-2">
                        <li>$y'' - 3y' + 2y = 0$</li>
                        <li>$y'' + y' - 6y = 0$</li>
                        <li>$y'' + 4y' + 4y = 0$</li>
                        <li>$y'' + 2y' + 5y = 0$</li>
                        <li>$y'' - 4y = 0$</li>
                    </ol>
                </div>
                <div class="bg-white border-2 border-gray-300 rounded-lg p-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">Initial Value Problems</h3>
                    <ol class="list-decimal ml-6 space-y-2">
                        <li>$y'' - y' - 2y = 0$, $y(0) = 1$, $y'(0) = 0$</li>
                        <li>$y'' + 4y = 0$, $y(0) = 2$, $y'(0) = -1$</li>
                        <li>$y'' + 2y' + y = 0$, $y(0) = 1$, $y'(0) = 1$</li>
                        <li>$y'' - 6y' + 9y = 0$, $y(0) = 0$, $y'(0) = 2$</li>
                    </ol>
                </div>
                <div class="bg-white border-2 border-gray-300 rounded-lg p-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">Applications</h3>
                    <ol class="list-decimal ml-6 space-y-2">
                        <li>Spring-mass system: $m = 2$ kg, $k = 8$ N/m</li>
                        <li>RLC circuit: $L = 1$ H, $R = 4$ Ω, $C = 0.25$ F</li>
                        <li>Damped oscillator: $m = 1$, $c = 3$, $k = 2$</li>
                    </ol>
                </div>
                <div class="bg-white border-2 border-gray-300 rounded-lg p-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">Higher-Order</h3>
                    <ol class="list-decimal ml-6 space-y-2">
                        <li>$y''' - 6y'' + 11y' - 6y = 0$</li>
                        <li>$y^{(4)} - y = 0$</li>
                        <li>$y''' + 3y'' + 3y' + y = 0$</li>
                    </ol>
                </div>
            </div>
        </section>

        <!-- Summary -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-gray-300 pb-2">
                <i class="fas fa-clipboard-check mr-2 text-blue-600"></i>Chapter Summary
            </h2>

            <div class="bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-300 rounded-lg p-8">
                <h3 class="text-2xl font-bold text-blue-800 mb-6 text-center">Key Concepts Mastered</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                            <div>
                                <p class="font-semibold">Characteristic Equation Method</p>
                                <p class="text-sm text-gray-600">Transform ODE to algebraic equation</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                            <div>
                                <p class="font-semibold">Root Classification</p>
                                <p class="text-sm text-gray-600">Real, complex, repeated cases</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                            <div>
                                <p class="font-semibold">Solution Construction</p>
                                <p class="text-sm text-gray-600">Build general solutions systematically</p>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                            <div>
                                <p class="font-semibold">Linear Independence</p>
                                <p class="text-sm text-gray-600">Wronskian and fundamental sets</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                            <div>
                                <p class="font-semibold">Physical Applications</p>
                                <p class="text-sm text-gray-600">Springs, circuits, oscillations</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                            <div>
                                <p class="font-semibold">Computational Tools</p>
                                <p class="text-sm text-gray-600">Python and R implementations</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-8 bg-white p-4 rounded-lg border-l-4 border-blue-500">
                    <p class="text-lg font-semibold text-blue-800 mb-2">Looking Ahead:</p>
                    <p class="text-blue-700">
                        In the next chapter, we'll extend these methods to <strong>nonhomogeneous equations</strong>, 
                        where we'll learn techniques like the method of undetermined coefficients and variation 
                        of parameters to handle forcing functions and external inputs.
                    </p>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="text-center py-8 border-t-2 border-gray-300">
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-8 rounded-lg">
                <p class="text-lg font-semibold">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    Chapter 11 Complete: Homogeneous Linear Equations with Constant Coefficients
                </p>
                <p class="text-sm opacity-90 mt-2">
                    Next: Chapter 12 - Nonhomogeneous Equations (Method of Undetermined Coefficients & Variation of Parameters)
                </p>
            </div>
        </footer>
    </div>

    <script>
        // Initialize syntax highlighting
        document.addEventListener('DOMContentLoaded', function() {
            hljs.highlightAll();
        });
        
        // Chart.js configurations
        Chart.defaults.font.family = 'Arial, sans-serif';
        Chart.defaults.font.size = 12;

        // Real Distinct Roots Chart
        window.addEventListener('load', function() {
            const ctx1 = document.getElementById('realDistinctChart').getContext('2d');
            const xValues = [];
            const y1Values = [];
            const y2Values = [];
            
            for (let x = 0; x <= 2; x += 0.05) {
                xValues.push(x.toFixed(2));
                y1Values.push(Math.exp(2*x));
                y2Values.push(Math.exp(3*x));
            }

            new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: xValues,
                    datasets: [{
                        label: 'e^(2x)',
                        data: y1Values,
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1
                    }, {
                        label: 'e^(3x)',
                        data: y2Values,
                        borderColor: 'rgb(239, 68, 68)',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Real Distinct Roots: y₁ = e^(2x), y₂ = e^(3x)',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'x'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: 'y'
                            }
                        }
                    }
                }
            });

            // Complex Conjugate Roots Chart
            const ctx2 = document.getElementById('complexChart').getContext('2d');
            const xValues2 = [];
            const yOsc = [];
            
            for (let x = 0; x <= 3; x += 0.05) {
                xValues2.push(x.toFixed(2));
                yOsc.push(Math.exp(-2*x) * Math.cos(3*x));
            }

            new Chart(ctx2, {
                type: 'line',
                data: {
                    labels: xValues2,
                    datasets: [{
                        label: 'e^(-2x)cos(3x)',
                        data: yOsc,
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Complex Conjugate Roots: Damped Oscillation',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'x'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: 'y'
                            }
                        }
                    }
                }
            });

            // Repeated Root Chart
            const ctx3 = document.getElementById('repeatedChart').getContext('2d');
            const xValues3 = [];
            const yRepeated = [];
            
            for (let x = 0; x <= 2; x += 0.05) {
                xValues3.push(x.toFixed(2));
                yRepeated.push((1 + x) * Math.exp(x));
            }

            new Chart(ctx3, {
                type: 'line',
                data: {
                    labels: xValues3,
                    datasets: [{
                        label: '(1 + x)e^x',
                        data: yRepeated,
                        borderColor: 'rgb(147, 51, 234)',
                        backgroundColor: 'rgba(147, 51, 234, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Repeated Root: Polynomial × Exponential',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'x'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: 'y'
                            }
                        }
                    }
                }
            });

            // Damping Comparison Chart
            const ctx4 = document.getElementById('dampingChart').getContext('2d');
            const tValues = [];
            const underdamped = [];
            const overdamped = [];
            const critical = [];
            
            for (let t = 0; t <= 5; t += 0.1) {
                tValues.push(t.toFixed(1));
                underdamped.push(Math.exp(-0.5*t) * Math.cos(2*t));
                overdamped.push(Math.exp(-t) + 0.5*Math.exp(-2*t));
                critical.push((1 + t) * Math.exp(-t));
            }

            new Chart(ctx4, {
                type: 'line',
                data: {
                    labels: tValues,
                    datasets: [{
                        label: 'Underdamped',
                        data: underdamped,
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1
                    }, {
                        label: 'Overdamped',
                        data: overdamped,
                        borderColor: 'rgb(239, 68, 68)',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1
                    }, {
                        label: 'Critical',
                        data: critical,
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Damping Types in Mechanical Systems',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 't'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: 'x(t)'
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
    <script id="html_badge_script1">
        window.__genspark_remove_badge_link = "https://www.genspark.ai/api/html_badge/" +
            "remove_badge?token=To%2FBnjzloZ3UfQdcSaYfDhC7BK6kSb5rFqWH0Er2RDS6c%2BgeB0%2Bqn0DtosNPCl%2Bj6PNbfFHXR2HylFPxoD7Sr5uKQRSz9YExmPVWhA8tmzicQGzO%2FXLy23TIRkTMiFbGgrWhR5mEQClBz4ld8hrfL7sjZL0CMmPZDddys7DfSBv2p%2Fj7W31DpTVpf1upUaMY8QI8sFBUaUewZSbcitbkHrxTgyLhVRO5mWd9%2BHMBrzlJ3M2HuRBH1SMl6oNtwBYO%2BxHxp2bbJA5eujuZujKEIkMkByw5eB9SX2s7V1vt%2BKsg%2BeVKLd5Mx7SyHMONQ4Yxu0Wfrhxz9Kofj0JdUbgzXOQRbOJ81D%2FLT2iodei%2FXeQOxbRD0U91JHyxutlPgIhvqTanvB4ZQ9rux6ZlLAho984X6J56Zq4AcppvVpx95mJyKWCvAPNfpRbnFh%2FvL%2BKRVL8EZZucVtMYIAaOre79REfQEC1jud6IVKN8e5WNT%2BShMD7WoOXZu1Fsfbzs8D4DT2d0DYVn1DfOcJeK1czzVlSryhvRMqX%2BhUn0EAGAfrAHAxr3h4Jz1myUkqZzfCceA9NERDd8cOlu%2FDrAEOBihg%3D%3D";
        window.__genspark_locale = "en-US";
        window.__genspark_token = "To/BnjzloZ3UfQdcSaYfDhC7BK6kSb5rFqWH0Er2RDS6c+geB0+qn0DtosNPCl+j6PNbfFHXR2HylFPxoD7Sr5uKQRSz9YExmPVWhA8tmzicQGzO/XLy23TIRkTMiFbGgrWhR5mEQClBz4ld8hrfL7sjZL0CMmPZDddys7DfSBv2p/j7W31DpTVpf1upUaMY8QI8sFBUaUewZSbcitbkHrxTgyLhVRO5mWd9+HMBrzlJ3M2HuRBH1SMl6oNtwBYO+xHxp2bbJA5eujuZujKEIkMkByw5eB9SX2s7V1vt+Ksg+eVKLd5Mx7SyHMONQ4Yxu0Wfrhxz9Kofj0JdUbgzXOQRbOJ81D/LT2iodei/XeQOxbRD0U91JHyxutlPgIhvqTanvB4ZQ9rux6ZlLAho984X6J56Zq4AcppvVpx95mJyKWCvAPNfpRbnFh/vL+KRVL8EZZucVtMYIAaOre79REfQEC1jud6IVKN8e5WNT+ShMD7WoOXZu1Fsfbzs8D4DT2d0DYVn1DfOcJeK1czzVlSryhvRMqX+hUn0EAGAfrAHAxr3h4Jz1myUkqZzfCceA9NERDd8cOlu/DrAEOBihg==";
    </script>
    
    <script id="html_notice_dialog_script" src="https://www.genspark.ai/notice_dialog.js"></script>
    