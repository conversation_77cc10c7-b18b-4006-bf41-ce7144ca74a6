<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ordinary Differential Equations in Medical Applications: A Comprehensive Tutorial</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            }
        };
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
        }
        .math-container {
            font-family: 'JetBrains Mono', monospace;
        }
        .section-break {
            page-break-inside: avoid;
        }
        .chart-container {
            height: 400px;
            margin: 20px 0;
        }
        .no-break {
            break-inside: avoid;
        }
         {
            body {
                font-size: 12px;
            }
            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900">
    <div class="max-w-4xl mx-auto bg-white shadow-lg">
        <!-- Header -->
        <header class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8">
            <div class="text-center">
                <h1 class="text-4xl font-bold mb-4">
                    <i class="fas fa-heartbeat mr-3"></i>
                    Ordinary Differential Equations in Medical Applications
                </h1>
                <p class="text-xl opacity-90">A Comprehensive Tutorial</p>
                <div class="mt-6 text-sm opacity-80">
                    <p>Mathematical Modeling for Healthcare Professionals</p>
                    <p>Theory • Applications • Case Studies</p>
                </div>
            </div>
        </header>

        <!-- Table of Contents -->
        <section class="p-8 border-b">
            <h2 class="text-2xl font-bold mb-6 text-center">
                <i class="fas fa-list mr-2"></i>Table of Contents
            </h2>
            <div class="grid md:grid-cols-2 gap-4">
                <div class="space-y-2">
                    <div class="flex justify-between items-center p-2 hover:bg-gray-100 rounded">
                        <span>1. Introduction to ODEs in Medicine</span>
                        <span class="text-gray-500">Page 3</span>
                    </div>
                    <div class="flex justify-between items-center p-2 hover:bg-gray-100 rounded">
                        <span>2. Mathematical Foundations</span>
                        <span class="text-gray-500">Page 5</span>
                    </div>
                    <div class="flex justify-between items-center p-2 hover:bg-gray-100 rounded">
                        <span>3. First-Order ODEs</span>
                        <span class="text-gray-500">Page 8</span>
                    </div>
                    <div class="flex justify-between items-center p-2 hover:bg-gray-100 rounded">
                        <span>4. Higher-Order ODEs</span>
                        <span class="text-gray-500">Page 15</span>
                    </div>
                    <div class="flex justify-between items-center p-2 hover:bg-gray-100 rounded">
                        <span>5. Systems of ODEs</span>
                        <span class="text-gray-500">Page 22</span>
                    </div>
                    <div class="flex justify-between items-center p-2 hover:bg-gray-100 rounded">
                        <span>6. Numerical Methods</span>
                        <span class="text-gray-500">Page 28</span>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex justify-between items-center p-2 hover:bg-gray-100 rounded">
                        <span>7. Pharmacokinetic Models</span>
                        <span class="text-gray-500">Page 32</span>
                    </div>
                    <div class="flex justify-between items-center p-2 hover:bg-gray-100 rounded">
                        <span>8. Epidemiological Models</span>
                        <span class="text-gray-500">Page 38</span>
                    </div>
                    <div class="flex justify-between items-center p-2 hover:bg-gray-100 rounded">
                        <span>9. Physiological Systems</span>
                        <span class="text-gray-500">Page 42</span>
                    </div>
                    <div class="flex justify-between items-center p-2 hover:bg-gray-100 rounded">
                        <span>10. Case Studies</span>
                        <span class="text-gray-500">Page 46</span>
                    </div>
                    <div class="flex justify-between items-center p-2 hover:bg-gray-100 rounded">
                        <span>11. Advanced Topics</span>
                        <span class="text-gray-500">Page 48</span>
                    </div>
                    <div class="flex justify-between items-center p-2 hover:bg-gray-100 rounded">
                        <span>12. Conclusion & Resources</span>
                        <span class="text-gray-500">Page 50</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Chapter 1: Introduction -->
        <section class="p-8 section-break">
            <h2 class="text-3xl font-bold mb-6 text-blue-600">
                <i class="fas fa-play-circle mr-2"></i>Chapter 1: Introduction to ODEs in Medicine
            </h2>
            
            <div class="prose max-w-none">
                <h3 class="text-xl font-semibold mb-4">What are Ordinary Differential Equations?</h3>
                <p class="mb-4">
                    Ordinary Differential Equations (ODEs) are mathematical equations that describe how quantities change over time. In medical applications, they serve as powerful tools for modeling biological processes, drug dynamics, disease progression, and physiological systems.
                </p>
                
                <div class="bg-blue-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold mb-2">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>Key Definition
                    </h4>
                    <p>An ordinary differential equation is an equation involving an unknown function $y(t)$ and its derivatives with respect to a single independent variable $t$ (typically time).</p>
                    <p class="mt-2">General form: $$F(t, y, y', y'', \ldots, y^{(n)}) = 0$$</p>
                </div>

                <h3 class="text-xl font-semibold mb-4">Why ODEs Matter in Medicine</h3>
                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-green-700 mb-2">
                            <i class="fas fa-pills mr-2"></i>Drug Kinetics
                        </h4>
                        <p class="text-sm">Model how drugs are absorbed, distributed, metabolized, and eliminated from the body.</p>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-red-700 mb-2">
                            <i class="fas fa-virus mr-2"></i>Disease Spread
                        </h4>
                        <p class="text-sm">Predict and control the spread of infectious diseases through populations.</p>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-purple-700 mb-2">
                            <i class="fas fa-heart mr-2"></i>Physiological Processes
                        </h4>
                        <p class="text-sm">Describe cardiovascular dynamics, neural signaling, and metabolic pathways.</p>
                    </div>
                    <div class="bg-orange-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-orange-700 mb-2">
                            <i class="fas fa-chart-line mr-2"></i>Population Dynamics
                        </h4>
                        <p class="text-sm">Analyze birth rates, death rates, and population growth in medical contexts.</p>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Historical Context</h3>
                <p class="mb-4">
                    The application of differential equations in medicine dates back to the 18th century when Daniel Bernoulli used mathematical models to study smallpox vaccination. Today, ODEs are fundamental to:
                </p>
                <ul class="list-disc pl-6 mb-6">
                    <li>Clinical trial design and drug development</li>
                    <li>Public health policy and epidemic control</li>
                    <li>Medical device engineering</li>
                    <li>Personalized medicine and treatment optimization</li>
                    <li>Biomedical research and systems biology</li>
                </ul>

                <div class="bg-gray-50 p-6 rounded-lg">
                    <h4 class="font-semibold mb-2">
                        <i class="fas fa-quote-left text-gray-400 mr-2"></i>Learning Objectives
                    </h4>
                    <p class="text-sm">By the end of this tutorial, you will be able to:</p>
                    <ul class="list-disc pl-6 text-sm mt-2">
                        <li>Understand the mathematical principles behind ODEs</li>
                        <li>Solve various types of differential equations</li>
                        <li>Apply ODEs to model medical and biological systems</li>
                        <li>Use numerical methods for complex problems</li>
                        <li>Interpret results in clinical contexts</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Chapter 2: Mathematical Foundations -->
        <section class="p-8 section-break border-t">
            <h2 class="text-3xl font-bold mb-6 text-blue-600">
                <i class="fas fa-square-root-alt mr-2"></i>Chapter 2: Mathematical Foundations
            </h2>

            <div class="prose max-w-none">
                <h3 class="text-xl font-semibold mb-4">Basic Terminology</h3>
                
                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-700 mb-2">Order of an ODE</h4>
                            <p class="text-sm">The highest derivative that appears in the equation.</p>
                            <p class="text-xs mt-2 font-mono">Example: $y'' + 3y' + 2y = 0$ is 2nd order</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-700 mb-2">Degree</h4>
                            <p class="text-sm">The power of the highest-order derivative when the equation is polynomial in derivatives.</p>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-purple-700 mb-2">Linear vs Nonlinear</h4>
                            <p class="text-sm">Linear: coefficients depend only on the independent variable.</p>
                            <p class="text-xs mt-2 font-mono">Linear: $y' + ty = e^t$</p>
                            <p class="text-xs font-mono">Nonlinear: $y' + y^2 = t$</p>
                        </div>
                        <div class="bg-red-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-red-700 mb-2">Homogeneous vs Nonhomogeneous</h4>
                            <p class="text-sm">Homogeneous: no terms without the unknown function or its derivatives.</p>
                        </div>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Classification System</h3>
                <div class="overflow-x-auto mb-6">
                    <table class="min-w-full bg-white border border-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 border-b text-left">Type</th>
                                <th class="px-4 py-2 border-b text-left">General Form</th>
                                <th class="px-4 py-2 border-b text-left">Medical Example</th>
                                <th class="px-4 py-2 border-b text-left">Application</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b font-semibold">1st Order Linear</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$y' + p(t)y = q(t)$</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$\frac{dC}{dt} = -kC$</td>
                                <td class="px-4 py-2 border-b text-sm">Drug elimination</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b font-semibold">1st Order Separable</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$\frac{dy}{dt} = f(t)g(y)$</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$\frac{dN}{dt} = rN$</td>
                                <td class="px-4 py-2 border-b text-sm">Population growth</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b font-semibold">2nd Order Linear</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$y'' + p(t)y' + q(t)y = r(t)$</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$m\ddot{x} + c\dot{x} + kx = F(t)$</td>
                                <td class="px-4 py-2 border-b text-sm">Biomechanics</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b font-semibold">System</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$\mathbf{y}' = \mathbf{f}(t, \mathbf{y})$</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$\frac{dS}{dt} = -\beta SI$</td>
                                <td class="px-4 py-2 border-b text-sm">Epidemiology</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3 class="text-xl font-semibold mb-4">Solution Concepts</h3>
                <div class="mb-6">
                    <div class="bg-yellow-50 p-6 rounded-lg mb-4">
                        <h4 class="font-semibold mb-2">
                            <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>General vs Particular Solutions
                        </h4>
                        <p class="mb-2"><strong>General Solution:</strong> Contains arbitrary constants and represents all possible solutions.</p>
                        <p class="mb-2 font-mono text-sm">Example: $y = Ce^{-kt}$ (where $C$ is arbitrary)</p>
                        <p class="mb-2"><strong>Particular Solution:</strong> Obtained by specifying initial conditions.</p>
                        <p class="font-mono text-sm">Example: $y = 100e^{-0.1t}$ (with initial condition $y(0) = 100$)</p>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Initial Value Problems (IVP)</h3>
                <p class="mb-4">
                    In medical applications, we typically know the initial state of a system and want to predict its future behavior. This leads to Initial Value Problems:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">
                        $$\begin{cases}
                        \frac{dy}{dt} = f(t, y) \\
                        y(t_0) = y_0
                        \end{cases}$$
                    </p>
                </div>

                <h4 class="text-lg font-semibold mb-3">Medical Example: Drug Concentration</h4>
                <p class="mb-4">
                    Consider a patient receiving an intravenous drug. The concentration $C(t)$ follows:
                </p>
                <div class="bg-blue-50 p-4 rounded-lg mb-4 math-container">
                    <p class="text-center">
                        $$\begin{cases}
                        \frac{dC}{dt} = -kC + R(t) \\
                        C(0) = C_0
                        \end{cases}$$
                    </p>
                </div>
                <p class="mb-6">
                    Where $k$ is the elimination rate constant, $R(t)$ is the infusion rate, and $C_0$ is the initial concentration.
                </p>

                <h3 class="text-xl font-semibold mb-4">Existence and Uniqueness</h3>
                <div class="bg-red-50 p-6 rounded-lg">
                    <h4 class="font-semibold mb-2">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>Picard-Lindelöf Theorem
                    </h4>
                    <p class="mb-2">For the IVP $y' = f(t,y)$, $y(t_0) = y_0$:</p>
                    <p class="text-sm">If $f$ and $\frac{\partial f}{\partial y}$ are continuous in a rectangle containing $(t_0, y_0)$, then there exists a unique solution in some interval around $t_0$.</p>
                    <p class="text-sm mt-2"><strong>Medical Significance:</strong> Ensures that physiological models have well-defined, predictable behavior.</p>
                </div>
            </div>
        </section>

        <!-- Chapter 3: First-Order ODEs -->
        <section class="p-8 section-break border-t">
            <h2 class="text-3xl font-bold mb-6 text-blue-600">
                <i class="fas fa-layer-group mr-2"></i>Chapter 3: First-Order ODEs
            </h2>

            <div class="prose max-w-none">
                <h3 class="text-xl font-semibold mb-4">Separable Equations</h3>
                <p class="mb-4">
                    The most fundamental type of ODE that can be written as:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">$$\frac{dy}{dt} = f(t)g(y)$$</p>
                </div>

                <h4 class="text-lg font-semibold mb-3">Solution Method</h4>
                <ol class="list-decimal pl-6 mb-6">
                    <li class="mb-2">Separate variables: $\frac{dy}{g(y)} = f(t)dt$</li>
                    <li class="mb-2">Integrate both sides: $\int \frac{dy}{g(y)} = \int f(t)dt$</li>
                    <li class="mb-2">Solve for $y$ if possible</li>
                    <li>Apply initial conditions</li>
                </ol>

                <div class="bg-green-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-green-700 mb-3">
                        <i class="fas fa-flask mr-2"></i>Medical Example: Exponential Decay
                    </h4>
                    <p class="mb-2"><strong>Problem:</strong> Model drug elimination from blood plasma</p>
                    <p class="mb-2">The rate of elimination is proportional to the current concentration:</p>
                    <div class="math-container mb-2">
                        $$\frac{dC}{dt} = -kC, \quad C(0) = C_0$$
                    </div>
                    <p class="mb-2"><strong>Solution:</strong></p>
                    <div class="math-container mb-2">
                        $$\frac{dC}{C} = -k dt$$
                        $$\ln C = -kt + \text{constant}$$
                        $$C(t) = C_0 e^{-kt}$$
                    </div>
                    <p class="text-sm"><strong>Medical Interpretation:</strong> The drug concentration decreases exponentially with half-life $t_{1/2} = \frac{\ln 2}{k}$</p>
                </div>

                <!-- Drug Elimination Visualization -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-3">Drug Elimination Curves</h4>
                    <div class="chart-container bg-white border rounded-lg p-4">
                        <canvas id="drugEliminationChart"></canvas>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Linear First-Order ODEs</h3>
                <p class="mb-4">
                    Equations of the form:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">$$\frac{dy}{dt} + p(t)y = q(t)$$</p>
                </div>

                <h4 class="text-lg font-semibold mb-3">Integrating Factor Method</h4>
                <ol class="list-decimal pl-6 mb-6">
                    <li class="mb-2">Calculate integrating factor: $\mu(t) = e^{\int p(t)dt}$</li>
                    <li class="mb-2">Multiply equation by $\mu(t)$</li>
                    <li class="mb-2">Recognize left side as $\frac{d}{dt}[\mu(t)y]$</li>
                    <li class="mb-2">Integrate and solve for $y$</li>
                </ol>

                <div class="bg-blue-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-blue-700 mb-3">
                        <i class="fas fa-syringe mr-2"></i>Medical Example: IV Infusion with Elimination
                    </h4>
                    <p class="mb-2"><strong>Problem:</strong> Patient receives constant IV infusion rate $R$ while drug is eliminated at rate $kC$</p>
                    <div class="math-container mb-2">
                        $$\frac{dC}{dt} = R - kC, \quad C(0) = 0$$
                    </div>
                    <p class="mb-2">Rearranging: $\frac{dC}{dt} + kC = R$</p>
                    <p class="mb-2">Integrating factor: $\mu(t) = e^{kt}$</p>
                    <p class="mb-2"><strong>Solution:</strong></p>
                    <div class="math-container mb-2">
                        $$C(t) = \frac{R}{k}(1 - e^{-kt})$$
                    </div>
                    <p class="text-sm"><strong>Medical Interpretation:</strong> Concentration approaches steady state $C_{ss} = R/k$ exponentially</p>
                </div>

                <!-- IV Infusion Visualization -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-3">IV Infusion to Steady State</h4>
                    <div class="chart-container bg-white border rounded-lg p-4">
                        <canvas id="ivInfusionChart"></canvas>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Logistic Growth Model</h3>
                <p class="mb-4">
                    One of the most important nonlinear ODEs in biology and medicine:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">$$\frac{dP}{dt} = rP\left(1 - \frac{P}{K}\right)$$</p>
                </div>
                <p class="mb-4">
                    Where $P$ is population, $r$ is growth rate, and $K$ is carrying capacity.
                </p>

                <div class="bg-purple-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-purple-700 mb-3">
                        <i class="fas fa-virus mr-2"></i>Medical Example: Tumor Growth
                    </h4>
                    <p class="mb-2"><strong>Problem:</strong> Model the growth of cancer cells with limited resources</p>
                    <div class="math-container mb-2">
                        $$\frac{dN}{dt} = rN\left(1 - \frac{N}{K}\right), \quad N(0) = N_0$$
                    </div>
                    <p class="mb-2"><strong>Solution:</strong></p>
                    <div class="math-container mb-2">
                        $$N(t) = \frac{K}{1 + \left(\frac{K}{N_0} - 1\right)e^{-rt}}$$
                    </div>
                    <p class="text-sm"><strong>Medical Interpretation:</strong> Tumor growth slows as it approaches the carrying capacity due to resource limitations</p>
                </div>

                <!-- Logistic Growth Visualization -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-3">Logistic Growth vs Exponential Growth</h4>
                    <div class="chart-container bg-white border rounded-lg p-4">
                        <canvas id="logisticGrowthChart"></canvas>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Bernoulli Equations</h3>
                <p class="mb-4">
                    Nonlinear equations of the form:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">$$\frac{dy}{dt} + p(t)y = q(t)y^n$$</p>
                </div>

                <h4 class="text-lg font-semibold mb-3">Solution Strategy</h4>
                <ol class="list-decimal pl-6 mb-6">
                    <li class="mb-2">Divide by $y^n$: $y^{-n}\frac{dy}{dt} + p(t)y^{1-n} = q(t)$</li>
                    <li class="mb-2">Substitute $v = y^{1-n}$, so $\frac{dv}{dt} = (1-n)y^{-n}\frac{dy}{dt}$</li>
                    <li class="mb-2">Transform to linear ODE in $v$</li>
                    <li>Solve and substitute back</li>
                </ol>

                <div class="bg-orange-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-orange-700 mb-3">
                        <i class="fas fa-heartbeat mr-2"></i>Medical Example: Allometric Scaling
                    </h4>
                    <p class="mb-2"><strong>Problem:</strong> Metabolic rate $M$ scales with body mass $W$ as:</p>
                    <div class="math-container mb-2">
                        $$\frac{dM}{dt} + aM = bW^{3/4}$$
                    </div>
                    <p class="text-sm"><strong>Application:</strong> Drug dosing based on body weight in pediatric medicine</p>
                </div>

                <h3 class="text-xl font-semibold mb-4">Qualitative Analysis</h3>
                <p class="mb-4">
                    Understanding behavior without explicit solutions:
                </p>

                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">
                            <i class="fas fa-arrows-alt-h mr-2"></i>Phase Line Analysis
                        </h4>
                        <ul class="text-sm list-disc pl-4">
                            <li>Find equilibrium points: $f(y) = 0$</li>
                            <li>Determine stability from sign of $f'(y)$</li>
                            <li>Sketch solution behavior</li>
                        </ul>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">
                            <i class="fas fa-chart-line mr-2"></i>Direction Fields
                        </h4>
                        <ul class="text-sm list-disc pl-4">
                            <li>Plot slope $\frac{dy}{dt}$ at grid points</li>
                            <li>Visualize solution curves</li>
                            <li>Identify basins of attraction</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-red-50 p-6 rounded-lg">
                    <h4 class="font-semibold text-red-700 mb-3">
                        <i class="fas fa-exclamation-circle mr-2"></i>Clinical Significance of Stability
                    </h4>
                    <p class="text-sm mb-2"><strong>Stable Equilibrium:</strong> System returns to baseline after perturbation (e.g., homeostasis)</p>
                    <p class="text-sm mb-2"><strong>Unstable Equilibrium:</strong> Small changes lead to dramatic effects (e.g., disease outbreaks)</p>
                    <p class="text-sm"><strong>Saddle Point:</strong> Stable in some directions, unstable in others (e.g., critical treatment thresholds)</p>
                </div>
            </div>
        </section>

        <!-- Chapter 4: Higher-Order ODEs -->
        <section class="p-8 section-break border-t">
            <h2 class="text-3xl font-bold mb-6 text-blue-600">
                <i class="fas fa-layer-group mr-2"></i>Chapter 4: Higher-Order ODEs
            </h2>

            <div class="prose max-w-none">
                <h3 class="text-xl font-semibold mb-4">Second-Order Linear ODEs</h3>
                <p class="mb-4">
                    The general form of a second-order linear ODE is:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">$$a(t)y'' + b(t)y' + c(t)y = f(t)$$</p>
                </div>

                <h4 class="text-lg font-semibold mb-3">Homogeneous Case with Constant Coefficients</h4>
                <p class="mb-4">
                    For $ay'' + by' + cy = 0$, we use the characteristic equation method:
                </p>
                <div class="bg-blue-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">$$ar^2 + br + c = 0$$</p>
                </div>

                <div class="overflow-x-auto mb-6">
                    <table class="min-w-full bg-white border border-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 border-b text-left">Case</th>
                                <th class="px-4 py-2 border-b text-left">Discriminant</th>
                                <th class="px-4 py-2 border-b text-left">Roots</th>
                                <th class="px-4 py-2 border-b text-left">General Solution</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b">Distinct Real</td>
                                <td class="px-4 py-2 border-b">$b^2 - 4ac > 0$</td>
                                <td class="px-4 py-2 border-b">$r_1, r_2$ real</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$y = c_1e^{r_1t} + c_2e^{r_2t}$</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b">Repeated Real</td>
                                <td class="px-4 py-2 border-b">$b^2 - 4ac = 0$</td>
                                <td class="px-4 py-2 border-b">$r$ (double)</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$y = (c_1 + c_2t)e^{rt}$</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b">Complex</td>
                                <td class="px-4 py-2 border-b">$b^2 - 4ac < 0$</td>
                                <td class="px-4 py-2 border-b">$\alpha \pm \beta i$</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$y = e^{\alpha t}(c_1\cos(\beta t) + c_2\sin(\beta t))$</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="bg-green-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-green-700 mb-3">
                        <i class="fas fa-lungs mr-2"></i>Medical Example: Respiratory Mechanics
                    </h4>
                    <p class="mb-2"><strong>Problem:</strong> Model chest wall displacement during breathing</p>
                    <p class="mb-2">The equation of motion for the respiratory system:</p>
                    <div class="math-container mb-2">
                        $$I\frac{d^2V}{dt^2} + R\frac{dV}{dt} + \frac{V}{C} = P(t)$$
                    </div>
                    <p class="mb-2">Where $I$ = inertance, $R$ = resistance, $C$ = compliance, $V$ = volume, $P(t)$ = driving pressure</p>
                    <p class="text-sm"><strong>Medical Interpretation:</strong> This models the mechanical properties of the lungs and chest wall</p>
                </div>

                <h3 class="text-xl font-semibold mb-4">Oscillatory Systems</h3>
                <p class="mb-4">
                    Many physiological systems exhibit oscillatory behavior. Consider the undamped harmonic oscillator:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">$$\frac{d^2x}{dt^2} + \omega^2 x = 0$$</p>
                </div>

                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-purple-700 mb-2">
                            <i class="fas fa-heartbeat mr-2"></i>Cardiac Oscillations
                        </h4>
                        <p class="text-sm mb-2">Heart rate variability can be modeled as:</p>
                        <div class="math-container text-xs">
                            $$\frac{d^2H}{dt^2} + 2\zeta\omega_0\frac{dH}{dt} + \omega_0^2 H = F(t)$$
                        </div>
                        <p class="text-xs mt-2">Where $H$ is heart rate deviation from baseline</p>
                    </div>
                    <div class="bg-orange-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-orange-700 mb-2">
                            <i class="fas fa-brain mr-2"></i>Neural Oscillations
                        </h4>
                        <p class="text-sm mb-2">Brain waves can be modeled as coupled oscillators:</p>
                        <div class="math-container text-xs">
                            $$\frac{d^2\phi}{dt^2} + \gamma\frac{d\phi}{dt} + \omega^2\sin(\phi) = I_{ext}$$
                        </div>
                        <p class="text-xs mt-2">Where $\phi$ is the phase of neural oscillation</p>
                    </div>
                </div>

                <!-- Oscillatory Systems Visualization -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-3">Damped Harmonic Oscillator</h4>
                    <div class="chart-container bg-white border rounded-lg p-4">
                        <canvas id="oscillatorChart"></canvas>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Method of Undetermined Coefficients</h3>
                <p class="mb-4">
                    For nonhomogeneous equations $ay'' + by' + cy = f(t)$ where $f(t)$ has specific forms:
                </p>

                <div class="overflow-x-auto mb-6">
                    <table class="min-w-full bg-white border border-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 border-b text-left">$f(t)$ Form</th>
                                <th class="px-4 py-2 border-b text-left">Trial Solution $y_p$</th>
                                <th class="px-4 py-2 border-b text-left">Medical Context</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b font-mono text-sm">$Ae^{rt}$</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$Be^{rt}$</td>
                                <td class="px-4 py-2 border-b text-sm">Exponential drug input</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b font-mono text-sm">$A\cos(\omega t)$</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$B\cos(\omega t) + C\sin(\omega t)$</td>
                                <td class="px-4 py-2 border-b text-sm">Periodic stimulation</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b font-mono text-sm">$At^n$</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$B_nt^n + \ldots + B_1t + B_0$</td>
                                <td class="px-4 py-2 border-b text-sm">Time-dependent dosing</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="bg-blue-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-blue-700 mb-3">
                        <i class="fas fa-wave-square mr-2"></i>Medical Example: Forced Oscillations in Drug Delivery</h4>
                    <p class="mb-2"><strong>Problem:</strong> Periodic drug administration with body's natural clearance</p>
                    <div class="math-container mb-2">
                        $$\frac{d^2C}{dt^2} + 2\alpha\frac{dC}{dt} + \omega_0^2 C = A\cos(\omega t)$$
                    </div>
                    <p class="mb-2">Where $A\cos(\omega t)$ represents periodic dosing</p>
                    <p class="mb-2"><strong>Particular solution:</strong></p>
                    <div class="math-container mb-2">
                        $$C_p(t) = \frac{A}{(\omega_0^2 - \omega^2) + (2\alpha\omega)^2} \cos(\omega t - \phi)$$
                    </div>
                    <p class="text-sm"><strong>Medical Interpretation:</strong> Resonance occurs when dosing frequency matches natural frequency, potentially causing dangerous drug accumulation</p>
                </div>

                <h3 class="text-xl font-semibold mb-4">Variation of Parameters</h3>
                <p class="mb-4">
                    A general method for nonhomogeneous linear ODEs when $f(t)$ is arbitrary:
                </p>

                <ol class="list-decimal pl-6 mb-6">
                    <li class="mb-2">Find homogeneous solution: $y_h = c_1y_1 + c_2y_2$</li>
                    <li class="mb-2">Replace constants with functions: $y_p = u_1(t)y_1 + u_2(t)y_2$</li>
                    <li class="mb-2">Solve the system:
                        <div class="math-container ml-4 mt-2">
                            $$\begin{cases}
                            u_1'y_1 + u_2'y_2 = 0 \\
                            u_1'y_1' + u_2'y_2' = f(t)
                            \end{cases}$$
                        </div>
                    </li>
                    <li>Integrate to find $u_1$ and $u_2$</li>
                </ol>

                <div class="bg-yellow-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-yellow-700 mb-3">
                        <i class="fas fa-exclamation-triangle mr-2"></i>Medical Example: Variable Drug Input
                    </h4>
                    <p class="mb-2"><strong>Problem:</strong> Patient receives variable infusion rate $R(t)$ based on real-time monitoring</p>
                    <div class="math-container mb-2">
                        $$\frac{d^2C}{dt^2} + a\frac{dC}{dt} + bC = R(t)$$
                    </div>
                    <p class="text-sm"><strong>Application:</strong> Closed-loop drug delivery systems that adjust dosing based on patient response</p>
                </div>

                <h3 class="text-xl font-semibold mb-4">Higher-Order Applications in Biomechanics</h3>
                
                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">
                            <i class="fas fa-running mr-2"></i>Gait Analysis
                        </h4>
                        <p class="text-sm mb-2">Fourth-order equation for leg motion:</p>
                        <div class="math-container text-xs mb-2">
                            $$m\frac{d^4x}{dt^4} + c\frac{d^3x}{dt^3} + k\frac{d^2x}{dt^2} = F(t)$$
                        </div>
                        <p class="text-xs">Models muscle forces and joint dynamics</p>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">
                            <i class="fas fa-bone mr-2"></i>Bone Vibration
                        </h4>
                        <p class="text-sm mb-2">Fourth-order beam equation:</p>
                        <div class="math-container text-xs mb-2">
                            $$EI\frac{d^4y}{dx^4} + \rho A\frac{d^2y}{dt^2} = 0$$
                        </div>
                        <p class="text-xs">Models bone vibrations for fracture healing</p>
                    </div>
                </div>

                <div class="bg-red-50 p-6 rounded-lg">
                    <h4 class="font-semibold text-red-700 mb-3">
                        <i class="fas fa-user-md mr-2"></i>Clinical Applications Summary
                    </h4>
                    <ul class="text-sm list-disc pl-4">
                        <li><strong>Pharmacokinetics:</strong> Multi-compartment drug distribution models</li>
                        <li><strong>Cardiology:</strong> Heart rhythm disorders and pacemaker dynamics</li>
                        <li><strong>Neurology:</strong> Action potential propagation and neural networks</li>
                        <li><strong>Orthopedics:</strong> Bone healing and prosthetic device design</li>
                        <li><strong>Respiratory Medicine:</strong> Ventilator settings and lung mechanics</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Chapter 5: Systems of ODEs -->
        <section class="p-8 section-break border-t">
            <h2 class="text-3xl font-bold mb-6 text-blue-600">
                <i class="fas fa-project-diagram mr-2"></i>Chapter 5: Systems of ODEs
            </h2>

            <div class="prose max-w-none">
                <h3 class="text-xl font-semibold mb-4">Introduction to Systems</h3>
                <p class="mb-4">
                    Many medical phenomena involve multiple interacting variables that change simultaneously. These require systems of ODEs:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">
                        $$\begin{cases}
                        \frac{dx}{dt} = f(t, x, y) \\
                        \frac{dy}{dt} = g(t, x, y)
                        \end{cases}$$
                    </p>
                </div>

                <div class="bg-blue-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-blue-700 mb-3">
                        <i class="fas fa-virus mr-2"></i>Classic Example: SIR Epidemiological Model
                    </h4>
                    <p class="mb-2"><strong>Problem:</strong> Model the spread of an infectious disease through a population</p>
                    <div class="math-container mb-2">
                        $$\begin{cases}
                        \frac{dS}{dt} = -\beta SI \\
                        \frac{dI}{dt} = \beta SI - \gamma I \\
                        \frac{dR}{dt} = \gamma I
                        \end{cases}$$
                    </div>
                    <p class="mb-2">Where $S$ = Susceptible, $I$ = Infected, $R$ = Recovered</p>
                    <p class="text-sm"><strong>Parameters:</strong> $\beta$ = infection rate, $\gamma$ = recovery rate</p>
                </div>

                <!-- SIR Model Visualization -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-3">SIR Model Dynamics</h4>
                    <div class="chart-container bg-white border rounded-lg p-4">
                        <canvas id="sirModelChart"></canvas>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Linear Systems with Constant Coefficients</h3>
                <p class="mb-4">
                    For systems of the form $\mathbf{x}' = A\mathbf{x}$, where $A$ is a constant matrix:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">
                        $$\begin{pmatrix} x' \\ y' \end{pmatrix} = \begin{pmatrix} a & b \\ c & d \end{pmatrix} \begin{pmatrix} x \\ y \end{pmatrix}$$
                    </p>
                </div>

                <h4 class="text-lg font-semibold mb-3">Eigenvalue Method</h4>
                <ol class="list-decimal pl-6 mb-6">
                    <li class="mb-2">Find eigenvalues: $\det(A - \lambda I) = 0$</li>
                    <li class="mb-2">Find corresponding eigenvectors</li>
                    <li class="mb-2">Construct general solution based on eigenvalue types</li>
                </ol>

                <div class="overflow-x-auto mb-6">
                    <table class="min-w-full bg-white border border-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 border-b text-left">Eigenvalue Type</th>
                                <th class="px-4 py-2 border-b text-left">Solution Form</th>
                                <th class="px-4 py-2 border-b text-left">Phase Portrait</th>
                                <th class="px-4 py-2 border-b text-left">Medical Interpretation</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b">Real, distinct, same sign</td>
                                <td class="px-4 py-2 border-b font-mono text-xs">$c_1e^{\lambda_1 t}\mathbf{v_1} + c_2e^{\lambda_2 t}\mathbf{v_2}$</td>
                                <td class="px-4 py-2 border-b text-sm">Node</td>
                                <td class="px-4 py-2 border-b text-sm">Stable/unstable equilibrium</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b">Real, opposite signs</td>
                                <td class="px-4 py-2 border-b font-mono text-xs">$c_1e^{\lambda_1 t}\mathbf{v_1} + c_2e^{\lambda_2 t}\mathbf{v_2}$</td>
                                <td class="px-4 py-2 border-b text-sm">Saddle</td>
                                <td class="px-4 py-2 border-b text-sm">Critical transitions</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b">Complex: $\alpha \pm \beta i$</td>
                                <td class="px-4 py-2 border-b font-mono text-xs">$e^{\alpha t}[c_1\cos(\beta t) + c_2\sin(\beta t)]$</td>
                                <td class="px-4 py-2 border-b text-sm">Spiral</td>
                                <td class="px-4 py-2 border-b text-sm">Oscillatory dynamics</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="bg-green-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-green-700 mb-3">
                        <i class="fas fa-exchange-alt mr-2"></i>Medical Example: Two-Compartment Pharmacokinetic Model
                    </h4>
                    <p class="mb-2"><strong>Problem:</strong> Drug distributes between plasma and tissue compartments</p>
                    <div class="math-container mb-2">
                        $$\begin{cases}
                        \frac{dC_1}{dt} = -k_{12}C_1 + k_{21}C_2 - k_{10}C_1 \\
                        \frac{dC_2}{dt} = k_{12}C_1 - k_{21}C_2
                        \end{cases}$$
                    </div>
                    <p class="mb-2">Where $C_1$ = plasma concentration, $C_2$ = tissue concentration</p>
                    <p class="text-sm"><strong>Medical Interpretation:</strong> Eigenvalues determine the rates of drug distribution and elimination phases</p>
                </div>

                <!-- Two-Compartment Model Visualization -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-3">Two-Compartment Pharmacokinetic Model</h4>
                    <div class="chart-container bg-white border rounded-lg p-4">
                        <canvas id="twoCompartmentChart"></canvas>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Predator-Prey Models in Medicine</h3>
                <p class="mb-4">
                    The Lotka-Volterra equations model competing populations:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">
                        $$\begin{cases}
                        \frac{dx}{dt} = ax - bxy \\
                        \frac{dy}{dt} = -cy + dxy
                        \end{cases}$$
                    </p>
                </div>

                <div class="bg-purple-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-purple-700 mb-3">
                        <i class="fas fa-microscope mr-2"></i>Medical Example: Cancer-Immune System Dynamics
                    </h4>
                    <p class="mb-2"><strong>Problem:</strong> Model interaction between cancer cells and immune cells</p>
                    <div class="math-container mb-2">
                        $$\begin{cases}
                        \frac{dC}{dt} = rC - \alpha CI \\
                        \frac{dI}{dt} = \beta CI - \delta I + s
                        \end{cases}$$
                    </div>
                    <p class="mb-2">Where $C$ = cancer cell density, $I$ = immune cell density</p>
                    <p class="text-sm"><strong>Medical Interpretation:</strong> Phase portraits show conditions for tumor control vs. growth</p>
                </div>

                <h3 class="text-xl font-semibold mb-4">Stability Analysis</h3>
                <p class="mb-4">
                    For nonlinear systems, we analyze equilibrium points and their stability:
                </p>

                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">
                            <i class="fas fa-search mr-2"></i>Finding Equilibria
                        </h4>
                        <ol class="text-sm list-decimal pl-4">
                            <li>Set $\frac{dx}{dt} = 0$ and $\frac{dy}{dt} = 0$</li>
                            <li>Solve the system algebraically</li>
                            <li>Identify all equilibrium points</li>
                        </ol>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">
                            <i class="fas fa-balance-scale mr-2"></i>Linearization
                        </h4>
                        <ol class="text-sm list-decimal pl-4">
                            <li>Compute Jacobian matrix $J$</li>
                            <li>Evaluate at equilibrium point</li>
                            <li>Analyze eigenvalues for stability</li>
                        </ol>
                    </div>
                </div>

                <div class="bg-yellow-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-yellow-700 mb-3">
                        <i class="fas fa-exclamation-triangle mr-2"></i>Stability Criteria
                    </h4>
                    <ul class="text-sm list-disc pl-4">
                        <li><strong>Stable Node:</strong> All eigenvalues have negative real parts</li>
                        <li><strong>Unstable Node:</strong> All eigenvalues have positive real parts</li>
                        <li><strong>Saddle Point:</strong> Eigenvalues have opposite signs</li>
                        <li><strong>Stable Spiral:</strong> Complex eigenvalues with negative real parts</li>
                        <li><strong>Unstable Spiral:</strong> Complex eigenvalues with positive real parts</li>
                        <li><strong>Center:</strong> Pure imaginary eigenvalues (neutrally stable)</li>
                    </ul>
                </div>

                <h3 class="text-xl font-semibold mb-4">Advanced Medical Applications</h3>

                <div class="space-y-6 mb-6">
                    <div class="bg-red-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-red-700 mb-2">
                            <i class="fas fa-heartbeat mr-2"></i>Cardiovascular Dynamics
                        </h4>
                        <p class="text-sm mb-2">Four-chamber heart model:</p>
                        <div class="math-container text-xs">
                            $$\begin{cases}
                            \frac{dV_{LV}}{dt} = Q_{in} - Q_{out} \\
                            \frac{dP_{LV}}{dt} = E(t)(V_{LV} - V_0) \\
                            \frac{dQ}{dt} = \frac{P_{LV} - P_{Ao}}{L} - \frac{RQ}{L}
                            \end{cases}$$
                        </div>
                    </div>

                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-700 mb-2">
                            <i class="fas fa-brain mr-2"></i>Neural Networks
                        </h4>
                        <p class="text-sm mb-2">Hodgkin-Huxley model for action potentials:</p>
                        <div class="math-container text-xs">
                            $$\begin{cases}
                            C\frac{dV}{dt} = I - g_K n^4(V-E_K) - g_{Na} m^3h(V-E_{Na}) - g_L(V-E_L) \\
                            \frac{dn}{dt} = \alpha_n(V)(1-n) - \beta_n(V)n \\
                            \frac{dm}{dt} = \alpha_m(V)(1-m) - \beta_m(V)m \\
                            \frac{dh}{dt} = \alpha_h(V)(1-h) - \beta_h(V)h
                            \end{cases}$$
                        </div>
                    </div>

                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-green-700 mb-2">
                            <i class="fas fa-dna mr-2"></i>Genetic Regulatory Networks
                        </h4>
                        <p class="text-sm mb-2">Gene expression dynamics:</p>
                        <div class="math-container text-xs">
                            $$\begin{cases}
                            \frac{dm_i}{dt} = \alpha_i f_i(\mathbf{p}) - \delta_m m_i \\
                            \frac{dp_i}{dt} = \beta_i m_i - \delta_p p_i
                            \end{cases}$$
                        </div>
                        <p class="text-xs mt-1">Where $m_i$ = mRNA, $p_i$ = protein concentrations</p>
                    </div>
                </div>

                <div class="bg-gray-100 p-6 rounded-lg">
                    <h4 class="font-semibold mb-3">
                        <i class="fas fa-chart-area mr-2"></i>Phase Portrait Analysis in Clinical Practice
                    </h4>
                    <div class="grid md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <p class="font-semibold mb-1">Diagnostic Applications:</p>
                            <ul class="list-disc pl-4">
                                <li>Identify abnormal system dynamics</li>
                                <li>Predict critical transitions</li>
                                <li>Optimize treatment protocols</li>
                            </ul>
                        </div>
                        <div>
                            <p class="font-semibold mb-1">Therapeutic Implications:</p>
                            <ul class="list-disc pl-4">
                                <li>Design control strategies</li>
                                <li>Prevent system instabilities</li>
                                <li>Maintain homeostasis</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Chapter 6: Numerical Methods -->
        <section class="p-8 section-break border-t">
            <h2 class="text-3xl font-bold mb-6 text-blue-600">
                <i class="fas fa-calculator mr-2"></i>Chapter 6: Numerical Methods
            </h2>

            <div class="prose max-w-none">
                <h3 class="text-xl font-semibold mb-4">Why Numerical Methods?</h3>
                <p class="mb-4">
                    Most medical ODEs cannot be solved analytically. Numerical methods provide approximate solutions that are often sufficient for clinical applications.
                </p>

                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-red-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-red-700 mb-2">
                            <i class="fas fa-times-circle mr-2"></i>Limitations of Analytical Methods
                        </h4>
                        <ul class="text-sm list-disc pl-4">
                            <li>Nonlinear equations rarely have closed-form solutions</li>
                            <li>Complex medical systems involve many variables</li>
                            <li>Parameter values may be uncertain or variable</li>
                        </ul>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-green-700 mb-2">
                            <i class="fas fa-check-circle mr-2"></i>Advantages of Numerical Methods
                        </h4>
                        <ul class="text-sm list-disc pl-4">
                            <li>Handle any type of ODE</li>
                            <li>Easily incorporate real patient data</li>
                            <li>Allow parameter sensitivity analysis</li>
                        </ul>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Euler's Method</h3>
                <p class="mb-4">
                    The simplest numerical method for solving $y' = f(t, y)$, $y(t_0) = y_0$:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">$$y_{n+1} = y_n + h \cdot f(t_n, y_n)$$</p>
                </div>
                <p class="mb-6">
                    Where $h$ is the step size and $t_n = t_0 + nh$.
                </p>

                <div class="bg-blue-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-blue-700 mb-3">
                        <i class="fas fa-pills mr-2"></i>Medical Example: Drug Elimination
                    </h4>
                    <p class="mb-2"><strong>Problem:</strong> Solve $\frac{dC}{dt} = -0.1C$, $C(0) = 100$ mg/L</p>
                    <p class="mb-2"><strong>Euler's Method with $h = 1$ hour:</strong></p>
                    <div class="overflow-x-auto mb-2">
                        <table class="min-w-full text-sm">
                            <thead>
                                <tr class="bg-blue-100">
                                    <th class="px-2 py-1 text-left">$t_n$ (h)</th>
                                    <th class="px-2 py-1 text-left">$C_n$ (mg/L)</th>
                                    <th class="px-2 py-1 text-left">$f(t_n, C_n)$</th>
                                    <th class="px-2 py-1 text-left">$C_{n+1}$</th>
                                    <th class="px-2 py-1 text-left">Exact</th>
                                    <th class="px-2 py-1 text-left">Error</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="px-2 py-1">0</td>
                                    <td class="px-2 py-1">100.0</td>
                                    <td class="px-2 py-1">-10.0</td>
                                    <td class="px-2 py-1">90.0</td>
                                    <td class="px-2 py-1">90.5</td>
                                    <td class="px-2 py-1">0.5</td>
                                </tr>
                                <tr class="bg-gray-50">
                                    <td class="px-2 py-1">1</td>
                                    <td class="px-2 py-1">90.0</td>
                                    <td class="px-2 py-1">-9.0</td>
                                    <td class="px-2 py-1">81.0</td>
                                    <td class="px-2 py-1">81.9</td>
                                    <td class="px-2 py-1">0.9</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-1">2</td>
                                    <td class="px-2 py-1">81.0</td>
                                    <td class="px-2 py-1">-8.1</td>
                                    <td class="px-2 py-1">72.9</td>
                                    <td class="px-2 py-1">74.1</td>
                                    <td class="px-2 py-1">1.2</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <p class="text-sm"><strong>Observation:</strong> Error accumulates over time; smaller step sizes improve accuracy</p>
                </div>

                <!-- Euler Method Comparison -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-3">Euler's Method vs Exact Solution</h4>
                    <div class="chart-container bg-white border rounded-lg p-4">
                        <canvas id="eulerMethodChart"></canvas>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Improved Euler Method (Heun's Method)</h3>
                <p class="mb-4">
                    A second-order method that averages slopes at beginning and end of interval:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">
                        $$\begin{align}
                        k_1 &= h \cdot f(t_n, y_n) \\
                        k_2 &= h \cdot f(t_n + h, y_n + k_1) \\
                        y_{n+1} &= y_n + \frac{1}{2}(k_1 + k_2)
                        \end{align}$$
                    </p>
                </div>

                <h3 class="text-xl font-semibold mb-4">Runge-Kutta Methods</h3>
                <p class="mb-4">
                    The fourth-order Runge-Kutta method (RK4) is the most popular:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">
                        $$\begin{align}
                        k_1 &= h \cdot f(t_n, y_n) \\
                        k_2 &= h \cdot f(t_n + \frac{h}{2}, y_n + \frac{k_1}{2}) \\
                        k_3 &= h \cdot f(t_n + \frac{h}{2}, y_n + \frac{k_2}{2}) \\
                        k_4 &= h \cdot f(t_n + h, y_n + k_3) \\
                        y_{n+1} &= y_n + \frac{1}{6}(k_1 + 2k_2 + 2k_3 + k_4)
                        \end{align}$$
                    </p>
                </div>

                <div class="bg-green-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-green-700 mb-3">
                        <i class="fas fa-heartbeat mr-2"></i>Medical Example: Cardiac Pacemaker Model
                    </h4>
                    <p class="mb-2"><strong>Problem:</strong> Van der Pol oscillator for heart rhythm</p>
                    <div class="math-container mb-2">
                        $$\frac{d^2x}{dt^2} - \mu(1-x^2)\frac{dx}{dt} + x = 0$$
                    </div>
                    <p class="mb-2">Convert to system:</p>
                    <div class="math-container mb-2">
                        $$\begin{cases}
                        \frac{dx}{dt} = y \\
                        \frac{dy}{dt} = \mu(1-x^2)y - x
                        \end{cases}$$
                    </div>
                    <p class="text-sm"><strong>Application:</strong> RK4 needed for accurate simulation of nonlinear cardiac dynamics</p>
                </div>

                <!-- Numerical Methods Comparison -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-3">Numerical Methods Accuracy Comparison</h4>
                    <div class="chart-container bg-white border rounded-lg p-4">
                        <canvas id="numericalMethodsChart"></canvas>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Adaptive Step Size Methods</h3>
                <p class="mb-4">
                    Medical simulations often require variable precision. Adaptive methods automatically adjust step size based on local error estimates.
                </p>

                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-yellow-700 mb-2">
                            <i class="fas fa-expand-arrows-alt mr-2"></i>Runge-Kutta-Fehlberg (RKF45)
                        </h4>
                        <ul class="text-sm list-disc pl-4">
                            <li>Uses 4th and 5th order estimates</li>
                            <li>Compares results to estimate error</li>
                            <li>Adjusts step size accordingly</li>
                        </ul>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-purple-700 mb-2">
                            <i class="fas fa-cogs mr-2"></i>Dormand-Prince (DP45)
                        </h4>
                        <ul class="text-sm list-disc pl-4">
                            <li>More efficient than RKF45</li>
                            <li>Better error control</li>
                            <li>Standard in many software packages</li>
                        </ul>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Stiff Equations</h3>
                <p class="mb-4">
                    Many medical systems involve processes with vastly different time scales, leading to stiff equations.
                </p>

                <div class="bg-red-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-red-700 mb-3">
                        <i class="fas fa-exclamation-triangle mr-2"></i>Example: Multi-Scale Pharmacokinetics
                    </h4>
                    <p class="mb-2"><strong>Problem:</strong> Drug absorption (minutes) vs elimination (hours)</p>
                    <div class="math-container mb-2">
                        $$\begin{cases}
                        \frac{dA}{dt} = -k_a A \quad \text{(fast absorption)} \\
                        \frac{dC}{dt} = \frac{k_a A}{V} - k_e C \quad \text{(slow elimination)}
                        \end{cases}$$
                    </div>
                    <p class="text-sm">When $k_a \gg k_e$, explicit methods require tiny step sizes</p>
                </div>

                <h4 class="text-lg font-semibold mb-3">Implicit Methods for Stiff Systems</h4>
                <div class="overflow-x-auto mb-6">
                    <table class="min-w-full bg-white border border-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 border-b text-left">Method</th>
                                <th class="px-4 py-2 border-b text-left">Formula</th>
                                <th class="px-4 py-2 border-b text-left">Stability</th>
                                <th class="px-4 py-2 border-b text-left">Medical Use</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b">Backward Euler</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$y_{n+1} = y_n + hf(t_{n+1}, y_{n+1})$</td>
                                <td class="px-4 py-2 border-b text-sm">A-stable</td>
                                <td class="px-4 py-2 border-b text-sm">Rapid drug kinetics</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b">Trapezoidal</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$y_{n+1} = y_n + \frac{h}{2}[f(t_n,y_n) + f(t_{n+1},y_{n+1})]$</td>
                                <td class="px-4 py-2 border-b text-sm">A-stable</td>
                                <td class="px-4 py-2 border-b text-sm">Cardiac models</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b">BDF2</td>
                                <td class="px-4 py-2 border-b font-mono text-sm">$y_{n+1} = \frac{4}{3}y_n - \frac{1}{3}y_{n-1} + \frac{2h}{3}f(t_{n+1}, y_{n+1})$</td>
                                <td class="px-4 py-2 border-b text-sm">A-stable</td>
                                <td class="px-4 py-2 border-b text-sm">Neural networks</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3 class="text-xl font-semibold mb-4">Systems of ODEs</h3>
                <p class="mb-4">
                    Vector form: $\mathbf{y}' = \mathbf{f}(t, \mathbf{y})$ where $\mathbf{y} = [y_1, y_2, \ldots, y_n]^T$
                </p>

                <div class="bg-blue-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-blue-700 mb-3">
                        <i class="fas fa-virus mr-2"></i>Implementation: SEIR Epidemic Model
                    </h4>
                    <div class="math-container mb-2">
                        $$\begin{cases}
                        \frac{dS}{dt} = -\beta \frac{SI}{N} \\
                        \frac{dE}{dt} = \beta \frac{SI}{N} - \sigma E \\
                        \frac{dI}{dt} = \sigma E - \gamma I \\
                        \frac{dR}{dt} = \gamma I
                        \end{cases}$$
                    </div>
                    <p class="text-sm">RK4 applied component-wise to each equation</p>
                </div>

                <h3 class="text-xl font-semibold mb-4">Error Analysis and Convergence</h3>

                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">
                            <i class="fas fa-chart-line mr-2"></i>Local Truncation Error
                        </h4>
                        <p class="text-sm mb-2">Error introduced in a single step:</p>
                        <ul class="text-sm list-disc pl-4">
                            <li>Euler: $O(h^2)$</li>
                            <li>RK2: $O(h^3)$</li>
                            <li>RK4: $O(h^5)$</li>
                        </ul>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">
                            <i class="fas fa-compress-arrows-alt mr-2"></i>Global Error
                        </h4>
                        <p class="text-sm mb-2">Accumulated error over time interval:</p>
                        <ul class="text-sm list-disc pl-4">
                            <li>Euler: $O(h)$</li>
                            <li>RK2: $O(h^2)$</li>
                            <li>RK4: $O(h^4)$</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-gray-100 p-6 rounded-lg">
                    <h4 class="font-semibold mb-3">
                        <i class="fas fa-desktop mr-2"></i>Software Implementation Guidelines
                    </h4>
                    <div class="grid md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <p class="font-semibold mb-1">Clinical Considerations:</p>
                            <ul class="list-disc pl-4">
                                <li>Choose method based on stiffness</li>
                                <li>Balance accuracy with computation time</li>
                                <li>Validate with known analytical solutions</li>
                                <li>Monitor conservation laws (mass, energy)</li>
                            </ul>
                        </div>
                        <div>
                            <p class="font-semibold mb-1">Popular Software Tools:</p>
                            <ul class="list-disc pl-4">
                                <li>MATLAB: ode45, ode15s, ode23t</li>
                                <li>Python: scipy.integrate</li>
                                <li>R: deSolve package</li>
                                <li>Specialized: COPASI, SBML</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Chapter 7: Pharmacokinetic Models -->
        <section class="p-8 section-break border-t">
            <h2 class="text-3xl font-bold mb-6 text-blue-600">
                <i class="fas fa-pills mr-2"></i>Chapter 7: Pharmacokinetic Models
            </h2>

            <div class="prose max-w-none">
                <h3 class="text-xl font-semibold mb-4">Introduction to Pharmacokinetics</h3>
                <p class="mb-4">
                    Pharmacokinetics describes how the body processes drugs through four main processes: Absorption, Distribution, Metabolism, and Excretion (ADME).
                </p>

                <div class="grid md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-blue-50 p-4 rounded-lg text-center">
                        <i class="fas fa-arrow-down text-2xl text-blue-600 mb-2"></i>
                        <h4 class="font-semibold text-sm">Absorption</h4>
                        <p class="text-xs mt-1">Drug enters bloodstream</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg text-center">
                        <i class="fas fa-share-alt text-2xl text-green-600 mb-2"></i>
                        <h4 class="font-semibold text-sm">Distribution</h4>
                        <p class="text-xs mt-1">Drug spreads through body</p>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-lg text-center">
                        <i class="fas fa-cogs text-2xl text-yellow-600 mb-2"></i>
                        <h4 class="font-semibold text-sm">Metabolism</h4>
                        <p class="text-xs mt-1">Drug is chemically altered</p>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg text-center">
                        <i class="fas fa-arrow-up text-2xl text-red-600 mb-2"></i>
                        <h4 class="font-semibold text-sm">Excretion</h4>
                        <p class="text-xs mt-1">Drug leaves the body</p>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">One-Compartment Model</h3>
                <p class="mb-4">
                    The simplest pharmacokinetic model assumes the body acts as a single, well-mixed compartment.
                </p>

                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold mb-3">Basic Equation</h4>
                    <div class="math-container mb-2">
                        $$V\frac{dC}{dt} = R_{in}(t) - k \cdot V \cdot C$$
                    </div>
                    <p class="mb-2">Simplifying:</p>
                    <div class="math-container mb-2">
                        $$\frac{dC}{dt} = \frac{R_{in}(t)}{V} - kC$$
                    </div>
                    <p class="text-sm">Where $C$ = concentration, $V$ = volume of distribution, $k$ = elimination rate constant</p>
                </div>

                <h4 class="text-lg font-semibold mb-3">IV Bolus Administration</h4>
                <p class="mb-4">
                    For instantaneous injection at $t = 0$:
                </p>
                <div class="bg-blue-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">
                        $$\begin{cases}
                        \frac{dC}{dt} = -kC \\
                        C(0) = \frac{D_0}{V}
                        \end{cases}$$
                    </p>
                    <p class="text-center mt-2">
                        <strong>Solution:</strong> $C(t) = \frac{D_0}{V}e^{-kt}$
                    </p>
                </div>

                <div class="bg-green-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-green-700 mb-3">
                        <i class="fas fa-calculator mr-2"></i>Clinical Parameters
                    </h4>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div>
                            <p class="font-semibold mb-2">Half-life:</p>
                            <div class="math-container text-sm">$$t_{1/2} = \frac{\ln 2}{k} = \frac{0.693}{k}$$</div>
                        </div>
                        <div>
                            <p class="font-semibold mb-2">Clearance:</p>
                            <div class="math-container text-sm">$$CL = kV = \frac{D_0}{AUC}$$</div>
                        </div>
                        <div>
                            <p class="font-semibold mb-2">Area Under Curve:</p>
                            <div class="math-container text-sm">$$AUC = \int_0^{\infty} C(t)dt = \frac{D_0}{kV}$$</div>
                        </div>
                        <div>
                            <p class="font-semibold mb-2">Mean Residence Time:</p>
                            <div class="math-container text-sm">$$MRT = \frac{1}{k}$$</div>
                        </div>
                    </div>
                </div>

                <!-- One-Compartment Model Visualization -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-3">One-Compartment Model: Different Routes</h4>
                    <div class="chart-container bg-white border rounded-lg p-4">
                        <canvas id="oneCompartmentChart"></canvas>
                    </div>
                </div>

                <h4 class="text-lg font-semibold mb-3">First-Order Absorption</h4>
                <p class="mb-4">
                    For oral administration with first-order absorption:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">
                        $$\begin{cases}
                        \frac{dA}{dt} = -k_a A \\
                        \frac{dC}{dt} = \frac{k_a A}{V} - kC \\
                        A(0) = D_0, \quad C(0) = 0
                        \end{cases}$$
                    </p>
                    <p class="text-center mt-2">
                        <strong>Solution:</strong> $C(t) = \frac{k_a D_0}{V(k_a - k)}(e^{-kt} - e^{-k_a t})$
                    </p>
                </div>

                <div class="bg-purple-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-purple-700 mb-3">
                        <i class="fas fa-chart-line mr-2"></i>Key Observations
                    </h4>
                    <ul class="text-sm list-disc pl-4">
                        <li><strong>Flip-flop kinetics:</strong> When $k_a < k$, absorption becomes rate-limiting</li>
                        <li><strong>Time to peak:</strong> $t_{max} = \frac{\ln(k_a/k)}{k_a - k}$</li>
                        <li><strong>Peak concentration:</strong> $C_{max} = \frac{k_a D_0}{V(k_a - k)}(e^{-kt_{max}} - e^{-k_a t_{max}})$</li>
                        <li><strong>Bioavailability:</strong> $F = \frac{AUC_{oral}}{AUC_{IV}}$</li>
                    </ul>
                </div>

                <h3 class="text-xl font-semibold mb-4">Two-Compartment Model</h3>
                <p class="mb-4">
                    More realistic model with central (plasma) and peripheral (tissue) compartments:
                </p>

                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">
                        $$\begin{cases}
                        V_1\frac{dC_1}{dt} = -(k_{10} + k_{12})V_1C_1 + k_{21}V_2C_2 \\
                        V_2\frac{dC_2}{dt} = k_{12}V_1C_1 - k_{21}V_2C_2
                        \end{cases}$$
                    </p>
                </div>

                <div class="mb-6">
                    <h4 class="font-semibold mb-2">Compartment Diagram</h4>
                    <div class="bg-white border-2 border-gray-300 rounded-lg p-6 flex items-center justify-center">
                        <div class="flex items-center space-x-8">
                            <div class="text-center">
                                <div class="w-20 h-20 bg-blue-100 border-2 border-blue-500 rounded-full flex items-center justify-center">
                                    <span class="font-semibold">Central<br/>$V_1, C_1$</span>
                                </div>
                                <div class="mt-2 text-sm">
                                    <div class="flex items-center justify-center">
                                        <i class="fas fa-arrow-down mr-1"></i>
                                        <span>$k_{10}$</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-arrow-right mr-2"></i>
                                    <span class="text-sm">$k_{12}$</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm mr-2">$k_{21}$</span>
                                    <i class="fas fa-arrow-left"></i>
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="w-20 h-20 bg-green-100 border-2 border-green-500 rounded-full flex items-center justify-center">
                                    <span class="font-semibold">Peripheral<br/>$V_2, C_2$</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <h4 class="text-lg font-semibold mb-3">Solution for IV Bolus</h4>
                <div class="bg-blue-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center mb-2">$$C_1(t) = Ae^{-\alpha t} + Be^{-\beta t}$$</p>
                    <p class="text-sm text-center">Where $\alpha$ and $\beta$ are roots of the characteristic equation</p>
                </div>

                <div class="bg-yellow-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-yellow-700 mb-3">
                        <i class="fas fa-hourglass-half mr-2"></i>Phase Interpretation
                    </h4>
                    <ul class="text-sm list-disc pl-4">
                        <li><strong>Distribution phase (α-phase):</strong> Rapid decline due to drug distribution to tissues</li>
                        <li><strong>Elimination phase (β-phase):</strong> Slower decline representing elimination from body</li>
                        <li><strong>Clinical significance:</strong> Loading dose needed to account for distribution</li>
                    </ul>
                </div>

                <!-- Two-Compartment Model Visualization -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-3">Two-Compartment Model: Distribution Phases</h4>
                    <div class="chart-container bg-white border rounded-lg p-4">
                        <canvas id="twoCompartmentPKChart"></canvas>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Nonlinear Pharmacokinetics</h3>
                <p class="mb-4">
                    When elimination processes become saturated, kinetics become nonlinear (Michaelis-Menten):
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">$$\frac{dC}{dt} = -\frac{V_{max}C}{K_m + C}$$</p>
                </div>

                <div class="bg-red-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-red-700 mb-3">
                        <i class="fas fa-exclamation-triangle mr-2"></i>Clinical Example: Phenytoin
                    </h4>
                    <p class="mb-2"><strong>Problem:</strong> Phenytoin shows dose-dependent kinetics</p>
                    <ul class="text-sm list-disc pl-4 mb-2">
                        <li>At low doses: First-order elimination (constant half-life)</li>
                        <li>At high doses: Zero-order elimination (constant rate)</li>
                        <li>Small dose increases can cause disproportionate concentration increases</li>
                    </ul>
                    <p class="text-sm"><strong>Clinical Impact:</strong> Requires careful dose titration and frequent monitoring</p>
                </div>

                <h3 class="text-xl font-semibold mb-4">Population Pharmacokinetics</h3>
                <p class="mb-4">
                    Accounts for variability between patients using covariates:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg mb-6 math-container">
                    <p class="text-center">$$CL_i = CL_{pop} \cdot \left(\frac{WT_i}{70}\right)^{0.75} \cdot e^{\eta_i}$$</p>
                </div>

                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-green-700 mb-2">
                            <i class="fas fa-users mr-2"></i>Fixed Effects
                        </h4>
                        <ul class="text-sm list-disc pl-4">
                            <li>Age effects on clearance</li>
                            <li>Weight effects on volume</li>
                            <li>Renal function effects</li>
                            <li>Gender differences</li>
                        </ul>
                    </div>
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-700 mb-2">
                            <i class="fas fa-random mr-2"></i>Random Effects
                        </h4>
                        <ul class="text-sm list-disc pl-4">
                            <li>Between-subject variability</li>
                            <li>Within-subject variability</li>
                            <li>Residual error</li>
                            <li>Measurement error</li>
                        </ul>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Dosing Regimen Design</h3>
                
                <div class="bg-purple-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-purple-700 mb-3">
                        <i class="fas fa-clock mr-2"></i>Multiple Dosing Equations
                    </h4>
                    <p class="mb-2"><strong>Steady-state concentration (IV infusion):</strong></p>
                    <div class="math-container mb-2">$$C_{ss} = \frac{R_0}{CL}$$</div>
                    <p class="mb-2"><strong>Average steady-state (multiple oral doses):</strong></p>
                    <div class="math-container mb-2">$$C_{ss,avg} = \frac{F \cdot D}{CL \cdot \tau}$$</div>
                    <p class="mb-2"><strong>Time to steady-state:</strong></p>
                    <div class="math-container">$$t_{ss} \approx 5 \times t_{1/2}$$</div>
                </div>

                <!-- Multiple Dosing Visualization -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-3">Multiple Dosing to Steady State</h4>
                    <div class="chart-container bg-white border rounded-lg p-4">
                        <canvas id="multipleDoseChart"></canvas>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Therapeutic Drug Monitoring</h3>

                <div class="overflow-x-auto mb-6">
                    <table class="min-w-full bg-white border border-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 border-b text-left">Drug</th>
                                <th class="px-4 py-2 border-b text-left">Therapeutic Range</th>
                                <th class="px-4 py-2 border-b text-left">Half-life</th>
                                <th class="px-4 py-2 border-b text-left">Key Considerations</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b font-semibold">Digoxin</td>
                                <td class="px-4 py-2 border-b">1.0-2.0 ng/mL</td>
                                <td class="px-4 py-2 border-b">36 hours</td>
                                <td class="px-4 py-2 border-b text-sm">Narrow therapeutic window</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b font-semibold">Theophylline</td>
                                <td class="px-4 py-2 border-b">10-20 μg/mL</td>
                                <td class="px-4 py-2 border-b">8 hours</td>
                                <td class="px-4 py-2 border-b text-sm">Nonlinear kinetics at high doses</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b font-semibold">Phenytoin</td>
                                <td class="px-4 py-2 border-b">10-20 μg/mL</td>
                                <td class="px-4 py-2 border-b">Variable</td>
                                <td class="px-4 py-2 border-b text-sm">Michaelis-Menten elimination</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border-b font-semibold">Lithium</td>
                                <td class="px-4 py-2 border-b">0.6-1.2 mEq/L</td>
                                <td class="px-4 py-2 border-b">18-24 hours</td>
                                <td class="px-4 py-2 border-b text-sm">Renal elimination, dehydration risk</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="bg-gray-100 p-6 rounded-lg">
                    <h4 class="font-semibold mb-3">
                        <i class="fas fa-user-md mr-2"></i>Clinical Applications Summary
                    </h4>
                    <div class="grid md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <p class="font-semibold mb-1">Dose Optimization:</p>
                            <ul class="list-disc pl-4">
                                <li>Individualize based on patient factors</li>
                                <li>Account for drug interactions</li>
                                <li>Consider disease state effects</li>
                                <li>Monitor for toxicity and efficacy</li>
                            </ul>
                        </div>
                        <div>
                            <p class="font-semibold mb-1">Model-Based Dosing:</p>
                            <ul class="list-disc pl-4">
                                <li>Bayesian forecasting</li>
                                <li>Population PK/PD models</li>
                                <li>Precision dosing software</li>
                                <li>Real-time dose adjustment</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Chapter 8: Epidemiological Models -->
        <section class="p-8 section-break border-t">
            <h2 class="text-3xl font-bold mb-6 text-blue-600">
                <i class="fas fa-virus mr-2"></i>Chapter 8: Epidemiological Models
            </h2>

            <div class="prose max-w-none">
                <h3 class="text-xl font-semibold mb-4">Introduction to Mathematical Epidemiology</h3>
                <p class="mb-4">
                    Mathematical epidemiology uses differential equations to model the spread of infectious diseases through populations, predict outbreak trajectories, and evaluate intervention strategies.
                </p>

                <div class="grid md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-blue-50 p-4 rounded-lg text-center">
                        <i class="fas fa-chart-line text-2xl text-blue-600 mb-2"></i>
                        <h4 class="font-semibold text-sm">Prediction</h4>
                        <p class="text-xs mt-1">Forecast disease spread</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg text-center">
                        <i class="fas fa-shield-alt text-2xl text-green-600 mb-2"></i>
                        <h4 class="font-semibold text-sm">Prevention</h4>
                        <p class="text-xs mt-1">Design intervention strategies</p>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg text-center">
                        <i class="fas fa-cogs text-2xl text-purple-600 mb-2"></i>
                        <h4 class="font-semibold text-sm">Control</h4>
                        <p class="text-xs mt-1">Optimize resource allocation</p>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">The Basic SIR Model</h3>
                <p class="mb-4">
                    The fundamental model divides the population into three compartments:
                </p>
                
                <div class="mb-6">
                    <div class="bg-white border-2 border-gray-300 rounded-lg p-6">
                        <div class="flex items-center justify-center space-x-8">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-blue-100 border-2 border-blue-500 rounded-full flex items-center justify-center">
                                    <span class="font-bold text-lg">S</span>
                                </div>
                                <p class="text-sm mt-1 font-semibold">Susceptible</p>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-arrow-right text-xl text-red-500"></i>
                                <span class="text-sm ml-2 mr-2">$\beta SI$</span>
                            </div>
                            <div class="text-center">
                                <div class="w-16 h-16 bg-red-100 border-2 border-red-500 rounded-full flex items-center justify-center">
                                    <span class="font-bold text-lg">I</span>
                                </div>
                                <p class="text-sm mt-1 font-semibold">Infected</p>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-arrow-right text-xl text-green-500"></i>
                                <span class="text-sm ml-2 mr-2">$\gamma I$</span>
                            </div>
                            <div class="text-center">
                                <div class="w-16 h-16 bg-green-100 border-2 border-green-500 rounded-full flex items-center justify-center">
                                    <span class="font-bold text-lg">R</span>
                                </div>
                                <p class="text-sm mt-1 font-semibold">Recovered</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold mb-3">SIR System of ODEs</h4>
                    <div class="math-container">
                        $$\begin{cases}
                        \frac{dS}{dt} = -\beta \frac{SI}{N} \\
                        \frac{dI}{dt} = \beta \frac{SI}{N} - \gamma I \\
                        \frac{dR}{dt} = \gamma I
                        \end{cases}$$
                    </div>
                </div>

                <div class="bg-blue-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-blue-700 mb-3">
                        <i class="fas fa-info-circle mr-2"></i>Parameter Interpretation
                    </h4>
                    <ul class="list-disc pl-4 text-sm">
                        <li><strong>$\beta$:</strong> Transmission rate (contacts per time × probability of transmission per contact)</li>
                        <li><strong>$\gamma$:</strong> Recovery rate (inverse of infectious period)</li>
                        <li><strong>$N = S + I + R$:</strong> Total population (constant)</li>
                        <li><strong>$R_0 = \beta/\gamma$:</strong> Basic reproduction number</li>
                    </ul>
                </div>

                <h3 class="text-xl font-semibold mb-4">Basic Reproduction Number ($R_0$)</h3>
                <p class="mb-4">
                    The most important epidemiological parameter:
                </p>

                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-green-700 mb-2">
                            <i class="fas fa-arrow-down mr-2"></i>$R_0 < 1$
                        </h4>
                        <ul class="text-sm list-disc pl-4">
                            <li>Disease dies out</li>
                            <li>Each infected person infects less than one other</li>
                            <li>Epidemic threshold not crossed</li>
                        </ul>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-red-700 mb-2">
                            <i class="fas fa-arrow-up mr-2"></i>$R_0 > 1$
                        </h4>
                        <ul class="text-sm list-disc pl-4">
                            <li>Epidemic occurs</li>
                            <li>Each infected person infects more than one other</li>
                            <li>Disease spreads through population</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-yellow-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-yellow-700 mb-3">
                        <i class="fas fa-calculator mr-2"></i>$R_0$ Values for Common Diseases
                    </h4>
                    <div class="grid md:grid-cols-2 gap-4">
                        <ul class="text-sm list-disc pl-4">
                            <li>Measles: 12-18</li>
                            <li>Pertussis: 12-17</li>
                            <li>Smallpox: 5-7</li>
                            <li>Polio: 5-7</li>
                        </ul>
                        <ul class="text-sm list-disc pl-4">
                            <li>Influenza (1918): 2-3</li>
                            <li>COVID-19 (original): 2.5-3</li>
                            <li>SARS: 2-5</li>
                            <li>Ebola: 1.5-2.5</li>
                        </ul>
                    </div>
                </div>

                <!-- R0 Impact Visualization -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-3">Impact of $R_0$ on Epidemic Curves</h4>
                    <div class="chart-container bg-white border rounded-lg p-4">
                        <canvas id="r0ComparisonChart"></canvas>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">SEIR Model</h3>
                <p class="mb-4">
                    Includes an exposed (incubating) compartment for diseases with latent periods:
                </p>

                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold mb-3">SEIR System</h4>
                    <div class="math-container">
                        $$\begin{cases}
                        \frac{dS}{dt} = -\beta \frac{SI}{N} \\
                        \frac{dE}{dt} = \beta \frac{SI}{N} - \sigma E \\
                        \frac{dI}{dt} = \sigma E - \gamma I \\
                        \frac{dR}{dt} = \gamma I
                        \end{cases}$$
                    </div>
                    <p class="text-sm mt-2">Where $\sigma$ is the rate of progression from exposed to infectious (inverse of incubation period)</p>
                </div>

                <div class="bg-purple-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-purple-700 mb-3">
                        <i class="fas fa-virus mr-2"></i>COVID-19 Example
                    </h4>
                    <p class="mb-2"><strong>Typical Parameters:</strong></p>
                    <ul class="text-sm list-disc pl-4 mb-2">
                        <li>Incubation period: 5.1 days ($\sigma = 1/5.1$)</li>
                        <li>Infectious period: 3.3 days ($\gamma = 1/3.3$)</li>
                        <li>$R_0 \approx 2.5$ (original strain)</li>
                        <li>Generation time: 6.5 days</li>
                    </ul>
                    <p class="text-sm"><strong>Clinical significance:</strong> Long incubation period allows asymptomatic transmission</p>
                </div>

                <!-- SEIR Model Visualization -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold mb-3">SEIR Model Dynamics</h4>
                    <div class="chart-container bg-white border rounded-lg p-4">
                        <canvas id="seirModelChart"></canvas>
                    </div>
                </div>

                <h3 class="text-xl font-semibold mb-4">Vaccination Models</h3>
                <p class="mb-4">
                    Incorporating vaccination into epidemiological models:
                </p>

                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold mb-3">SIR with Vaccination</h4>
                    <div class="math-container">
                        $$\begin{cases}
                        \frac{dS}{dt} = \mu N - \beta \frac{SI}{N} - \mu S - \nu S \\
                        \frac{dI}{dt} = \beta \frac{SI}{N} - \gamma I - \mu I \\
                        \frac{dR}{dt} = \gamma I - \mu R + \nu S
                        \end{cases}$$
                    </div>
                    <p class="text-sm mt-2">Where $\mu$ is birth/death rate and $\nu$ is vaccination rate</p>
                </div>

                <div class="bg-green-50 p-6 rounded-lg mb-6">
                    <h4 class="font-semibold text-green-700 mb-3">
                        <i class="fas fa-syringe mr-2"></i>Herd Immunity Threshold
                    </h4>
                
    <script id="html_badge_script1">
        window.__genspark_remove_badge_link = "https://www.genspark.ai/api/html_badge/" +
            "remove_badge?token=To%2FBnjzloZ3UfQdcSaYfDj4a5yHc2cSyxfdIGrjS7hKSusfvQ12dMaVpxep9HC1vcXicEhQho3vX0Jkry1D5U8cWtqGQ4jDyPSsVDdhTkagM7%2BYdGhkPYrVU7rvm0j4MFxzeSuL2Pxs0wuqlIzOBIWSjFMinhdo3wQtWyySm%2Fr%2BHT05jqi0M3Qq8YFQdPTJ9zNwQepoFVc7KIGJSaYULYa508GKHfcIZys02d0TDIYzEc%2BMN624XlIBphtX1xH%2FOUQ7dscU4HUIiWDtXt8NdXyk26nAHAtuetrTqjrhEyJfqUN2KZkEBgrAEOR9p0QcTa6pN%2B4hEiNCoM3K9HqcYU6jVLEJGe1fGmMZjL7bSgVCzR6hs%2B%2FyIxcStclh%2Bvq74W8ik33ok%2Fbbt4YB7KbK1KqjfvL7xlVyoLmYhhqYq%2BxB%2FHjJ1MTwhU38%2BZqWzLtK7eZMkhJK3uXtcG%2FMsVqy0HUnTJsOSFDCYfcE%2FOqPXrxwdFdzGa%2FPGcVu9harLWSXbgkCNSZv%2F6nkIsFic600FBw%3D%3D";
        window.__genspark_locale = "en-US";
        window.__genspark_token = "To/BnjzloZ3UfQdcSaYfDj4a5yHc2cSyxfdIGrjS7hKSusfvQ12dMaVpxep9HC1vcXicEhQho3vX0Jkry1D5U8cWtqGQ4jDyPSsVDdhTkagM7+YdGhkPYrVU7rvm0j4MFxzeSuL2Pxs0wuqlIzOBIWSjFMinhdo3wQtWyySm/r+HT05jqi0M3Qq8YFQdPTJ9zNwQepoFVc7KIGJSaYULYa508GKHfcIZys02d0TDIYzEc+MN624XlIBphtX1xH/OUQ7dscU4HUIiWDtXt8NdXyk26nAHAtuetrTqjrhEyJfqUN2KZkEBgrAEOR9p0QcTa6pN+4hEiNCoM3K9HqcYU6jVLEJGe1fGmMZjL7bSgVCzR6hs+/yIxcStclh+vq74W8ik33ok/bbt4YB7KbK1KqjfvL7xlVyoLmYhhqYq+xB/HjJ1MTwhU38+ZqWzLtK7eZMkhJK3uXtcG/MsVqy0HUnTJsOSFDCYfcE/OqPXrxwdFdzGa/PGcVu9harLWSXbgkCNSZv/6nkIsFic600FBw==";
    </script>
    
    <script id="html_notice_dialog_script" src="https://www.genspark.ai/notice_dialog.js"></script>
    