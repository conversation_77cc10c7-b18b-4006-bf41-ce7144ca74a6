\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\@writefile{toc}{\contentsline {part}{I\hspace  {1em}Mathematical Foundations}{3}{part.1}\protected@file@percent }
\@writefile{toc}{\contentsline {chapter}{\numberline {1}Introduction to ODEs in Medicine}{4}{chapter.1}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {1.1}What are Ordinary Differential Equations?}{4}{section.1.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {1.2}Why ODEs Matter in Medicine}{4}{section.1.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {1.3}Historical Context and Modern Applications}{5}{section.1.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {1.4}Learning Objectives}{5}{section.1.4}\protected@file@percent }
\@writefile{toc}{\contentsline {chapter}{\numberline {2}Mathematical Foundations}{6}{chapter.2}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {2.1}Basic Terminology: Order, Degree, Linearity}{6}{section.2.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {2.2}Classification of ODEs}{6}{section.2.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {2.3}Solution Concepts: General and Particular Solutions}{7}{section.2.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {2.4}Initial Value Problems (IVPs)}{8}{section.2.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {2.5}The Importance of Existence and Uniqueness}{8}{section.2.5}\protected@file@percent }
\@writefile{toc}{\contentsline {chapter}{\numberline {3}First-Order Ordinary Differential Equations}{9}{chapter.3}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {3.1}Separable Equations: Modeling Exponential Processes}{9}{section.3.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {3.2}Linear First-Order ODEs: The Integrating Factor Method}{10}{section.3.2}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {3.1}{\ignorespaces Drug Elimination Curves. The plot illustrates exponential decay of drug concentration following an IV bolus for different elimination rate constants ($k$). A higher $k$ value leads to a faster decay and a shorter half-life. Triangle markers indicate the half-life points where concentration drops to 50\% of the initial value. The mathematical model follows $C(t) = C_0 e^{-kt}$ where $C_0 = 100$ mg/L.}}{11}{figure.3.1}\protected@file@percent }
\newlabel{fig:drug_elimination}{{3.1}{11}{Drug Elimination Curves. The plot illustrates exponential decay of drug concentration following an IV bolus for different elimination rate constants ($k$). A higher $k$ value leads to a faster decay and a shorter half-life. Triangle markers indicate the half-life points where concentration drops to 50\% of the initial value. The mathematical model follows $C(t) = C_0 e^{-kt}$ where $C_0 = 100$ mg/L}{figure.3.1}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {3.2}{\ignorespaces IV Infusion to Steady State. The plot shows the drug concentration rising and asymptotically approaching the steady-state concentration ($C_{ss}$) over time during a constant-rate IV infusion. The mathematical model follows $C(t) = \frac  {R}{k}(1 - e^{-kt})$ where $R = 50$ mg/hr is the infusion rate and $k = 0.1$ hr$^{-1}$ is the elimination rate constant. Green dots mark key milestones at 1, 2, 3, and 5 half-lives, showing the percentage of steady state reached. Steady state is achieved after approximately 5 half-lives (35 hours).}}{12}{figure.3.2}\protected@file@percent }
\newlabel{fig:iv_infusion_steady_state}{{3.2}{12}{IV Infusion to Steady State. The plot shows the drug concentration rising and asymptotically approaching the steady-state concentration ($C_{ss}$) over time during a constant-rate IV infusion. The mathematical model follows $C(t) = \frac {R}{k}(1 - e^{-kt})$ where $R = 50$ mg/hr is the infusion rate and $k = 0.1$ hr$^{-1}$ is the elimination rate constant. Green dots mark key milestones at 1, 2, 3, and 5 half-lives, showing the percentage of steady state reached. Steady state is achieved after approximately 5 half-lives (35 hours)}{figure.3.2}{}}
\@writefile{toc}{\contentsline {section}{\numberline {3.3}Nonlinear Dynamics: The Logistic Growth Model}{12}{section.3.3}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {3.3}{\ignorespaces Logistic Growth vs. Exponential Growth. The comparison shows that while exponential growth (red) is unbounded and continues indefinitely, logistic growth (blue) realistically models the slowing of growth as the population approaches the environmental carrying capacity ($K = 100$). The logistic curve exhibits three distinct phases: initial exponential-like growth, an inflection point at $K/2$ where growth rate is maximum, and saturation as the population approaches $K$. This S-shaped (sigmoidal) curve is characteristic of many biological systems including tumor growth, bacterial cultures, and population dynamics where resources become limiting.}}{13}{figure.3.3}\protected@file@percent }
\newlabel{fig:logistic_vs_exponential}{{3.3}{13}{Logistic Growth vs. Exponential Growth. The comparison shows that while exponential growth (red) is unbounded and continues indefinitely, logistic growth (blue) realistically models the slowing of growth as the population approaches the environmental carrying capacity ($K = 100$). The logistic curve exhibits three distinct phases: initial exponential-like growth, an inflection point at $K/2$ where growth rate is maximum, and saturation as the population approaches $K$. This S-shaped (sigmoidal) curve is characteristic of many biological systems including tumor growth, bacterial cultures, and population dynamics where resources become limiting}{figure.3.3}{}}
\@writefile{toc}{\contentsline {section}{\numberline {3.4}Bernoulli Equations and Allometric Scaling}{14}{section.3.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {3.5}Qualitative Analysis: Understanding Behavior Without Solutions}{14}{section.3.5}\protected@file@percent }
\@writefile{toc}{\contentsline {chapter}{\numberline {4}Higher-Order Ordinary Differential Equations}{16}{chapter.4}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {4.1}Second-Order Linear ODEs with Constant Coefficients}{16}{section.4.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {4.2}Oscillatory Systems in Physiology}{17}{section.4.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {4.3}Nonhomogeneous Equations: Method of Undetermined Coefficients}{17}{section.4.3}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {4.1}{\ignorespaces Damped Harmonic Oscillator Response. The plot shows the response of a second-order system to an initial displacement based on the damping ratio ($\zeta $). The mathematical model follows $m\ddot  {x} + c\dot  {x} + kx = 0$ or in standard form $\ddot  {x} + 2\zeta \omega _0\dot  {x} + \omega _0^2 x = 0$ where $\omega _0 = 2$ rad/s is the natural frequency. Underdamped systems ($\zeta < 1$, green) oscillate with exponentially decaying amplitude shown by the dotted envelope. Critically damped systems ($\zeta = 1$, orange) return to equilibrium in the shortest time without oscillation. Overdamped systems ($\zeta > 1$, red) return slowly to equilibrium without oscillation. The settling time $t_s$ marks when the response stays within a specified tolerance of the final value.}}{18}{figure.4.1}\protected@file@percent }
\newlabel{fig:damped_harmonic_oscillator}{{4.1}{18}{Damped Harmonic Oscillator Response. The plot shows the response of a second-order system to an initial displacement based on the damping ratio ($\zeta $). The mathematical model follows $m\ddot {x} + c\dot {x} + kx = 0$ or in standard form $\ddot {x} + 2\zeta \omega _0\dot {x} + \omega _0^2 x = 0$ where $\omega _0 = 2$ rad/s is the natural frequency. Underdamped systems ($\zeta < 1$, green) oscillate with exponentially decaying amplitude shown by the dotted envelope. Critically damped systems ($\zeta = 1$, orange) return to equilibrium in the shortest time without oscillation. Overdamped systems ($\zeta > 1$, red) return slowly to equilibrium without oscillation. The settling time $t_s$ marks when the response stays within a specified tolerance of the final value}{figure.4.1}{}}
\@writefile{toc}{\contentsline {section}{\numberline {4.4}The General Approach: Variation of Parameters}{19}{section.4.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {4.5}Applications in Biomechanics}{20}{section.4.5}\protected@file@percent }
\@writefile{toc}{\contentsline {chapter}{\numberline {5}Systems of Ordinary Differential Equations}{21}{chapter.5}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {5.1}Introduction to Coupled Systems}{21}{section.5.1}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {5.1}{\ignorespaces SIR Model Dynamics. This plot shows the typical progression of an epidemic modeled by the system of coupled ODEs. The number of susceptible individuals (S, blue) declines as people become infected, the number of infected individuals (I, red) rises to a peak and then falls as people recover, and the number of recovered individuals (R, green) grows and plateaus. The model parameters are $\beta = 0.5$ day$^{-1}$, $\gamma = 0.2$ day$^{-1}$, giving $R_0 = 2.5$. The epidemic peak occurs around day 25, and the final attack rate is approximately 80\% of the population. The dashed line indicates the herd immunity threshold, above which the susceptible population must remain to prevent future outbreaks.}}{22}{figure.5.1}\protected@file@percent }
\newlabel{fig:sir_model_dynamics}{{5.1}{22}{SIR Model Dynamics. This plot shows the typical progression of an epidemic modeled by the system of coupled ODEs. The number of susceptible individuals (S, blue) declines as people become infected, the number of infected individuals (I, red) rises to a peak and then falls as people recover, and the number of recovered individuals (R, green) grows and plateaus. The model parameters are $\beta = 0.5$ day$^{-1}$, $\gamma = 0.2$ day$^{-1}$, giving $R_0 = 2.5$. The epidemic peak occurs around day 25, and the final attack rate is approximately 80\% of the population. The dashed line indicates the herd immunity threshold, above which the susceptible population must remain to prevent future outbreaks}{figure.5.1}{}}
\@writefile{toc}{\contentsline {section}{\numberline {5.2}Linear Systems and the Eigenvalue Method}{23}{section.5.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {5.3}Nonlinear Systems and Stability Analysis}{23}{section.5.3}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {5.2}{\ignorespaces Two-Compartment Pharmacokinetic Model. The plot shows the concentration in the central compartment (plasma) declining rapidly during the initial distribution phase ($\alpha $-phase), followed by a slower decline during the elimination phase ($\beta $-phase). The peripheral compartment concentration rises to a peak and then falls. The model diagram shows the compartmental structure with transfer rate constants.}}{24}{figure.5.2}\protected@file@percent }
\newlabel{fig:two_compartment_pk}{{5.2}{24}{Two-Compartment Pharmacokinetic Model. The plot shows the concentration in the central compartment (plasma) declining rapidly during the initial distribution phase ($\alpha $-phase), followed by a slower decline during the elimination phase ($\beta $-phase). The peripheral compartment concentration rises to a peak and then falls. The model diagram shows the compartmental structure with transfer rate constants}{figure.5.2}{}}
\@writefile{toc}{\contentsline {section}{\numberline {5.4}Predator-Prey Dynamics in Medicine}{24}{section.5.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {5.5}Advanced System Models in Physiology}{25}{section.5.5}\protected@file@percent }
\@writefile{toc}{\contentsline {chapter}{\numberline {6}Numerical Methods for ODEs}{26}{chapter.6}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {6.1}The Need for Numerical Solutions}{26}{section.6.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {6.2}The Euler Method: A First Approximation}{26}{section.6.2}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {6.1}{\ignorespaces Euler's Method vs. Exact Solution. The plot demonstrates how Euler's method (orange points and lines) systematically underestimates the exact solution $y = e^t$ (green curve) for the differential equation $\frac  {dy}{dt} = y$ with $y(0) = 1$. Using step size $h = 0.5$, each linear segment (red dashed tangent lines) approximates the curve using the slope at the beginning of the interval. The error accumulates with each step, showing that at $t = 1$, Euler gives $y \approx 2.25$ versus the exact value $e \approx 2.718$, and at $t = 2$, Euler gives $y \approx 5.063$ versus $e^2 \approx 7.389$. This systematic underestimation occurs because the method cannot capture the increasing slope of the exponential function within each interval.}}{27}{figure.6.1}\protected@file@percent }
\newlabel{fig:euler_vs_exact}{{6.1}{27}{Euler's Method vs. Exact Solution. The plot demonstrates how Euler's method (orange points and lines) systematically underestimates the exact solution $y = e^t$ (green curve) for the differential equation $\frac {dy}{dt} = y$ with $y(0) = 1$. Using step size $h = 0.5$, each linear segment (red dashed tangent lines) approximates the curve using the slope at the beginning of the interval. The error accumulates with each step, showing that at $t = 1$, Euler gives $y \approx 2.25$ versus the exact value $e \approx 2.718$, and at $t = 2$, Euler gives $y \approx 5.063$ versus $e^2 \approx 7.389$. This systematic underestimation occurs because the method cannot capture the increasing slope of the exponential function within each interval}{figure.6.1}{}}
\@writefile{toc}{\contentsline {section}{\numberline {6.3}Higher-Order Methods: Runge-Kutta}{27}{section.6.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {6.4}Adaptive Step-Size Control}{28}{section.6.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {6.5}Handling Stiff Equations in Medical Models}{28}{section.6.5}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {6.2}{\ignorespaces Numerical Methods Accuracy Comparison. For the same step size ($h = 0.25$), the fourth-order Runge-Kutta (RK4) method (green triangles) provides dramatically better accuracy than the first-order Euler method (red circles) or second-order Heun method (orange squares) when solving $\frac  {dy}{dt} = y$ with $y(0) = 1$. At $t = 2$, the exact solution is $e^2 = 7.3891$. The error ratio is approximately $14290:1430:1$, demonstrating that RK4's fourth-order accuracy provides orders of magnitude better precision than lower-order methods.}}{29}{figure.6.2}\protected@file@percent }
\newlabel{fig:numerical_methods_comparison}{{6.2}{29}{Numerical Methods Accuracy Comparison. For the same step size ($h = 0.25$), the fourth-order Runge-Kutta (RK4) method (green triangles) provides dramatically better accuracy than the first-order Euler method (red circles) or second-order Heun method (orange squares) when solving $\frac {dy}{dt} = y$ with $y(0) = 1$. At $t = 2$, the exact solution is $e^2 = 7.3891$. The error ratio is approximately $14290:1430:1$, demonstrating that RK4's fourth-order accuracy provides orders of magnitude better precision than lower-order methods}{figure.6.2}{}}
\@writefile{toc}{\contentsline {section}{\numberline {6.6}Software for Solving ODEs}{30}{section.6.6}\protected@file@percent }
\@writefile{toc}{\contentsline {part}{II\hspace  {1em}Core Medical Applications}{31}{part.2}\protected@file@percent }
\@writefile{toc}{\contentsline {chapter}{\numberline {7}Pharmacokinetic (PK) Models}{32}{chapter.7}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {7.1}Introduction to Pharmacokinetics (ADME)}{32}{section.7.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {7.2}The One-Compartment Model}{32}{section.7.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {7.3}The Two-Compartment Model}{33}{section.7.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {7.4}Nonlinear Pharmacokinetics: Michaelis-Menten Elimination}{34}{section.7.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {7.5}Population Pharmacokinetics and Variability}{34}{section.7.5}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {7.6}Dosing Regimen Design}{35}{section.7.6}\protected@file@percent }
\@writefile{toc}{\contentsline {chapter}{\numberline {8}Cardiac Modeling Applications}{36}{chapter.8}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {8.1}The Heart as a Dynamic System}{36}{section.8.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {8.2}Modeling the Cardiac Action Potential}{36}{section.8.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {8.3}Models of the Heart's Pacemaker}{37}{section.8.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {8.4}Hemodynamic Modeling: The Windkessel Model}{37}{section.8.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {8.5}Integrated Models of the Cardiovascular System}{37}{section.8.5}\protected@file@percent }
\@writefile{toc}{\contentsline {part}{III\hspace  {1em}Broader Applications and Advanced Topics}{39}{part.3}\protected@file@percent }
\@writefile{toc}{\contentsline {chapter}{\numberline {9}Epidemiological Models}{40}{chapter.9}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {9.1}Introduction to Mathematical Epidemiology}{40}{section.9.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {9.2}The Basic SIR Model and the Reproduction Number}{40}{section.9.2}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {9.1}{\ignorespaces Impact of $R_0$ on Epidemic Curves. The plot demonstrates how the basic reproduction number ($R_0$) fundamentally determines epidemic dynamics. Higher $R_0$ values lead to faster, more explosive epidemics with higher peak infections, earlier time to peak, and larger final attack rates. The curves show infected individuals over time for a population of 10,000 with $\gamma = 0.1$ day$^{-1}$ (10-day infectious period) and different transmission rates: $R_0 = 1.5$ (green, $\beta = 0.15$), $R_0 = 2.5$ (yellow, $\beta = 0.25$), $R_0 = 4.0$ (orange, $\beta = 0.4$), and $R_0 = 6.0$ (red, $\beta = 0.6$). The critical threshold at $R_0 = 1$ separates epidemic growth from decline. Higher reproduction numbers result in exponentially faster spread, overwhelming healthcare systems and requiring more aggressive interventions to control.}}{41}{figure.9.1}\protected@file@percent }
\newlabel{fig:r0_impact_epidemic_curves}{{9.1}{41}{Impact of $R_0$ on Epidemic Curves. The plot demonstrates how the basic reproduction number ($R_0$) fundamentally determines epidemic dynamics. Higher $R_0$ values lead to faster, more explosive epidemics with higher peak infections, earlier time to peak, and larger final attack rates. The curves show infected individuals over time for a population of 10,000 with $\gamma = 0.1$ day$^{-1}$ (10-day infectious period) and different transmission rates: $R_0 = 1.5$ (green, $\beta = 0.15$), $R_0 = 2.5$ (yellow, $\beta = 0.25$), $R_0 = 4.0$ (orange, $\beta = 0.4$), and $R_0 = 6.0$ (red, $\beta = 0.6$). The critical threshold at $R_0 = 1$ separates epidemic growth from decline. Higher reproduction numbers result in exponentially faster spread, overwhelming healthcare systems and requiring more aggressive interventions to control}{figure.9.1}{}}
\@writefile{toc}{\contentsline {section}{\numberline {9.3}The SEIR Model: Incorporating a Latent Period}{41}{section.9.3}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {9.2}{\ignorespaces SEIR Model Dynamics. Comparison of SIR and SEIR models showing the impact of including an Exposed (E) compartment representing the latent period. The SIR model (red dashed line) shows infected individuals peaking at day 25, while the SEIR model introduces delay through the latent period: exposed individuals (orange) peak at day 20, followed by infected individuals (blue) peaking at day 30. This 5-day delay reflects the realistic progression from infection to infectiousness. Parameters: $\beta = 0.5$ day$^{-1}$, $\sigma = 0.5$ day$^{-1}$ (2-day latent period), $\gamma = 0.33$ day$^{-1}$ (3-day infectious period), giving $R_0 = 1.5$. The latent period creates a reservoir of future cases and delays epidemic peak timing, which is crucial for public health planning, contact tracing effectiveness, and intervention timing. This delay is particularly important for diseases like COVID-19, influenza, and measles where individuals are infected but not yet infectious.}}{42}{figure.9.2}\protected@file@percent }
\newlabel{fig:seir_model_dynamics}{{9.2}{42}{SEIR Model Dynamics. Comparison of SIR and SEIR models showing the impact of including an Exposed (E) compartment representing the latent period. The SIR model (red dashed line) shows infected individuals peaking at day 25, while the SEIR model introduces delay through the latent period: exposed individuals (orange) peak at day 20, followed by infected individuals (blue) peaking at day 30. This 5-day delay reflects the realistic progression from infection to infectiousness. Parameters: $\beta = 0.5$ day$^{-1}$, $\sigma = 0.5$ day$^{-1}$ (2-day latent period), $\gamma = 0.33$ day$^{-1}$ (3-day infectious period), giving $R_0 = 1.5$. The latent period creates a reservoir of future cases and delays epidemic peak timing, which is crucial for public health planning, contact tracing effectiveness, and intervention timing. This delay is particularly important for diseases like COVID-19, influenza, and measles where individuals are infected but not yet infectious}{figure.9.2}{}}
\@writefile{toc}{\contentsline {section}{\numberline {9.4}Modeling Vaccination and Control Strategies}{42}{section.9.4}\protected@file@percent }
\@writefile{toc}{\contentsline {chapter}{\numberline {10}Case Studies in Medical Modeling}{44}{chapter.10}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {10.1}Case Study: Designing an Antibiotic Dosing Regimen}{44}{section.10.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {10.2}Case Study: Simulating a Simple Disease Outbreak}{45}{section.10.2}\protected@file@percent }
\@writefile{toc}{\contentsline {chapter}{\numberline {11}Advanced Topics and Future Directions}{46}{chapter.11}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {11.1}Parameter Estimation and Model Fitting}{46}{section.11.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {11.2}Sensitivity Analysis}{46}{section.11.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {11.3}Beyond ODEs: Stochastic and Partial Differential Equations}{46}{section.11.3}\protected@file@percent }
\@writefile{toc}{\contentsline {chapter}{\numberline {12}Conclusion and Further Reading}{48}{chapter.12}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{toc}{\contentsline {section}{\numberline {12.1}Summary of Key Concepts}{48}{section.12.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {12.2}The Future of Mathematical Modeling in Medicine}{48}{section.12.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {12.3}Recommended References}{49}{section.12.3}\protected@file@percent }
\gdef \@abspage@last{51}
