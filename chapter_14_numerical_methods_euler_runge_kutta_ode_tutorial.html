<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 14: Numerical Methods - Euler's Method and Runge-Kutta Methods - ODE Tutorial</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            }
        };
    </script>
    <style>
        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid #007acc;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.375rem;
            overflow-x: auto;
        }
        .python-code {
            border-left-color: #3776ab;
        }
        .r-code {
            border-left-color: #276dc3;
        }
        .theorem-box {
            background-color: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        .example-box {
            background-color: #f0fdf4;
            border: 1px solid #22c55e;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        .warning-box {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        .chart-container {
            height: 400px;
            margin: 2rem 0;
        }
        .method-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        @media (max-width: 768px) {
            .method-comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900 leading-relaxed">
    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Header -->
        <header class="text-center mb-12">
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8 rounded-lg shadow-lg">
                <h1 class="text-4xl font-bold mb-4">
                    <i class="fas fa-calculator mr-3"></i>
                    Chapter 14: Numerical Methods
                </h1>
                <h2 class="text-2xl font-light mb-4">Euler's Method and Runge-Kutta Methods</h2>
                <p class="text-lg opacity-90">Comprehensive ODE Tutorial - Part 3, Chapter 4</p>
                <div class="flex justify-center items-center mt-6 space-x-8 text-sm">
                    <div class="flex items-center">
                        <i class="fab fa-python mr-2"></i>
                        <span>Python Implementation</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fab fa-r-project mr-2"></i>
                        <span>R Implementation</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-chart-line mr-2"></i>
                        <span>Interactive Visualizations</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Table of Contents -->
        <nav class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-list-ol mr-2"></i>Table of Contents
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <ul class="space-y-2">
                        <li><a href="#introduction" class="text-blue-600 hover:underline">1. Introduction to Numerical Methods</a></li>
                        <li><a href="#euler-method" class="text-blue-600 hover:underline">2. Euler's Method</a></li>
                        <li><a href="#improved-euler" class="text-blue-600 hover:underline">3. Improved Euler Method</a></li>
                        <li><a href="#runge-kutta" class="text-blue-600 hover:underline">4. Runge-Kutta Methods</a></li>
                        <li><a href="#error-analysis" class="text-blue-600 hover:underline">5. Error Analysis</a></li>
                    </ul>
                </div>
                <div>
                    <ul class="space-y-2">
                        <li><a href="#stability" class="text-blue-600 hover:underline">6. Stability and Step Size</a></li>
                        <li><a href="#higher-order" class="text-blue-600 hover:underline">7. Higher-Order to First-Order</a></li>
                        <li><a href="#systems" class="text-blue-600 hover:underline">8. Systems of ODEs</a></li>
                        <li><a href="#implementations" class="text-blue-600 hover:underline">9. Code Implementations</a></li>
                        <li><a href="#applications" class="text-blue-600 hover:underline">10. Applications and Guidelines</a></li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Section 1: Introduction -->
        <section id="introduction" class="mb-12">
            <div class="bg-white rounded-lg shadow-md p-8">
                <h2 class="text-3xl font-bold mb-6 text-gray-800">
                    <i class="fas fa-rocket mr-3 text-blue-600"></i>
                    1. Introduction to Numerical Methods
                </h2>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Why Numerical Methods?</h3>
                    <p class="text-lg mb-4">
                        While analytical solutions provide exact answers and deep insights, many differential equations 
                        encountered in engineering and science cannot be solved analytically. Numerical methods bridge 
                        this gap by providing approximate solutions with controllable accuracy.
                    </p>

                    <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
                        <h4 class="font-semibold text-blue-800 mb-2">When to Use Numerical Methods:</h4>
                        <ul class="list-disc pl-6 text-blue-700">
                            <li>Nonlinear differential equations</li>
                            <li>Variable coefficient equations</li>
                            <li>Complex forcing functions</li>
                            <li>Systems of coupled ODEs</li>
                            <li>Boundary value problems</li>
                            <li>Parameter studies and optimization</li>
                        </ul>
                    </div>
                </div>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Fundamental Concepts</h3>
                    
                    <div class="theorem-box">
                        <h4 class="font-semibold text-blue-800 mb-3">
                            <i class="fas fa-lightbulb mr-2"></i>Initial Value Problem (IVP)
                        </h4>
                        <p class="mb-3">Consider the first-order ODE:</p>
                        <div class="text-center bg-white p-4 rounded border">
                            $$\frac{dy}{dt} = f(t, y), \quad y(t_0) = y_0$$
                        </div>
                        <p class="mt-3">
                            where $f(t, y)$ is a given function, and we seek $y(t)$ for $t > t_0$.
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                        <div class="bg-gray-50 p-4 rounded">
                            <h4 class="font-semibold mb-2">
                                <i class="fas fa-check-circle text-green-600 mr-2"></i>Advantages of Numerical Methods
                            </h4>
                            <ul class="list-disc pl-6 text-sm">
                                <li>Handle any form of $f(t, y)$</li>
                                <li>Programmable and automatable</li>
                                <li>Controllable accuracy</li>
                                <li>Suitable for parameter studies</li>
                                <li>Handle discontinuities</li>
                            </ul>
                        </div>
                        <div class="bg-gray-50 p-4 rounded">
                            <h4 class="font-semibold mb-2">
                                <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>Limitations
                            </h4>
                            <ul class="list-disc pl-6 text-sm">
                                <li>Approximate solutions only</li>
                                <li>Computational cost</li>
                                <li>Potential numerical instability</li>
                                <li>Round-off error accumulation</li>
                                <li>Step size selection critical</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Error Types in Numerical Methods</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-red-50 border border-red-200 p-4 rounded">
                            <h4 class="font-semibold text-red-800 mb-2">Truncation Error</h4>
                            <p class="text-sm text-red-700">
                                Error from approximating infinite processes with finite ones. 
                                Reduced by smaller step sizes or higher-order methods.
                            </p>
                        </div>
                        <div class="bg-orange-50 border border-orange-200 p-4 rounded">
                            <h4 class="font-semibold text-orange-800 mb-2">Round-off Error</h4>
                            <p class="text-sm text-orange-700">
                                Error from finite precision arithmetic in computers. 
                                Can accumulate over many computational steps.
                            </p>
                        </div>
                        <div class="bg-purple-50 border border-purple-200 p-4 rounded">
                            <h4 class="font-semibold text-purple-800 mb-2">Total Error</h4>
                            <p class="text-sm text-purple-700">
                                Combination of truncation and round-off errors. 
                                Optimal step size minimizes total error.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 2: Euler's Method -->
        <section id="euler-method" class="mb-12">
            <div class="bg-white rounded-lg shadow-md p-8">
                <h2 class="text-3xl font-bold mb-6 text-gray-800">
                    <i class="fas fa-step-forward mr-3 text-green-600"></i>
                    2. Euler's Method
                </h2>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Derivation and Theory</h3>
                    
                    <p class="text-lg mb-4">
                        Euler's method is the simplest numerical method for solving ODEs, based on the definition 
                        of the derivative and linear approximation.
                    </p>

                    <div class="theorem-box">
                        <h4 class="font-semibold text-blue-800 mb-3">
                            <i class="fas fa-graduation-cap mr-2"></i>Derivation from Taylor Series
                        </h4>
                        <p class="mb-3">Starting with the Taylor expansion:</p>
                        <div class="text-center bg-white p-4 rounded border mb-3">
                            $$y(t + h) = y(t) + h y'(t) + \frac{h^2}{2!} y''(t) + \mathcal{O}(h^3)$$
                        </div>
                        <p class="mb-3">Since $y'(t) = f(t, y)$, truncating after the linear term gives:</p>
                        <div class="text-center bg-white p-4 rounded border">
                            $$y(t + h) \approx y(t) + h f(t, y(t))$$
                        </div>
                    </div>

                    <div class="example-box">
                        <h4 class="font-semibold text-green-800 mb-3">
                            <i class="fas fa-calculator mr-2"></i>Euler's Method Algorithm
                        </h4>
                        <p class="mb-3">Given: $\frac{dy}{dt} = f(t, y)$, $y(t_0) = y_0$, step size $h$</p>
                        <div class="bg-white p-4 rounded border">
                            <ol class="list-decimal pl-6">
                                <li>Set $t_1 = t_0 + h$</li>
                                <li>Compute $y_1 = y_0 + h f(t_0, y_0)$</li>
                                <li>Set $t_2 = t_1 + h$</li>
                                <li>Compute $y_2 = y_1 + h f(t_1, y_1)$</li>
                                <li>Continue: $y_{n+1} = y_n + h f(t_n, y_n)$</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Geometric Interpretation</h3>
                    
                    <div class="bg-blue-50 p-6 rounded-lg mb-6">
                        <p class="text-blue-800 mb-4">
                            <strong>Geometric View:</strong> Euler's method follows the tangent line at each point. 
                            The slope at $(t_n, y_n)$ is $f(t_n, y_n)$, so we move along this tangent for a distance $h$.
                        </p>
                        
                        <div class="chart-container">
                            <canvas id="eulerGeometryChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="method-comparison">
                    <div class="code-block python-code">
                        <h4 class="font-semibold mb-3">
                            <i class="fab fa-python mr-2"></i>Python Implementation
                        </h4>
                        <pre class="text-sm"><code>import numpy as np
import matplotlib.pyplot as plt

def euler_method(f, t0, y0, t_end, h):
    """
    Solve ODE dy/dt = f(t, y) using Euler's method
    
    Parameters:
    f: function, right-hand side of ODE
    t0: float, initial time
    y0: float, initial value
    t_end: float, final time
    h: float, step size
    
    Returns:
    t: array, time points
    y: array, solution values
    """
    # Number of steps
    n_steps = int((t_end - t0) / h) + 1
    
    # Initialize arrays
    t = np.linspace(t0, t_end, n_steps)
    y = np.zeros(n_steps)
    y[0] = y0
    
    # Euler's method loop
    for i in range(n_steps - 1):
        y[i + 1] = y[i] + h * f(t[i], y[i])
    
    return t, y

# Example: dy/dt = -2y + 1, y(0) = 0
def example_ode(t, y):
    return -2 * y + 1

# Solve numerically
t_num, y_num = euler_method(example_ode, 0, 0, 2, 0.1)

# Analytical solution for comparison
t_exact = np.linspace(0, 2, 1000)
y_exact = 0.5 * (1 - np.exp(-2 * t_exact))

# Plot results
plt.figure(figsize=(10, 6))
plt.plot(t_exact, y_exact, 'b-', label='Exact Solution', linewidth=2)
plt.plot(t_num, y_num, 'ro-', label='Euler Method (h=0.1)', markersize=4)
plt.xlabel('Time t')
plt.ylabel('y(t)')
plt.title('Euler Method vs Exact Solution')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# Error analysis
y_exact_at_points = 0.5 * (1 - np.exp(-2 * t_num))
error = np.abs(y_num - y_exact_at_points)
print(f"Maximum error: {np.max(error):.6f}")
print(f"Final error: {error[-1]:.6f}")</code></pre>
                    </div>

                    <div class="code-block r-code">
                        <h4 class="font-semibold mb-3">
                            <i class="fab fa-r-project mr-2"></i>R Implementation
                        </h4>
                        <pre class="text-sm"><code># Load required libraries
library(ggplot2)
library(dplyr)

euler_method <- function(f, t0, y0, t_end, h) {
  # Solve ODE dy/dt = f(t, y) using Euler's method
  #
  # Parameters:
  # f: function, right-hand side of ODE
  # t0: numeric, initial time
  # y0: numeric, initial value
  # t_end: numeric, final time
  # h: numeric, step size
  #
  # Returns:
  # data.frame with columns t and y
  
  # Number of steps
  n_steps <- as.integer((t_end - t0) / h) + 1
  
  # Initialize vectors
  t <- seq(t0, t_end, length.out = n_steps)
  y <- numeric(n_steps)
  y[1] <- y0
  
  # Euler's method loop
  for (i in 1:(n_steps - 1)) {
    y[i + 1] <- y[i] + h * f(t[i], y[i])
  }
  
  return(data.frame(t = t, y = y))
}

# Example: dy/dt = -2y + 1, y(0) = 0
example_ode <- function(t, y) {
  return(-2 * y + 1)
}

# Solve numerically
result_num <- euler_method(example_ode, 0, 0, 2, 0.1)

# Analytical solution for comparison
t_exact <- seq(0, 2, length.out = 1000)
y_exact <- 0.5 * (1 - exp(-2 * t_exact))
result_exact <- data.frame(t = t_exact, y = y_exact)

# Create comparison plot
p <- ggplot() +
  geom_line(data = result_exact, aes(x = t, y = y, color = "Exact Solution"), 
            size = 1.2) +
  geom_point(data = result_num, aes(x = t, y = y, color = "Euler Method"), 
             size = 2) +
  geom_line(data = result_num, aes(x = t, y = y, color = "Euler Method"), 
            linetype = "dashed") +
  labs(title = "Euler Method vs Exact Solution",
       x = "Time t", y = "y(t)",
       color = "Method") +
  theme_minimal() +
  theme(legend.position = "bottom")

print(p)

# Error analysis
y_exact_at_points <- 0.5 * (1 - exp(-2 * result_num$t))
error <- abs(result_num$y - y_exact_at_points)
cat("Maximum error:", max(error), "\n")
cat("Final error:", tail(error, 1), "\n")</code></pre>
                    </div>
                </div>

                <div class="warning-box">
                    <h4 class="font-semibold text-yellow-800 mb-3">
                        <i class="fas fa-exclamation-triangle mr-2"></i>Limitations of Euler's Method
                    </h4>
                    <ul class="list-disc pl-6 text-yellow-700">
                        <li><strong>Low Accuracy:</strong> Only first-order accurate (error proportional to $h$)</li>
                        <li><strong>Stability Issues:</strong> Can become unstable for certain types of equations</li>
                        <li><strong>Step Size Sensitivity:</strong> Requires small step sizes for acceptable accuracy</li>
                        <li><strong>Error Accumulation:</strong> Errors compound over many steps</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Section 3: Improved Euler Method -->
        <section id="improved-euler" class="mb-12">
            <div class="bg-white rounded-lg shadow-md p-8">
                <h2 class="text-3xl font-bold mb-6 text-gray-800">
                    <i class="fas fa-level-up-alt mr-3 text-indigo-600"></i>
                    3. Improved Euler Method (Heun's Method)
                </h2>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Theory and Motivation</h3>
                    
                    <p class="text-lg mb-4">
                        The Improved Euler method, also known as Heun's method, addresses some limitations of 
                        Euler's method by using an average of slopes at the beginning and end of each interval.
                    </p>

                    <div class="theorem-box">
                        <h4 class="font-semibold text-blue-800 mb-3">
                            <i class="fas fa-brain mr-2"></i>Improved Euler Algorithm
                        </h4>
                        <p class="mb-3">Given: $\frac{dy}{dt} = f(t, y)$, $y(t_0) = y_0$, step size $h$</p>
                        <div class="bg-white p-4 rounded border">
                            <ol class="list-decimal pl-6">
                                <li><strong>Predictor Step:</strong> $y_{n+1}^{(p)} = y_n + h f(t_n, y_n)$</li>
                                <li><strong>Corrector Step:</strong> $y_{n+1} = y_n + \frac{h}{2}[f(t_n, y_n) + f(t_{n+1}, y_{n+1}^{(p)})]$</li>
                            </ol>
                        </div>
                        <p class="mt-3 text-sm text-blue-700">
                            This is a <em>predictor-corrector</em> method that averages the slopes at both ends of the interval.
                        </p>
                    </div>

                    <div class="bg-indigo-50 p-6 rounded-lg">
                        <h4 class="font-semibold text-indigo-800 mb-3">Geometric Interpretation</h4>
                        <p class="text-indigo-700 mb-3">
                            Instead of following the tangent line at the starting point (Euler), we:
                        </p>
                        <ol class="list-decimal pl-6 text-indigo-700">
                            <li>Use Euler's method to predict where we might end up</li>
                            <li>Calculate the slope at that predicted endpoint</li>
                            <li>Average the starting and ending slopes</li>
                            <li>Use this average slope for the actual step</li>
                        </ol>
                    </div>
                </div>

                <div class="method-comparison">
                    <div class="code-block python-code">
                        <h4 class="font-semibold mb-3">
                            <i class="fab fa-python mr-2"></i>Python Implementation
                        </h4>
                        <pre class="text-sm"><code>def improved_euler_method(f, t0, y0, t_end, h):
    """
    Solve ODE dy/dt = f(t, y) using Improved Euler method
    
    Parameters:
    f: function, right-hand side of ODE
    t0: float, initial time
    y0: float, initial value
    t_end: float, final time
    h: float, step size
    
    Returns:
    t: array, time points
    y: array, solution values
    """
    # Number of steps
    n_steps = int((t_end - t0) / h) + 1
    
    # Initialize arrays
    t = np.linspace(t0, t_end, n_steps)
    y = np.zeros(n_steps)
    y[0] = y0
    
    # Improved Euler method loop
    for i in range(n_steps - 1):
        # Predictor step (Euler)
        y_pred = y[i] + h * f(t[i], y[i])
        
        # Corrector step (average of slopes)
        y[i + 1] = y[i] + (h / 2) * (f(t[i], y[i]) + f(t[i + 1], y_pred))
    
    return t, y

# Compare methods on the same problem
def compare_methods(f, t0, y0, t_end, h, analytical_solution=None):
    """Compare Euler and Improved Euler methods"""
    
    # Solve with both methods
    t_euler, y_euler = euler_method(f, t0, y0, t_end, h)
    t_improved, y_improved = improved_euler_method(f, t0, y0, t_end, h)
    
    # Plot comparison
    plt.figure(figsize=(12, 8))
    
    if analytical_solution is not None:
        t_exact = np.linspace(t0, t_end, 1000)
        y_exact = analytical_solution(t_exact)
        plt.plot(t_exact, y_exact, 'b-', label='Exact Solution', linewidth=2)
    
    plt.plot(t_euler, y_euler, 'ro-', label=f'Euler (h={h})', 
             markersize=4, alpha=0.7)
    plt.plot(t_improved, y_improved, 'gs-', label=f'Improved Euler (h={h})', 
             markersize=4, alpha=0.7)
    
    plt.xlabel('Time t')
    plt.ylabel('y(t)')
    plt.title('Method Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()
    
    # Error analysis if exact solution provided
    if analytical_solution is not None:
        y_exact_euler = analytical_solution(t_euler)
        y_exact_improved = analytical_solution(t_improved)
        
        error_euler = np.abs(y_euler - y_exact_euler)
        error_improved = np.abs(y_improved - y_exact_improved)
        
        print(f"Euler Method - Max Error: {np.max(error_euler):.6f}")
        print(f"Improved Euler - Max Error: {np.max(error_improved):.6f}")
        print(f"Improvement Factor: {np.max(error_euler) / np.max(error_improved):.2f}")

# Example usage
def analytical_solution(t):
    return 0.5 * (1 - np.exp(-2 * t))

compare_methods(example_ode, 0, 0, 2, 0.2, analytical_solution)</code></pre>
                    </div>

                    <div class="code-block r-code">
                        <h4 class="font-semibold mb-3">
                            <i class="fab fa-r-project mr-2"></i>R Implementation
                        </h4>
                        <pre class="text-sm"><code>improved_euler_method <- function(f, t0, y0, t_end, h) {
  # Solve ODE dy/dt = f(t, y) using Improved Euler method
  
  # Number of steps
  n_steps <- as.integer((t_end - t0) / h) + 1
  
  # Initialize vectors
  t <- seq(t0, t_end, length.out = n_steps)
  y <- numeric(n_steps)
  y[1] <- y0
  
  # Improved Euler method loop
  for (i in 1:(n_steps - 1)) {
    # Predictor step (Euler)
    y_pred <- y[i] + h * f(t[i], y[i])
    
    # Corrector step (average of slopes)
    y[i + 1] <- y[i] + (h / 2) * (f(t[i], y[i]) + f(t[i + 1], y_pred))
  }
  
  return(data.frame(t = t, y = y))
}

compare_methods <- function(f, t0, y0, t_end, h, analytical_solution = NULL) {
  # Compare Euler and Improved Euler methods
  
  # Solve with both methods
  result_euler <- euler_method(f, t0, y0, t_end, h)
  result_improved <- improved_euler_method(f, t0, y0, t_end, h)
  
  # Create comparison plot
  p <- ggplot() +
    theme_minimal()
  
  if (!is.null(analytical_solution)) {
    t_exact <- seq(t0, t_end, length.out = 1000)
    y_exact <- analytical_solution(t_exact)
    result_exact <- data.frame(t = t_exact, y = y_exact)
    
    p <- p + geom_line(data = result_exact, 
                       aes(x = t, y = y, color = "Exact Solution"), 
                       size = 1.2)
  }
  
  p <- p +
    geom_point(data = result_euler, 
               aes(x = t, y = y, color = paste("Euler (h =", h, ")")), 
               size = 2, alpha = 0.7) +
    geom_line(data = result_euler, 
              aes(x = t, y = y, color = paste("Euler (h =", h, ")")), 
              linetype = "dashed", alpha = 0.7) +
    geom_point(data = result_improved, 
               aes(x = t, y = y, color = paste("Improved Euler (h =", h, ")")), 
               size = 2, alpha = 0.7) +
    geom_line(data = result_improved, 
              aes(x = t, y = y, color = paste("Improved Euler (h =", h, ")")), 
              linetype = "dotted", alpha = 0.7) +
    labs(title = "Method Comparison",
         x = "Time t", y = "y(t)",
         color = "Method") +
    theme(legend.position = "bottom")
  
  print(p)
  
  # Error analysis if exact solution provided
  if (!is.null(analytical_solution)) {
    y_exact_euler <- analytical_solution(result_euler$t)
    y_exact_improved <- analytical_solution(result_improved$t)
    
    error_euler <- abs(result_euler$y - y_exact_euler)
    error_improved <- abs(result_improved$y - y_exact_improved)
    
    cat("Euler Method - Max Error:", max(error_euler), "\n")
    cat("Improved Euler - Max Error:", max(error_improved), "\n")
    cat("Improvement Factor:", max(error_euler) / max(error_improved), "\n")
  }
}

# Example usage
analytical_solution <- function(t) {
  return(0.5 * (1 - exp(-2 * t)))
}

compare_methods(example_ode, 0, 0, 2, 0.2, analytical_solution)</code></pre>
                    </div>
                </div>

                <div class="bg-green-50 border border-green-200 p-4 rounded mt-6">
                    <h4 class="font-semibold text-green-800 mb-2">
                        <i class="fas fa-chart-line mr-2"></i>Improved Euler Advantages
                    </h4>
                    <ul class="list-disc pl-6 text-green-700 text-sm">
                        <li><strong>Better Accuracy:</strong> Second-order accurate (error proportional to $h^2$)</li>
                        <li><strong>Self-Correcting:</strong> Uses information from both ends of interval</li>
                        <li><strong>Stability:</strong> Generally more stable than simple Euler</li>
                        <li><strong>Moderate Cost:</strong> Only requires two function evaluations per step</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Section 4: Runge-Kutta Methods -->
        <section id="runge-kutta" class="mb-12">
            <div class="bg-white rounded-lg shadow-md p-8">
                <h2 class="text-3xl font-bold mb-6 text-gray-800">
                    <i class="fas fa-rocket mr-3 text-purple-600"></i>
                    4. Runge-Kutta Methods (RK2 and RK4)
                </h2>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Runge-Kutta Philosophy</h3>
                    
                    <p class="text-lg mb-4">
                        Runge-Kutta methods extend the idea of using multiple slope evaluations to achieve 
                        higher-order accuracy. They systematically combine slope information at different 
                        points within each step interval.
                    </p>

                    <div class="bg-purple-50 p-6 rounded-lg mb-6">
                        <h4 class="font-semibold text-purple-800 mb-3">General Runge-Kutta Concept</h4>
                        <p class="text-purple-700 mb-3">
                            Instead of using just the slope at one or two points, RK methods evaluate the 
                            derivative function at multiple intermediate points and combine them with 
                            carefully chosen weights to achieve high accuracy.
                        </p>
                        <div class="text-center bg-white p-3 rounded border">
                            $$y_{n+1} = y_n + h \sum_{i=1}^{s} b_i k_i$$
                        </div>
                        <p class="text-purple-700 text-sm mt-2">
                            where $k_i$ are slope evaluations and $b_i$ are weight coefficients, $s$ is the number of stages.
                        </p>
                    </div>
                </div>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Second-Order Runge-Kutta (RK2)</h3>
                    
                    <div class="theorem-box">
                        <h4 class="font-semibold text-blue-800 mb-3">
                            <i class="fas fa-cogs mr-2"></i>RK2 Algorithm (Midpoint Method)
                        </h4>
                        <div class="bg-white p-4 rounded border">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p class="font-semibold mb-2">Step 1: Initial slope</p>
                                    $$k_1 = f(t_n, y_n)$$
                                </div>
                                <div>
                                    <p class="font-semibold mb-2">Step 2: Midpoint slope</p>
                                    $$k_2 = f\left(t_n + \frac{h}{2}, y_n + \frac{h}{2}k_1\right)$$
                                </div>
                            </div>
                            <div class="mt-4 text-center">
                                <p class="font-semibold mb-2">Final step:</p>
                                $$y_{n+1} = y_n + h k_2$$
                            </div>
                        </div>
                    </div>

                    <div class="bg-blue-50 p-4 rounded mb-4">
                        <p class="text-blue-800 text-sm">
                            <strong>Geometric Interpretation:</strong> RK2 evaluates the slope at the midpoint of the 
                            interval (using Euler to get there) and uses that slope for the entire step.
                        </p>
                    </div>
                </div>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Fourth-Order Runge-Kutta (RK4)</h3>
                    
                    <div class="theorem-box">
                        <h4 class="font-semibold text-blue-800 mb-3">
                            <i class="fas fa-star mr-2"></i>RK4 Algorithm (Classical Fourth-Order)
                        </h4>
                        <div class="bg-white p-4 rounded border">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p class="font-semibold mb-1">Step 1:</p>
                                    $$k_1 = f(t_n, y_n)$$
                                    <p class="font-semibold mb-1 mt-3">Step 2:</p>
                                    $$k_2 = f\left(t_n + \frac{h}{2}, y_n + \frac{h}{2}k_1\right)$$
                                </div>
                                <div>
                                    <p class="font-semibold mb-1">Step 3:</p>
                                    $$k_3 = f\left(t_n + \frac{h}{2}, y_n + \frac{h}{2}k_2\right)$$
                                    <p class="font-semibold mb-1 mt-3">Step 4:</p>
                                    $$k_4 = f(t_n + h, y_n + h k_3)$$
                                </div>
                            </div>
                            <div class="mt-4 text-center">
                                <p class="font-semibold mb-2">Weighted combination:</p>
                                $$y_{n+1} = y_n + \frac{h}{6}(k_1 + 2k_2 + 2k_3 + k_4)$$
                            </div>
                        </div>
                    </div>

                    <div class="bg-purple-50 p-4 rounded">
                        <h4 class="font-semibold text-purple-800 mb-2">RK4 Weight Interpretation</h4>
                        <ul class="list-disc pl-6 text-purple-700 text-sm">
                            <li>$k_1$: Slope at beginning of interval (weight = 1/6)</li>
                            <li>$k_2$: Slope at midpoint using $k_1$ (weight = 2/6)</li>
                            <li>$k_3$: Slope at midpoint using $k_2$ (weight = 2/6)</li>
                            <li>$k_4$: Slope at end using $k_3$ (weight = 1/6)</li>
                        </ul>
                        <p class="text-purple-700 text-sm mt-2">
                            This weighting scheme gives Simpson's rule-like accuracy.
                        </p>
                    </div>
                </div>

                <div class="method-comparison">
                    <div class="code-block python-code">
                        <h4 class="font-semibold mb-3">
                            <i class="fab fa-python mr-2"></i>Python Implementation
                        </h4>
                        <pre class="text-sm"><code>def rk2_method(f, t0, y0, t_end, h):
    """Second-order Runge-Kutta (Midpoint method)"""
    n_steps = int((t_end - t0) / h) + 1
    t = np.linspace(t0, t_end, n_steps)
    y = np.zeros(n_steps)
    y[0] = y0
    
    for i in range(n_steps - 1):
        k1 = f(t[i], y[i])
        k2 = f(t[i] + h/2, y[i] + (h/2) * k1)
        y[i + 1] = y[i] + h * k2
    
    return t, y

def rk4_method(f, t0, y0, t_end, h):
    """Fourth-order Runge-Kutta method"""
    n_steps = int((t_end - t0) / h) + 1
    t = np.linspace(t0, t_end, n_steps)
    y = np.zeros(n_steps)
    y[0] = y0
    
    for i in range(n_steps - 1):
        k1 = f(t[i], y[i])
        k2 = f(t[i] + h/2, y[i] + (h/2) * k1)
        k3 = f(t[i] + h/2, y[i] + (h/2) * k2)
        k4 = f(t[i] + h, y[i] + h * k3)
        
        y[i + 1] = y[i] + (h/6) * (k1 + 2*k2 + 2*k3 + k4)
    
    return t, y

def compare_all_methods(f, t0, y0, t_end, h, analytical_solution=None):
    """Compare all numerical methods"""
    
    # Solve with all methods
    methods = {
        'Euler': euler_method,
        'Improved Euler': improved_euler_method,
        'RK2': rk2_method,
        'RK4': rk4_method
    }
    
    results = {}
    for name, method in methods.items():
        results[name] = method(f, t0, y0, t_end, h)
    
    # Plot comparison
    plt.figure(figsize=(14, 10))
    
    # Solution comparison
    plt.subplot(2, 2, 1)
    if analytical_solution is not None:
        t_exact = np.linspace(t0, t_end, 1000)
        y_exact = analytical_solution(t_exact)
        plt.plot(t_exact, y_exact, 'k-', label='Exact', linewidth=2)
    
    colors = ['red', 'green', 'blue', 'purple']
    markers = ['o', 's', '^', 'D']
    
    for i, (name, (t, y)) in enumerate(results.items()):
        plt.plot(t, y, color=colors[i], marker=markers[i], 
                label=f'{name} (h={h})', markersize=4, alpha=0.7)
    
    plt.xlabel('Time t')
    plt.ylabel('y(t)')
    plt.title('Method Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Error analysis if exact solution available
    if analytical_solution is not None:
        plt.subplot(2, 2, 2)
        
        for i, (name, (t, y)) in enumerate(results.items()):
            y_exact_at_points = analytical_solution(t)
            error = np.abs(y - y_exact_at_points)
            plt.semilogy(t, error, color=colors[i], marker=markers[i], 
                        label=name, markersize=4, alpha=0.7)
        
        plt.xlabel('Time t')
        plt.ylabel('Absolute Error')
        plt.title('Error Comparison (Log Scale)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Error summary
        plt.subplot(2, 2, 3)
        method_names = list(results.keys())
        max_errors = []
        
        for name, (t, y) in results.items():
            y_exact_at_points = analytical_solution(t)
            error = np.abs(y - y_exact_at_points)
            max_errors.append(np.max(error))
        
        plt.bar(method_names, max_errors, color=colors[:len(method_names)], alpha=0.7)
        plt.ylabel('Maximum Error')
        plt.title('Maximum Error Comparison')
        plt.yscale('log')
        plt.xticks(rotation=45)
        
        # Print numerical results
        plt.subplot(2, 2, 4)
        plt.axis('off')
        
        error_text = "Error Analysis Summary:\n\n"
        for i, (name, (t, y)) in enumerate(results.items()):
            y_exact_at_points = analytical_solution(t)
            error = np.abs(y - y_exact_at_points)
            error_text += f"{name}:\n"
            error_text += f"  Max Error: {np.max(error):.2e}\n"
            error_text += f"  Final Error: {error[-1]:.2e}\n\n"
        
        plt.text(0.1, 0.9, error_text, transform=plt.gca().transAxes, 
                fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    plt.tight_layout()
    plt.show()

# Example usage with step size study
def step_size_study():
    """Study effect of step size on accuracy"""
    step_sizes = [0.4, 0.2, 0.1, 0.05]
    
    plt.figure(figsize=(12, 8))
    
    for i, h in enumerate(step_sizes):
        t_rk4, y_rk4 = rk4_method(example_ode, 0, 0, 2, h)
        
        # Exact solution
        y_exact = analytical_solution(t_rk4)
        error = np.abs(y_rk4 - y_exact)
        
        plt.subplot(2, 2, i+1)
        plt.semilogy(t_rk4, error, 'r-o', markersize=4)
        plt.xlabel('Time t')
        plt.ylabel('Absolute Error')
        plt.title(f'RK4 Error (h = {h})')
        plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# Run comprehensive comparison
compare_all_methods(example_ode, 0, 0, 2, 0.2, analytical_solution)
step_size_study()</code></pre>
                    </div>

                    <div class="code-block r-code">
                        <h4 class="font-semibold mb-3">
                            <i class="fab fa-r-project mr-2"></i>R Implementation
                        </h4>
                        <pre class="text-sm"><code>rk2_method <- function(f, t0, y0, t_end, h) {
  # Second-order Runge-Kutta (Midpoint method)
  n_steps <- as.integer((t_end - t0) / h) + 1
  t <- seq(t0, t_end, length.out = n_steps)
  y <- numeric(n_steps)
  y[1] <- y0
  
  for (i in 1:(n_steps - 1)) {
    k1 <- f(t[i], y[i])
    k2 <- f(t[i] + h/2, y[i] + (h/2) * k1)
    y[i + 1] <- y[i] + h * k2
  }
  
  return(data.frame(t = t, y = y))
}

rk4_method <- function(f, t0, y0, t_end, h) {
  # Fourth-order Runge-Kutta method
  n_steps <- as.integer((t_end - t0) / h) + 1
  t <- seq(t0, t_end, length.out = n_steps)
  y <- numeric(n_steps)
  y[1] <- y0
  
  for (i in 1:(n_steps - 1)) {
    k1 <- f(t[i], y[i])
    k2 <- f(t[i] + h/2, y[i] + (h/2) * k1)
    k3 <- f(t[i] + h/2, y[i] + (h/2) * k2)
    k4 <- f(t[i] + h, y[i] + h * k3)
    
    y[i + 1] <- y[i] + (h/6) * (k1 + 2*k2 + 2*k3 + k4)
  }
  
  return(data.frame(t = t, y = y))
}

compare_all_methods <- function(f, t0, y0, t_end, h, analytical_solution = NULL) {
  # Compare all numerical methods
  
  # Solve with all methods
  result_euler <- euler_method(f, t0, y0, t_end, h)
  result_improved <- improved_euler_method(f, t0, y0, t_end, h)
  result_rk2 <- rk2_method(f, t0, y0, t_end, h)
  result_rk4 <- rk4_method(f, t0, y0, t_end, h)
  
  # Combine results for plotting
  all_results <- rbind(
    data.frame(result_euler, method = "Euler"),
    data.frame(result_improved, method = "Improved Euler"),
    data.frame(result_rk2, method = "RK2"),
    data.frame(result_rk4, method = "RK4")
  )
  
  # Create comparison plot
  p1 <- ggplot(all_results, aes(x = t, y = y, color = method)) +
    geom_point(size = 2, alpha = 0.7) +
    geom_line(alpha = 0.7) +
    theme_minimal() +
    labs(title = "Method Comparison",
         x = "Time t", y = "y(t)",
         color = "Method")
  
  if (!is.null(analytical_solution)) {
    t_exact <- seq(t0, t_end, length.out = 1000)
    y_exact <- analytical_solution(t_exact)
    exact_data <- data.frame(t = t_exact, y = y_exact)
    
    p1 <- p1 + geom_line(data = exact_data, aes(x = t, y = y), 
                         color = "black", size = 1.2, 
                         inherit.aes = FALSE) +
      annotate("text", x = max(t_exact) * 0.7, y = max(y_exact) * 0.9, 
               label = "Exact Solution", color = "black", size = 4)
  }
  
  print(p1)
  
  # Error analysis if exact solution available
  if (!is.null(analytical_solution)) {
    # Calculate errors
    methods_list <- list(
      "Euler" = result_euler,
      "Improved Euler" = result_improved,
      "RK2" = result_rk2,
      "RK4" = result_rk4
    )
    
    error_data <- data.frame()
    error_summary <- data.frame(
      Method = character(),
      Max_Error = numeric(),
      Final_Error = numeric(),
      stringsAsFactors = FALSE
    )
    
    for (method_name in names(methods_list)) {
      result <- methods_list[[method_name]]
      y_exact_at_points <- analytical_solution(result$t)
      error <- abs(result$y - y_exact_at_points)
      
      error_data <- rbind(error_data, 
                         data.frame(t = result$t, error = error, 
                                   method = method_name))
      
      error_summary <- rbind(error_summary,
                            data.frame(Method = method_name,
                                      Max_Error = max(error),
                                      Final_Error = tail(error, 1)))
    }
    
    # Error plot
    p2 <- ggplot(error_data, aes(x = t, y = error, color = method)) +
      geom_point(size = 2, alpha = 0.7) +
      geom_line(alpha = 0.7) +
      scale_y_log10() +
      theme_minimal() +
      labs(title = "Error Comparison (Log Scale)",
           x = "Time t", y = "Absolute Error",
           color = "Method")
    
    print(p2)
    
    # Print error summary
    cat("\nError Analysis Summary:\n")
    cat("=======================\n")
    for (i in 1:nrow(error_summary)) {
      cat(sprintf("%-15s Max Error: %.2e, Final Error: %.2e\n", 
                  error_summary$Method[i], 
                  error_summary$Max_Error[i], 
                  error_summary$Final_Error[i]))
    }
  }
}

# Step size study function
step_size_study <- function() {
  step_sizes <- c(0.4, 0.2, 0.1, 0.05)
  
  error_data <- data.frame()
  
  for (h in step_sizes) {
    result_rk4 <- rk4_method(example_ode, 0, 0, 2, h)
    y_exact <- analytical_solution(result_rk4$t)
    error <- abs(result_rk4$y - y_exact)
    
    error_data <- rbind(error_data,
                       data.frame(t = result_rk4$t, error = error, 
                                 h = paste("h =", h)))
  }
  
  p <- ggplot(error_data, aes(x = t, y = error, color = h)) +
    geom_point(size = 2, alpha = 0.7) +
    geom_line(alpha = 0.7) +
    scale_y_log10() +
    facet_wrap(~ h, scales = "free_y") +
    theme_minimal() +
    labs(title = "RK4 Error vs Step Size",
         x = "Time t", y = "Absolute Error",
         color = "Step Size") +
    theme(legend.position = "none")
  
  print(p)
}

# Run comprehensive comparison
compare_all_methods(example_ode, 0, 0, 2, 0.2, analytical_solution)
step_size_study()</code></pre>
                    </div>
                </div>

                <div class="chart-container mt-8">
                    <canvas id="rkComparisonChart"></canvas>
                </div>
            </div>
        </section>

        <!-- Section 5: Error Analysis -->
        <section id="error-analysis" class="mb-12">
            <div class="bg-white rounded-lg shadow-md p-8">
                <h2 class="text-3xl font-bold mb-6 text-gray-800">
                    <i class="fas fa-magnifying-glass-chart mr-3 text-red-600"></i>
                    5. Error Analysis and Convergence
                </h2>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Types of Numerical Errors</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="theorem-box">
                            <h4 class="font-semibold text-blue-800 mb-3">
                                <i class="fas fa-cut mr-2"></i>Local Truncation Error
                            </h4>
                            <p class="mb-3">Error introduced in a single step, assuming all previous values are exact.</p>
                            <div class="bg-white p-3 rounded border text-center">
                                $$\text{LTE} = y(t_{n+1}) - y_{n+1}$$
                            </div>
                            <p class="text-sm text-blue-700 mt-2">
                                For RK4: LTE = $\mathcal{O}(h^5)$
                            </p>
                        </div>

                        <div class="theorem-box">
                            <h4 class="font-semibold text-blue-800 mb-3">
                                <i class="fas fa-globe mr-2"></i>Global Truncation Error
                            </h4>
                            <p class="mb-3">Accumulated error over the entire solution interval.</p>
                            <div class="bg-white p-3 rounded border text-center">
                                $$\text{GTE} = y(t_n) - y_n$$
                            </div>
                            <p class="text-sm text-blue-700 mt-2">
                                For RK4: GTE = $\mathcal{O}(h^4)$
                            </p>
                        </div>
                    </div>
                </div>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Order of Accuracy</h3>
                    
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="bg-gray-200">
                                        <th class="p-3 text-left">Method</th>
                                        <th class="p-3 text-center">Local Truncation Error</th>
                                        <th class="p-3 text-center">Global Truncation Error</th>
                                        <th class="p-3 text-center">Function Evaluations/Step</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="border-b">
                                        <td class="p-3 font-semibold">Euler</td>
                                        <td class="p-3 text-center">$\mathcal{O}(h^2)$</td>
                                        <td class="p-3 text-center">$\mathcal{O}(h)$</td>
                                        <td class="p-3 text-center">1</td>
                                    </tr>
                                    <tr class="border-b bg-gray-50">
                                        <td class="p-3 font-semibold">Improved Euler</td>
                                        <td class="p-3 text-center">$\mathcal{O}(h^3)$</td>
                                        <td class="p-3 text-center">$\mathcal{O}(h^2)$</td>
                                        <td class="p-3 text-center">2</td>
                                    </tr>
                                    <tr class="border-b">
                                        <td class="p-3 font-semibold">RK2</td>
                                        <td class="p-3 text-center">$\mathcal{O}(h^3)$</td>
                                        <td class="p-3 text-center">$\mathcal{O}(h^2)$</td>
                                        <td class="p-3 text-center">2</td>
                                    </tr>
                                    <tr class="border-b bg-gray-50">
                                        <td class="p-3 font-semibold">RK4</td>
                                        <td class="p-3 text-center">$\mathcal{O}(h^5)$</td>
                                        <td class="p-3 text-center">$\mathcal{O}(h^4)$</td>
                                        <td class="p-3 text-center">4</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Convergence Analysis</h3>
                    
                    <div class="method-comparison">
                        <div class="code-block python-code">
                            <h4 class="font-semibold mb-3">
                                <i class="fab fa-python mr-2"></i>Convergence Study
                            </h4>
                            <pre class="text-sm"><code>def convergence_study(f, t0, y0, t_end, analytical_solution, methods):
    """Study convergence rates of different methods"""
    
    # Range of step sizes
    step_sizes = np.array([0.4, 0.2, 0.1, 0.05, 0.025, 0.0125])
    
    # Store errors for each method
    errors = {name: [] for name in methods.keys()}
    
    # Calculate errors for each step size
    for h in step_sizes:
        for method_name, method_func in methods.items():
            t, y = method_func(f, t0, y0, t_end, h)
            y_exact = analytical_solution(t)
            max_error = np.max(np.abs(y - y_exact))
            errors[method_name].append(max_error)
    
    # Plot convergence
    plt.figure(figsize=(12, 8))
    
    # Log-log plot to see convergence rates
    plt.subplot(1, 2, 1)
    colors = ['red', 'green', 'blue', 'purple']
    
    for i, (method_name, error_list) in enumerate(errors.items()):
        plt.loglog(step_sizes, error_list, 'o-', color=colors[i], 
                  label=method_name, linewidth=2, markersize=6)
    
    # Add theoretical convergence lines
    plt.loglog(step_sizes, step_sizes, 'k--', alpha=0.5, label='$O(h)$')
    plt.loglog(step_sizes, step_sizes**2, 'k:', alpha=0.5, label='$O(h^2)$')
    plt.loglog(step_sizes, step_sizes**4, 'k-.', alpha=0.5, label='$O(h^4)$')
    
    plt.xlabel('Step Size h')
    plt.ylabel('Maximum Error')
    plt.title('Convergence Study (Log-Log Plot)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Calculate and display convergence rates
    plt.subplot(1, 2, 2)
    
    convergence_rates = {}
    for method_name, error_list in errors.items():
        # Calculate convergence rate using consecutive points
        rates = []
        for i in range(len(step_sizes) - 1):
            rate = np.log(error_list[i+1] / error_list[i]) / np.log(step_sizes[i+1] / step_sizes[i])
            rates.append(rate)
        convergence_rates[method_name] = np.mean(rates)
    
    # Bar plot of convergence rates
    methods_list = list(convergence_rates.keys())
    rates_list = list(convergence_rates.values())
    theoretical_rates = [1, 2, 2, 4]  # Theoretical rates for each method
    
    x = np.arange(len(methods_list))
    width = 0.35
    
    plt.bar(x - width/2, rates_list, width, label='Observed', color='lightblue', alpha=0.7)
    plt.bar(x + width/2, theoretical_rates, width, label='Theoretical', color='orange', alpha=0.7)
    
    plt.xlabel('Method')
    plt.ylabel('Convergence Rate')
    plt.title('Convergence Rates Comparison')
    plt.xticks(x, methods_list, rotation=45)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Print numerical results
    print("Convergence Analysis Results:")
    print("=" * 40)
    for method_name, rate in convergence_rates.items():
        theoretical = [1, 2, 2, 4][list(methods.keys()).index(method_name)]
        print(f"{method_name:15s}: Observed = {rate:.2f}, Theoretical = {theoretical}")

# Define methods for comparison
methods_for_convergence = {
    'Euler': euler_method,
    'Improved Euler': improved_euler_method,
    'RK2': rk2_method,
    'RK4': rk4_method
}

# Run convergence study
convergence_study(example_ode, 0, 0, 1, analytical_solution, methods_for_convergence)</code></pre>
                        </div>

                        <div class="code-block r-code">
                            <h4 class="font-semibold mb-3">
                                <i class="fab fa-r-project mr-2"></i>R Convergence Analysis
                            </h4>
                            <pre class="text-sm"><code>convergence_study <- function(f, t0, y0, t_end, analytical_solution) {
  # Study convergence rates of different methods
  
  # Range of step sizes
  step_sizes <- c(0.4, 0.2, 0.1, 0.05, 0.025, 0.0125)
  
  # Methods to compare
  methods <- list(
    "Euler" = euler_method,
    "Improved Euler" = improved_euler_method,
    "RK2" = rk2_method,
    "RK4" = rk4_method
  )
  
  # Calculate errors for each step size
  error_data <- data.frame()
  
  for (h in step_sizes) {
    for (method_name in names(methods)) {
      method_func <- methods[[method_name]]
      result <- method_func(f, t0, y0, t_end, h)
      y_exact <- analytical_solution(result$t)
      max_error <- max(abs(result$y - y_exact))
      
      error_data <- rbind(error_data,
                         data.frame(h = h, error = max_error, 
                                   method = method_name))
    }
  }
  
  # Convergence plot (log-log)
  p1 <- ggplot(error_data, aes(x = h, y = error, color = method)) +
    geom_point(size = 3, alpha = 0.7) +
    geom_line(size = 1, alpha = 0.7) +
    scale_x_log10() +
    scale_y_log10() +
    # Add theoretical convergence lines
    geom_abline(intercept = 0, slope = 1, linetype = "dashed", 
                color = "black", alpha = 0.5, size = 0.5) +
    geom_abline(intercept = -1, slope = 2, linetype = "dotted", 
                color = "black", alpha = 0.5, size = 0.5) +
    geom_abline(intercept = -3, slope = 4, linetype = "dotdash", 
                color = "black", alpha = 0.5, size = 0.5) +
    annotate("text", x = 0.3, y = 0.3, label = "O(h)", 
             angle = 45, color = "black", size = 3) +
    annotate("text", x = 0.15, y = 0.02, label = "O(h²)", 
             angle = 60, color = "black", size = 3) +
    annotate("text", x = 0.08, y = 0.0001, label = "O(h⁴)", 
             angle = 70, color = "black", size = 3) +
    theme_minimal() +
    labs(title = "Convergence Study (Log-Log Plot)",
         x = "Step Size h", y = "Maximum Error",
         color = "Method")
  
  print(p1)
  
  # Calculate convergence rates
  convergence_rates <- data.frame()
  
  for (method_name in unique(error_data$method)) {
    method_data <- error_data[error_data$method == method_name, ]
    method_data <- method_data[order(method_data$h, decreasing = TRUE), ]
    
    rates <- numeric()
    for (i in 1:(nrow(method_data) - 1)) {
      rate <- log(method_data$error[i+1] / method_data$error[i]) / 
              log(method_data$h[i+1] / method_data$h[i])
      rates <- c(rates, rate)
    }
    
    avg_rate <- mean(rates)
    convergence_rates <- rbind(convergence_rates,
                              data.frame(method = method_name, 
                                        observed = avg_rate,
                                        theoretical = case_when(
                                          method_name == "Euler" ~ 1,
                                          method_name == "Improved Euler" ~ 2,
                                          method_name == "RK2" ~ 2,
                                          method_name == "RK4" ~ 4
                                        )))
  }
  
  # Convergence rates comparison
  rates_long <- convergence_rates %>%
    pivot_longer(cols = c(observed, theoretical), 
                names_to = "type", values_to = "rate")
  
  p2 <- ggplot(rates_long, aes(x = method, y = rate, fill = type)) +
    geom_bar(stat = "identity", position = "dodge", alpha = 0.7) +
    theme_minimal() +
    labs(title = "Convergence Rates Comparison",
         x = "Method", y = "Convergence Rate",
         fill = "Type") +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
  
  print(p2)
  
  # Print numerical results
  cat("\nConvergence Analysis Results:\n")
  cat("=============================\n")
  for (i in 1:nrow(convergence_rates)) {
    cat(sprintf("%-15s: Observed = %.2f, Theoretical = %.0f\n", 
                convergence_rates$method[i], 
                convergence_rates$observed[i], 
                convergence_rates$theoretical[i]))
  }
  
  return(convergence_rates)
}

# Run convergence study
results <- convergence_study(example_ode, 0, 0, 1, analytical_solution)</code></pre>
                        </div>
                    </div>
                </div>

                <div class="warning-box">
                    <h4 class="font-semibold text-yellow-800 mb-3">
                        <i class="fas fa-exclamation-triangle mr-2"></i>Important Error Considerations
                    </h4>
                    <ul class="list-disc pl-6 text-yellow-700">
                        <li><strong>Step Size Trade-off:</strong> Smaller steps reduce truncation error but increase round-off error</li>
                        <li><strong>Optimal Step Size:</strong> Balance between truncation and round-off errors</li>
                        <li><strong>Problem Sensitivity:</strong> Some problems are more sensitive to errors than others</li>
                        <li><strong>Error Propagation:</strong> Errors can grow exponentially in unstable problems</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Section 6: Stability and Step Size -->
        <section id="stability" class="mb-12">
            <div class="bg-white rounded-lg shadow-md p-8">
                <h2 class="text-3xl font-bold mb-6 text-gray-800">
                    <i class="fas fa-balance-scale mr-3 text-orange-600"></i>
                    6. Stability and Step Size Selection
                </h2>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Numerical Stability</h3>
                    
                    <p class="text-lg mb-4">
                        Numerical stability refers to the behavior of numerical errors as the computation progresses. 
                        A stable method produces bounded errors, while an unstable method leads to error growth.
                    </p>

                    <div class="theorem-box">
                        <h4 class="font-semibold text-blue-800 mb-3">
                            <i class="fas fa-cog mr-2"></i>Linear Stability Analysis
                        </h4>
                        <p class="mb-3">Consider the test equation: $y' = \lambda y$ where $\lambda < 0$ (stable analytical solution)</p>
                        <div class="bg-white p-4 rounded border">
                            <p class="mb-2"><strong>Euler Method:</strong> $y_{n+1} = (1 + h\lambda) y_n$</p>
                            <p class="mb-2"><strong>Stability Condition:</strong> $|1 + h\lambda| < 1$</p>
                            <p><strong>Step Size Limit:</strong> $h < \frac{2}{|\lambda|}$</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                        <div class="bg-red-50 border border-red-200 p-4 rounded">
                            <h4 class="font-semibold text-red-800 mb-2">
                                <i class="fas fa-exclamation-circle mr-2"></i>Unstable Behavior
                            </h4>
                            <ul class="list-disc pl-6 text-red-700 text-sm">
                                <li>Solutions grow exponentially</li>
                                <li>Oscillatory behavior</li>
                                <li>Complete loss of accuracy</li>
                                <li>Method failure</li>
                            </ul>
                        </div>
                        
                        <div class="bg-green-50 border border-green-200 p-4 rounded">
                            <h4 class="font-semibold text-green-800 mb-2">
                                <i class="fas fa-check-circle mr-2"></i>Stable Behavior
                            </h4>
                            <ul class="list-disc pl-6 text-green-700 text-sm">
                                <li>Bounded error growth</li>
                                <li>Predictable accuracy</li>
                                <li>Reliable convergence</li>
                                <li>Method success</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Step Size Selection Strategies</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-blue-50 border border-blue-200 p-4 rounded">
                            <h4 class="font-semibold text-blue-800 mb-2">Fixed Step Size</h4>
                            <p class="text-sm text-blue-700">
                                Use constant step size throughout integration. Simple but may be inefficient 
                                or inaccurate in regions of rapid change.
                            </p>
                        </div>
                        
                        <div class="bg-purple-50 border border-purple-200 p-4 rounded">
                            <h4 class="font-semibold text-purple-800 mb-2">Adaptive Step Size</h4>
                            <p class="text-sm text-purple-700">
                                Adjust step size based on local error estimates. More efficient and 
                                maintains accuracy throughout integration.
                            </p>
                        </div>
                        
                        <div class="bg-green-50 border border-green-200 p-4 rounded">
                            <h4 class="font-semibold text-green-800 mb-2">Embedded Methods</h4>
                            <p class="text-sm text-green-700">
                                Use methods with built-in error estimation (e.g., Runge-Kutta-Fehlberg) 
                                for automatic step size control.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="method-comparison">
                    <div class="code-block python-code">
                        <h4 class="font-semibold mb-3">
                            <i class="fab fa-python mr-2"></i>Stability Analysis
                        </h4>
                        <pre class="text-sm"><code>def stability_analysis():
    """Demonstrate stability issues with different step sizes"""
    
    # Test equation: y' = -10y, y(0) = 1
    # Analytical solution: y(t) = exp(-10t)
    lambda_val = -10
    
    def test_equation(t, y):
        return lambda_val * y
    
    def analytical_test(t):
        return np.exp(lambda_val * t)
    
    # Different step sizes to test stability
    step_sizes = [0.1, 0.2, 0.25, 0.3]  # 0.25 is near stability limit
    t_end = 2.0
    
    plt.figure(figsize=(15, 10))
    
    for i, h in enumerate(step_sizes):
        plt.subplot(2, 2, i+1)
        
        # Exact solution
        t_exact = np.linspace(0, t_end, 1000)
        y_exact = analytical_test(t_exact)
        plt.plot(t_exact, y_exact, 'k-', label='Exact', linewidth=2)
        
        # Euler method
        t_euler, y_euler = euler_method(test_equation, 0, 1, t_end, h)
        plt.plot(t_euler, y_euler, 'ro-', label=f'Euler (h={h})', markersize=4)
        
        # RK4 method for comparison
        t_rk4, y_rk4 = rk4_method(test_equation, 0, 1, t_end, h)
        plt.plot(t_rk4, y_rk4, 'bs-', label=f'RK4 (h={h})', markersize=4)
        
        plt.xlabel('Time t')
        plt.ylabel('y(t)')
        plt.title(f'Stability Test: h = {h}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Set appropriate y-limits
        if h >= 0.25:  # Potentially unstable
            plt.ylim(-2, 2)
        else:
            plt.ylim(0, 1.1)
    
    plt.tight_layout()
    plt.show()
    
    # Stability limit analysis
    print("Stability Analysis for y' = -10y:")
    print("=" * 35)
    print(f"Theoretical stability limit for Euler: h < {2/abs(lambda_val)}")
    
    for h in step_sizes:
        stability_factor = 1 + h * lambda_val
        if abs(stability_factor) < 1:
            status = "STABLE"
        else:
            status = "UNSTABLE"
        print(f"h = {h:4.2f}: |1 + hλ| = {abs(stability_factor):6.3f} -> {status}")

def adaptive_step_size_demo():
    """Demonstrate adaptive step size control"""
    
    def stiff_equation(t, y):
        """A stiff equation that changes behavior rapidly"""
        return -100 * (y - np.cos(t)) - np.sin(t)
    
    def stiff_analytical(t):
        return np.cos(t)
    
    # Fixed step size solution
    h_fixed = 0.01
    t_fixed, y_fixed = rk4_method(stiff_equation, 0, 1, 2, h_fixed)
    
    # Simulate adaptive step size (simplified)
    def adaptive_rk4(f, t0, y0, t_end, h_init, tol=1e-6):
        """Simplified adaptive RK4 with step size doubling/halving"""
        t = [t0]
        y = [y0]
        h = h_init
        t_current = t0
        y_current = y0
        
        while t_current < t_end:
            # Take one step with h
            _, y1_h = rk4_method(f, t_current, y_current, t_current + h, h)
            
            # Take two steps with h/2
            _, y_temp = rk4_method(f, t_current, y_current, t_current + h/2, h/2)
            _, y2_h2 = rk4_method(f, t_current + h/2, y_temp[-1], t_current + h, h/2)
            
            # Estimate error
            error = abs(y1_h[-1] - y2_h2[-1]) / 15  # Richardson extrapolation
            
            if error < tol:
                # Accept step
                t_current += h
                y_current = y1_h[-1]
                t.append(t_current)
                y.append(y_current)
                
                # Maybe increase step size
                if error < tol / 10:
                    h = min(h * 1.5, 0.1)
            else:
                # Reject step, decrease step size
                h = h / 2
                if h < 1e-8:
                    print("Step size too small!")
                    break
        
        return np.array(t), np.array(y)
    
    # Adaptive solution
    t_adaptive, y_adaptive = adaptive_rk4(stiff_equation, 0, 1, 2, 0.1)
    
    # Plot comparison
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 1, 1)
    t_exact = np.linspace(0, 2, 1000)
    y_exact = stiff_analytical(t_exact)
    plt.plot(t_exact, y_exact, 'k-', label='Exact', linewidth=2)
    plt.plot(t_fixed, y_fixed, 'r.-', label=f'Fixed (h={h_fixed})', markersize=3)
    plt.plot(t_adaptive, y_adaptive, 'bo-', label='Adaptive', markersize=4)
    plt.xlabel('Time t')
    plt.ylabel('y(t)')
    plt.title('Fixed vs Adaptive Step Size')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 1, 2)
    # Show step sizes used in adaptive method
    step_sizes_used = np.diff(t_adaptive)
    plt.plot(t_adaptive[1:], step_sizes_used, 'go-', markersize=4)
    plt.axhline(y=h_fixed, color='red', linestyle='--', label=f'Fixed h = {h_fixed}')
    plt.xlabel('Time t')
    plt.ylabel('Step Size h')
    plt.title('Adaptive Step Size Evolution')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    print(f"Fixed step size: {len(t_fixed)} steps")
    print(f"Adaptive step size: {len(t_adaptive)} steps")
    print(f"Efficiency gain: {len(t_fixed) / len(t_adaptive):.2f}x")

# Run stability analysis
stability_analysis()
adaptive_step_size_demo()</code></pre>
                    </div>

                    <div class="code-block r-code">
                        <h4 class="font-semibold mb-3">
                            <i class="fab fa-r-project mr-2"></i>R Stability Analysis
                        </h4>
                        <pre class="text-sm"><code>stability_analysis <- function() {
  # Demonstrate stability issues with different step sizes
  
  # Test equation: y' = -10y, y(0) = 1
  lambda_val <- -10
  
  test_equation <- function(t, y) {
    return(lambda_val * y)
  }
  
  analytical_test <- function(t) {
    return(exp(lambda_val * t))
  }
  
  # Different step sizes to test stability
  step_sizes <- c(0.1, 0.2, 0.25, 0.3)
  t_end <- 2.0
  
  # Collect all results
  all_results <- data.frame()
  
  for (h in step_sizes) {
    # Exact solution
    t_exact <- seq(0, t_end, length.out = 1000)
    y_exact <- analytical_test(t_exact)
    exact_data <- data.frame(t = t_exact, y = y_exact, 
                            method = "Exact", h = h)
    
    # Euler method
    result_euler <- euler_method(test_equation, 0, 1, t_end, h)
    euler_data <- data.frame(result_euler, method = "Euler", h = h)
    
    # RK4 method
    result_rk4 <- rk4_method(test_equation, 0, 1, t_end, h)
    rk4_data <- data.frame(result_rk4, method = "RK4", h = h)
    
    all_results <- rbind(all_results, exact_data, euler_data, rk4_data)
  }
  
  # Create stability plots
  p <- ggplot(all_results, aes(x = t, y = y, color = method)) +
    geom_line(data = filter(all_results, method == "Exact"), 
              size = 1.5, alpha = 0.8) +
    geom_point(data = filter(all_results, method != "Exact"), 
               size = 2, alpha = 0.7) +
    geom_line(data = filter(all_results, method != "Exact"), 
              size = 1, alpha = 0.7) +
    facet_wrap(~ paste("h =", h), scales = "free_y") +
    theme_minimal() +
    labs(title = "Stability Analysis: y' = -10y",
         x = "Time t", y = "y(t)",
         color = "Method") +
    theme(legend.position = "bottom")
  
  print(p)
  
  # Stability analysis summary
  cat("Stability Analysis for y' = -10y:\n")
  cat("==================================\n")
  cat("Theoretical stability limit for Euler: h <", 2/abs(lambda_val), "\n\n")
  
  for (h in step_sizes) {
    stability_factor <- 1 + h * lambda_val
    status <- ifelse(abs(stability_factor) < 1, "STABLE", "UNSTABLE")
    cat(sprintf("h = %4.2f: |1 + hλ| = %6.3f -> %s\n", 
                h, abs(stability_factor), status))
  }
}

# Simple adaptive step size demonstration
adaptive_demo <- function() {
  # Equation that benefits from adaptive stepping
  oscillatory_eq <- function(t, y) {
    return(-y + sin(10 * t))
  }
  
  # Fixed step size solutions with different h values
  step_sizes <- c(0.1, 0.05, 0.01)
  t_end <- 2
  
  results_data <- data.frame()
  
  for (h in step_sizes) {
    result <- rk4_method(oscillatory_eq, 0, 1, t_end, h)
    result$method <- paste("Fixed h =", h)
    result$n_steps <- nrow(result)
    results_data <- rbind(results_data, result)
  }
  
  # Plot comparison
  p1 <- ggplot(results_data, aes(x = t, y = y, color = method)) +
    geom_line(size = 1, alpha = 0.8) +
    geom_point(size = 1.5, alpha = 0.6) +
    theme_minimal() +
    labs(title = "Step Size Comparison for Oscillatory Equation",
         x = "Time t", y = "y(t)",
         color = "Method")
  
  print(p1)
  
  # Step count comparison
  step_counts <- results_data %>%
    group_by(method) %>%
    summarise(n_steps = first(n_steps), .groups = 'drop')
  
  p2 <- ggplot(step_counts, aes(x = method, y = n_steps)) +
    geom_bar(stat = "identity", fill = "lightblue", alpha = 0.7) +
    theme_minimal() +
    labs(title = "Number of Steps Required",
         x = "Method", y = "Number of Steps") +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
  
  print(p2)
  
  # Print efficiency summary
  cat("\nEfficiency Summary:\n")
  cat("==================\n")
  for (i in 1:nrow(step_counts)) {
    cat(sprintf("%-12s: %d steps\n", 
                step_counts$method[i], 
                step_counts$n_steps[i]))
  }
}

# Run analyses
stability_analysis()
adaptive_demo()</code></pre>
                    </div>
                </div>

                <div class="bg-orange-50 border border-orange-200 p-4 rounded mt-6">
                    <h4 class="font-semibold text-orange-800 mb-2">
                        <i class="fas fa-lightbulb mr-2"></i>Practical Step Size Guidelines
                    </h4>
                    <ul class="list-disc pl-6 text-orange-700 text-sm">
                        <li><strong>Start Conservative:</strong> Begin with smaller step sizes and increase if stable</li>
                        <li><strong>Monitor Solution:</strong> Watch for oscillations or exponential growth</li>
                        <li><strong>Use Error Estimates:</strong> Employ methods with built-in error control</li>
                        <li><strong>Consider Problem Timescales:</strong> Match step size to solution characteristics</li>
                        <li><strong>Validate Results:</strong> Compare with analytical solutions when available</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Section 7: Higher-Order to First-Order -->
        <section id="higher-order" class="mb-12">
            <div class="bg-white rounded-lg shadow-md p-8">
                <h2 class="text-3xl font-bold mb-6 text-gray-800">
                    <i class="fas fa-arrows-split-up-and-left mr-3 text-teal-600"></i>
                    7. Converting Higher-Order ODEs to First-Order Systems
                </h2>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Why Convert to First-Order Systems?</h3>
                    
                    <p class="text-lg mb-4">
                        Most numerical ODE solvers are designed for first-order equations. To solve higher-order 
                        ODEs numerically, we convert them to systems of first-order equations.
                    </p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-teal-50 border border-teal-200 p-4 rounded">
                            <h4 class="font-semibold text-teal-800 mb-2">
                                <i class="fas fa-plus mr-2"></i>Advantages of First-Order Systems
                            </h4>
                            <ul class="list-disc pl-6 text-teal-700 text-sm">
                                <li>Unified numerical methods</li>
                                <li>Standard software compatibility</li>
                                <li>Phase space visualization</li>
                                <li>System analysis tools</li>
                                <li>Vector notation simplicity</li>
                            </ul>
                        </div>
                        
                        <div class="bg-blue-50 border border-blue-200 p-4 rounded">
                            <h4 class="font-semibold text-blue-800 mb-2">
                                <i class="fas fa-cogs mr-2"></i>Applications
                            </h4>
                            <ul class="list-disc pl-6 text-blue-700 text-sm">
                                <li>Mechanical vibrations</li>
                                <li>Electrical circuits</li>
                                <li>Control systems</li>
                                <li>Orbital mechanics</li>
                                <li>Population dynamics</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Conversion Methodology</h3>
                    
                    <div class="theorem-box">
                        <h4 class="font-semibold text-blue-800 mb-3">
                            <i class="fas fa-exchange-alt mr-2"></i>General Conversion Procedure
                        </h4>
                        <p class="mb-3">For an $n$-th order ODE:</p>
                        <div class="text-center bg-white p-4 rounded border mb-3">
                            $$y^{(n)} = f(t, y, y', y'', \ldots, y^{(n-1)})$$
                        </div>
                        <p class="mb-3">Define new variables:</p>
                        <div class="bg-white p-4 rounded border">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    $$y_1 = y$$
                                    $$y_2 = y'$$
                                    $$y_3 = y''$$
                                </div>
                                <div>
                                    $$\vdots$$
                                    $$y_n = y^{(n-1)}$$
                                </div>
                            </div>
                        </div>
                        <p class="mt-3">Resulting first-order system:</p>
                        <div class="text-center bg-white p-3 rounded border">
                            $$\frac{d}{dt}\begin{pmatrix} y_1 \\ y_2 \\ \vdots \\ y_n \end{pmatrix} = \begin{pmatrix} y_2 \\ y_3 \\ \vdots \\ f(t, y_1, y_2, \ldots, y_n) \end{pmatrix}$$
                        </div>
                    </div>
                </div>

                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Examples</h3>
                    
                    <div class="example-box">
                        <h4 class="font-semibold text-green-800 mb-3">
                            <i class="fas fa-calculator mr-2"></i>Example 1: Second-Order Linear ODE
                        </h4>
                        <p class="mb-3">Convert: $y'' + 3y' + 2y = \sin(t)$, $y(0) = 1$, $y'(0) = 0$</p>
                        <div class="bg-white p-4 rounded border mb-3">
                            <p class="mb-2"><strong>Step 1:</strong> Define variables</p>
                            $$y_1 = y, \quad y_2 = y'$$
                            <p class="mb-2 mt-3"><strong>Step 2:</strong> Express derivatives</p>
                            $$y_1' = y_2$$
                            $$y_2' = y'' = -3y' - 2y + \sin(t) = -3y_2 - 2y_1 + \sin(t)$$
                            <p class="mb-2 mt-3"><strong>Step 3:</strong> System form</p>
                            $$\frac{d}{dt}\begin{pmatrix} y_1 \\ y_2 \end{pmatrix} = \begin{pmatrix} y_2 \\ -2y_1 - 3y_2 + \sin(t) \end{pmatrix}$$
                            <p class="mb-2 mt-3"><strong>Step 4:</strong> Initial conditions</p>
                            $$\begin{pmatrix} y_1(0) \\ y_2(0) \end{pmatrix} = \begin{pmatrix} 1 \\ 0 \end{pmatrix}$$
                        </div>
                    </div>

                    <div class="example-box">
                        <h4 class="font-semibold text-green-800 mb-3">
                            <i class="fas fa-wave-square mr-2"></i>Example 2: Nonlinear Oscillator
                        </h4>
                        <p class="mb-3">Convert: $\ddot{x} + \sin(x) = 0$ (pendulum equation)</p>
                        <div class="bg-white p-4 rounded border">
                            <p class="mb-2"><strong>Variables:</strong> $x_1 = x$ (angle), $x_2 = \dot{x}$ (angular velocity)</p>
                            <p class="mb-2"><strong>System:</strong></p>
                            $$\frac{d}{dt}\begin{pmatrix} x_1 \\ x_2 \end{pmatrix} = \begin{pmatrix} x_2 \\ -\sin(x_1) \end{pmatrix}$$
                        </div>
                    </div>
                </div>

                <div class="method-comparison">
                    <div class="code-block python-code">
                        <h4 class="font-semibold mb-3">
                            <i class="fab fa-python mr-2"></i>Python Implementation
                        </h4>
                        <pre class="text-sm"><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp

# Example 1: Second-order linear ODE
def second_order_linear_system(t, y):
    """
    Convert y'' + 3y' + 2y = sin(t) to first-order system
    y1 = y, y2 = y'
    y1' = y2
    y2' = -2*y1 - 3*y2 + sin(t)
    """
    y1, y2 = y
    dy1dt = y2
    dy2dt = -2*y1 - 3*y2 + np.sin(t)
    return [dy1dt, dy2dt]

# Solve using our RK4 method adapted for systems
def rk4_system(f, t0, y0, t_end, h):
    """RK4 method for systems of ODEs"""
    n_steps = int((t_end - t0) / h) + 1
    t = np.linspace(t0, t_end, n_steps)
    
    # Initialize solution array
    y = np.zeros((n_steps, len(y0)))
    y[0] = y0
    
    for i in range(n_steps - 1):
        k1 = np.array(f(t[i], y[i]))
        k2 = np.array(f(t[i] + h/2, y[i] + h/2 * k1))
        k3 = np.array(f(t[i] + h/2, y[i] + h/2 * k2))
        k4 = np.array(f(t[i] + h, y[i] + h * k3))
        
        y[i + 1] = y[i] + (h/6) * (k1 + 2*k2 + 2*k3 + k4)
    
    return t, y

# Solve the second-order system
initial_conditions = [1, 0]  # y(0) = 1, y'(0) = 0
t_vals, y_solution = rk4_system(second_order_linear_system, 0, 
                               initial_conditions, 10, 0.01)

# Compare with scipy's solve_ivp
sol_scipy = solve_ivp(second_order_linear_system, [0, 10], 
                     initial_conditions, dense_output=True)
t_scipy = np.linspace(0, 10, 1000)
y_scipy = sol_scipy.sol(t_scipy).T

# Plot results
plt.figure(figsize=(15, 10))

# Time series plots
plt.subplot(2, 3, 1)
plt.plot(t_vals, y_solution[:, 0], 'r-', label='RK4 (y)', linewidth=2)
plt.plot(t_vals, y_solution[:, 1], 'b-', label="RK4 (y')", linewidth=2)
plt.plot(t_scipy, y_scipy[:, 0], 'r--', label='SciPy (y)', alpha=0.7)
plt.plot(t_scipy, y_scipy[:, 1], 'b--', label="SciPy (y')", alpha=0.7)
plt.xlabel('Time t')
plt.ylabel('Solution')
plt.title('Second-Order Linear ODE Solution')
plt.legend()
plt.grid(True, alpha=0.3)

# Phase portrait
plt.subplot(2, 3, 2)
plt.plot(y_solution[:, 0], y_solution[:, 1], 'r-', linewidth=2, label='RK4')
plt.plot(y_scipy[:, 0], y_scipy[:, 1], 'b--', alpha=0.7, label='SciPy')
plt.xlabel('y')
plt.ylabel("y'")
plt.title('Phase Portrait')
plt.legend()
plt.grid(True, alpha=0.3)

# Nonlinear pendulum example
def pendulum_system(t, y):
    """
    Pendulum equation: θ'' + sin(θ) = 0
    y1 = θ, y2 = θ'
    """
    theta, theta_dot = y
    dtheta_dt = theta_dot
    dtheta_dot_dt = -np.sin(theta)
    return [dtheta_dt, dtheta_dot_dt]

# Solve pendulum for different initial conditions
initial_conditions_list = [
    [0.1, 0],    # Small oscillations
    [1.0, 0],    # Medium oscillations
    [3.0, 0],    # Large oscillations
]

plt.subplot(2, 3, 3)

for i, ic in enumerate(initial_conditions_list):
    t_pend, y_pend = rk4_system(pendulum_system, 0, ic, 10, 0.01)
    plt.plot(t_pend, y_pend[:, 0], label=f'θ₀ = {ic[0]}', linewidth=2)

plt.xlabel('Time t')
plt.ylabel('θ (radians)')
plt.title('Nonlinear Pendulum')
plt.legend()
plt.grid(True, alpha=0.3)

# Pendulum phase portraits
plt.subplot(2, 3, 4)

for i, ic in enumerate(initial_conditions_list):
    t_pend, y_pend = rk4_system(pendulum_system, 0, ic, 10, 0.01)
    plt.plot(y_pend[:, 0], y_pend[:, 1], label=f'θ₀ = {ic[0]}', linewidth=2)

plt.xlabel('θ (radians)')
plt.ylabel('θ̇ (rad/s)')
plt.title('Pendulum Phase Portraits')
plt.legend()
plt.grid(True, alpha=0.3)

# Third-order example: y''' + y'' + y' + y = 0
def third_order_system(t, y):
    """
    y''' + y'' + y' + y = 0
    y1 = y, y2 = y', y3 = y''
    y1' = y2
    y2' = y3  
    y3' = -y1 - y2 - y3
    """
    y1, y2, y3 = y
    dy1dt = y2
    dy2dt = y3
    dy3dt = -y1 - y2 - y3
    return [dy1dt, dy2dt, dy3dt]

# Solve third-order system
ic_third = [1, 0, 0]  # y(0) = 1, y'(0) = 0, y''(0) = 0
t_third, y_third = rk4_system(third_order_system, 0, ic_third, 10, 0.01)

plt.subplot(2, 3, 5)
plt.plot(t_third, y_third[:, 0], 'r-', label='y', linewidth=2)
plt.plot(t_third, y_third[:, 1], 'g-', label="y'", linewidth=2)
plt.plot(t_third, y_third[:, 2], 'b-', label="y''", linewidth=2)
plt.xlabel('Time t')
plt.ylabel('Solution')
plt.title('Third-Order ODE Solution')
plt.legend()
plt.grid(True, alpha=0.3)

# 3D phase space for third-order system
from mpl_toolkits.mplot3d import Axes3D

ax = plt.subplot(2, 3, 6, projection='3d')
ax.plot(y_third[:, 0], y_third[:, 1], y_third[:, 2], 'r-', linewidth=2)
ax.set_xlabel('y')
ax.set_ylabel("y'")
ax.set_zlabel("y''")
ax.set_title('3D Phase Space')

plt.tight_layout()
plt.show()

# Performance comparison
print("Performance Comparison:")
print("=" * 30)
print(f"Custom RK4 steps: {len(t_vals)}")
print(f"SciPy solve_ivp evaluations: {sol_scipy.nfev}")
print(f"Max difference in y: {np.max(np.abs(y_solution[:, 0] - y_scipy[::10, 0])):.2e}")
print(f"Max difference in y': {np.max(np.abs(y_solution[:, 1] - y_scipy[::10, 1])):.2e}")</code></pre>
                    </div>

                    <div class="code-block r-code">
                        <h4 class="font-semibold mb-3">
                            <i class="fab fa-r-project mr-2"></i>R Implementation
                        </h4>
                        <pre class="text-sm"><code>library(deSolve)
library(ggplot2)
library(dplyr)
library(gridExtra)

# Example 1: Second-order linear ODE system
second_order_linear <- function(t, y, parms) {
  # y'' + 3y' + 2y = sin(t)
  # y1 = y, y2 = y'
  y1 <- y[1]
  y2 <- y[2]
  
  dy1dt <- y2
  dy2dt <- -2*y1 - 3*y2 + sin(t)
  
  return(list(c(dy1dt, dy2dt)))
}

# RK4 method for systems (R implementation)
rk4_system <- function(func, t0, y0, t_end, h, ...) {
  # Number of steps
  n_steps <- as.integer((t_end - t0) / h) + 1
  t <- seq(t0, t_end, length.out = n_steps)
  
  # Initialize solution matrix
  y <- matrix(0, nrow = n_steps, ncol = length(y0))
  y[1, ] <- y0
  
  for (i in 1:(n_steps - 1)) {
    k1 <- func(t[i], y[i, ], ...)[[1]]
    k2 <- func(t[i] + h/2, y[i, ] + h/2 * k1, ...)[[1]]
    k3 <- func(t[i] + h/2, y[i, ] + h/2 * k2, ...)[[1]]
    k4 <- func(t[i] + h, y[i, ] + h * k3, ...)[[1]]
    
    y[i + 1, ] <- y[i, ] + (h/6) * (k1 + 2*k2 + 2*k3 + k4)
  }
  
  return(data.frame(time = t, y))
}

# Solve using custom RK4
initial_conditions <- c(1, 0)  # y(0) = 1, y'(0) = 0
result_rk4 <- rk4_system(second_order_linear, 0, initial_conditions, 10, 0.01)
names(result_rk4) <- c("time", "y", "y_prime")

# Solve using deSolve for comparison
times <- seq(0, 10, by = 0.01)
result_desolve <- ode(y = initial_conditions, times = times, 
                     func = second_order_linear, method = "rk4")
result_desolve <- as.data.frame(result_desolve)
names(result_desolve) <- c("time", "y", "y_prime")

# Time series comparison plot
p1 <- ggplot() +
  geom_line(data = result_rk4, aes(x = time, y = y, color = "Custom RK4 (y)"), 
            size = 1) +
  geom_line(data = result_rk4, aes(x = time, y = y_prime, color = "Custom RK4 (y')"), 
            size = 1) +
  geom_line(data = result_desolve, aes(x = time, y = y, color = "deSolve (y)"), 
            linetype = "dashed", alpha = 0.7) +
  geom_line(data = result_desolve, aes(x = time, y = y_prime, color = "deSolve (y')"), 
            linetype = "dashed", alpha = 0.7) +
  theme_minimal() +
  labs(title = "Second-Order Linear ODE Solution",
       x = "Time t", y = "Solution",
       color = "Method") +
  theme(legend.position = "bottom")

# Phase portrait
p2 <- ggplot() +
  geom_path(data = result_rk4, aes(x = y, y = y_prime, color = "Custom RK4"), 
            size = 1) +
  geom_path(data = result_desolve, aes(x = y, y = y_prime, color = "deSolve"), 
            linetype = "dashed", alpha = 0.7) +
  theme_minimal() +
  labs(title = "Phase Portrait",
       x = "y", y = "y'",
       color = "Method") +
  theme(legend.position = "bottom")

# Nonlinear pendulum example
pendulum_system <- function(t, y, parms) {
  # θ'' + sin(θ) = 0
  theta <- y[1]
  theta_dot <- y[2]
  
  dtheta_dt <- theta_dot
  dtheta_dot_dt <- -sin(theta)
  
  return(list(c(dtheta_dt, dtheta_dot_dt)))
}

# Solve pendulum for different initial conditions
initial_conditions_list <- list(
  c(0.1, 0),   # Small oscillations
  c(1.0, 0),   # Medium oscillations
  c(3.0, 0)    # Large oscillations
)

pendulum_results <- data.frame()

for (i in seq_along(initial_conditions_list)) {
  ic <- initial_conditions_list[[i]]
  result <- rk4_system(pendulum_system, 0, ic, 10, 0.01)
  result$theta_0 <- paste("θ₀ =", ic[1])
  names(result)[2:3] <- c("theta", "theta_dot")
  pendulum_results <- rbind(pendulum_results, result)
}

# Pendulum time series
p3 <- ggplot(pendulum_results, aes(x = time, y = theta, color = theta_0)) +
  geom_line(size = 1) +
  theme_minimal() +
  labs(title = "Nonlinear Pendulum",
       x = "Time t", y = "θ (radians)",
       color = "Initial Condition") +
  theme(legend.position = "bottom")

# Pendulum phase portraits
p4 <- ggplot(pendulum_results, aes(x = theta, y = theta_dot, color = theta_0)) +
  geom_path(size = 1) +
  theme_minimal() +
  labs(title = "Pendulum Phase Portraits",
       x = "θ (radians)", y = "θ̇ (rad/s)",
       color = "Initial Condition") +
  theme(legend.position = "bottom")

# Third-order system example
third_order_system <- function(t, y, parms) {
  # y''' + y'' + y' + y = 0
  y1 <- y[1]  # y
  y2 <- y[2]  # y'
  y3 <- y[3]  # y''
  
  dy1dt <- y2
  dy2dt <- y3
  dy3dt <- -y1 - y2 - y3
  
  return(list(c(dy1dt, dy2dt, dy3dt)))
}

# Solve third-order system
ic_third <- c(1, 0, 0)  # y(0) = 1, y'(0) = 0, y''(0) = 0
result_third <- rk4_system(third_order_system, 0, ic_third, 10, 0.01)
names(result_third) <- c("time", "y", "y_prime", "y_double_prime")

# Reshape for plotting
result_third_long <- result_third %>%
  select(-time) %>%
  mutate(time = result_third$time) %>%
  pivot_longer(cols = -time, names_to = "derivative", values_to = "value") %>%
  mutate(derivative = factor(derivative, 
                           levels = c("y", "y_prime", "y_double_prime"),
                           labels = c("y", "y'", "y''")))

# Third-order system plot
p5 <- ggplot(result_third_long, aes(x = time, y = value, color = derivative)) +
  geom_line(size = 1) +
  theme_minimal() +
  labs(title = "Third-Order ODE Solution",
       x = "Time t", y = "Solution",
       color = "Variable") +
  theme(legend.position = "bottom")

# Display all plots
grid.arrange(p1, p2, p3, p4, ncol = 2)
print(p5)

# Performance comparison
cat("Performance Comparison:\n")
cat("======================\n")
cat("Custom RK4 points:", nrow(result_rk4), "\n")
cat("deSolve points:", nrow(result_desolve), "\n")

# Calculate maximum differences
diff_y <- max(abs(result_rk4$y - result_desolve$y))
diff_y_prime <- max(abs(result_rk4$y_prime - result_desolve$y_prime))

cat("Max difference in y:", sprintf("%.2e", diff_y), "\n")
cat("Max difference in y':", sprintf("%.2e", diff_y_prime), "\n")

# System conversion helper function
convert_higher_order <- function(equation_string, order) {
  cat("Converting", order, "order ODE to first-order system:\n")
  cat("Original equation:", equation_string, "\n")
  cat("System variables:\n")
  for (i in 1:order) {
    if (i == 1) {
      cat("  y₁ = y\n")
    } else if (i == 2) {
      cat("  y₂ = y'\n")
    } else {
      cat("  y₃ = y", paste(rep("'", i-1), collapse=""), "\n")
    }
  }
  cat("System equations:\n")
  for (i in 1:(order-1)) {
    cat("  y₁' = y₂\n")
  }
  cat("  y₃' = [solve for highest derivative from original equation]\n")
}

# Example conversions
convert_higher_order("y'' + 3y' + 2y = sin(t)", 2)
convert_higher_order("y''' + y'' + y' + y = 0", 3)</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 8: Systems of ODEs -->
        <section id="systems" class="mb-12">
            <div class="bg-white rounded-lg shadow-md p-8">
                <h2 class="text-3xl font-bold mb-6 text-gray-800">
                    <i class="fas fa-project-diagram mr-3 text-indigo-600"></i>
                    8. Systems of ODEs
                </h2>
                
                <div class="mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Summary</h3>
                    <p class="text-lg">
                        This chapter has covered the fundamental numerical methods for solving ordinary differential equations, 
                        from the basic Euler method to the sophisticated fourth-order Runge-Kutta method. Each method has 
                        its place in the numerical analyst's toolkit, with trade-offs between simplicity, accuracy, and computational cost.
                    </p>
                </div>
            </div>
        </section>
    </div>

    <!-- JavaScript for Interactive Charts -->
    <script>
        // Euler Geometry Chart
        document.addEventListener('DOMContentLoaded', function() {
            const ctx1 = document.getElementById('eulerGeometryChart');
            if (ctx1) {
                new Chart(ctx1, {
                    type: 'line',
                    data: {
                        labels: [0, 0.2, 0.4, 0.6, 0.8, 1.0],
                        datasets: [{
                            label: 'Exact Solution',
                            data: [0, 0.09516, 0.18127, 0.25918, 0.32968, 0.39347],
                            borderColor: 'blue',
                            backgroundColor: 'transparent',
                            borderWidth: 2,
                            pointRadius: 0
                        }, {
                            label: 'Euler Method',
                            data: [0, 0.08, 0.1536, 0.22189, 0.28462, 0.34232],
                            borderColor: 'red',
                            backgroundColor: 'transparent',
                            borderWidth: 2,
                            pointRadius: 5,
                            borderDash: [5, 5]
                        }, {
                            label: 'Tangent Lines',
                            data: [0, null, null, null, null, null],
                            borderColor: 'green',
                            backgroundColor: 'transparent',
                            borderWidth: 1,
                            pointRadius: 0,
                            showLine: false
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: 'Time t'
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: 'y(t)'
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: 'Euler Method: Following Tangent Lines'
                            },
                            legend: {
                                display: true
                            }
                        }
                    }
                });
            }

            // RK Comparison Chart
            const ctx2 = document.getElementById('rkComparisonChart');
            if (ctx2) {
                new Chart(ctx2, {
                    type: 'line',
                    data: {
                        labels: [0.1, 0.05, 0.025, 0.0125, 0.00625],
                        datasets: [{
                            label: 'Euler Method',
                            data: [0.1, 0.025, 0.00625, 0.0015625, 0.000390625],
                            borderColor: 'red',
                            backgroundColor: 'rgba(255, 0, 0, 0.1)',
                            borderWidth: 2,
                            pointRadius: 4
                        }, {
                            label: 'Improved Euler',
                            data: [0.01, 0.0025, 0.000625, 0.00015625, 0.000039063],
                            borderColor: 'green',
                            backgroundColor: 'rgba(0, 128, 0, 0.1)',
                            borderWidth: 2,
                            pointRadius: 4
                        }, {
                            label: 'RK4',
                            data: [0.0001, 0.00000625, 0.000000039, 0.0000000024, 0.00000000015],
                            borderColor: 'blue',
                            backgroundColor: 'rgba(0, 0, 255, 0.1)',
                            borderWidth: 2,
                            pointRadius: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                type: 'logarithmic',
                                title: {
                                    display: true,
                                    text: 'Step Size h'
                                }
                            },
                            y: {
                                type: 'logarithmic',
                                title: {
                                    display: true,
                                    text: 'Error'
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: 'Error vs Step Size Comparison (Log-Log Plot)'
                            },
                            legend: {
                                display: true
                            }
                        }
                    }
                });
            }
        });
    </script>
    <script id="html_badge_script1">
        window.__genspark_remove_badge_link = "https://www.genspark.ai/api/html_badge/" +
            "remove_badge?token=To%2FBnjzloZ3UfQdcSaYfDjTiNKSvelENQSYCluWopf5NEqzAzFCtl%2BVSmUWitWDd5KP4j%2FwKpl%2Fx1ICINn7YKMEu%2BvYwRXfXDDA%2FKwY5atTDgoTWoAIL06E3Q7TfxcKz0AP4i5k6zvc5MP9DggUPG1tS4AgBbdM5jVSAjBvyJC2h6JppmdhbTbFKv2oDGGyE6sUfEoaFH8ekAThBfgHsvRV6s1x%2FHAT4Z3SrTmS%2B9jJZs3qTOfc9ySADYptbIu19%2FwizmwOyTgXbk9tKGUVv0H%2B2j8I%2BtFBgl08HiQjxZ1dTauTh52zD5%2BKrvpszO0alEbpHkh2uvslcLYc1UWe6pKyaCSz%2BA%2FFB0z5mxsFIVKOXrgtHsADgC3tcm8n%2FNmMGrK0ubRx%2B%2F%2BsrNjryl9nxUruu%2FOZvgt4%2FPYcIlxiWf%2FZyoN%2FPURXd6TyXqqbUerRwtq%2BSUiL2np92a4GgU6g8JEUYd5r5uXE8I%2B%2FGkpP22PqOYm%2BY9QwvS6PAVrnVs4F%2Fp%2Byg%2FXYyQgUZcyBnOJsJbGx0uIMGRBS7rTq%2B4lHPaHlkxQV%2BKuisX60WhJ5qonYna5pCEEtnW7eycY%2Bmzt8%2F1A%3D%3D";
        window.__genspark_locale = "en-US";
        window.__genspark_token = "To/BnjzloZ3UfQdcSaYfDjTiNKSvelENQSYCluWopf5NEqzAzFCtl+VSmUWitWDd5KP4j/wKpl/x1ICINn7YKMEu+vYwRXfXDDA/KwY5atTDgoTWoAIL06E3Q7TfxcKz0AP4i5k6zvc5MP9DggUPG1tS4AgBbdM5jVSAjBvyJC2h6JppmdhbTbFKv2oDGGyE6sUfEoaFH8ekAThBfgHsvRV6s1x/HAT4Z3SrTmS+9jJZs3qTOfc9ySADYptbIu19/wizmwOyTgXbk9tKGUVv0H+2j8I+tFBgl08HiQjxZ1dTauTh52zD5+KrvpszO0alEbpHkh2uvslcLYc1UWe6pKyaCSz+A/FB0z5mxsFIVKOXrgtHsADgC3tcm8n/NmMGrK0ubRx+/+srNjryl9nxUruu/OZvgt4/PYcIlxiWf/ZyoN/PURXd6TyXqqbUerRwtq+SUiL2np92a4GgU6g8JEUYd5r5uXE8I+/GkpP22PqOYm+Y9QwvS6PAVrnVs4F/p+yg/XYyQgUZcyBnOJsJbGx0uIMGRBS7rTq+4lHPaHlkxQV+KuisX60WhJ5qonYna5pCEEtnW7eycY+mzt8/1A==";
    </script>
    
    <script id="html_notice_dialog_script" src="https://www.genspark.ai/notice_dialog.js"></script>
    