<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 12: Nonhomogeneous Equations - ODE Tutorial</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            }
        };
    </script>
    <style>
        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid #007acc;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            border-radius: 0.375rem;
        }
        .python-code { border-left-color: #3776ab; }
        .r-code { border-left-color: #276dc3; }
        .solution-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        .theorem-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        .example-box {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        .application-box {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        .warning-box {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: #333;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900 leading-relaxed">
    <div class="max-w-4xl mx-auto p-6">
        <!-- Header -->
        <header class="text-center mb-10">
            <h1 class="text-5xl font-bold text-gray-800 mb-4">
                <i class="fas fa-calculator text-blue-600 mr-3"></i>
                Chapter 12: Nonhomogeneous Equations
            </h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Comprehensive guide to solving second-order linear differential equations with forcing functions using the method of undetermined coefficients and variation of parameters
            </p>
            <div class="mt-6 p-4 bg-blue-100 rounded-lg">
                <p class="text-blue-800 font-semibold">
                    <i class="fas fa-info-circle mr-2"></i>
                    Part 3: Second-Order and Higher-Order Linear ODEs - Chapter 2
                </p>
            </div>
        </header>

        <!-- Learning Objectives -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-bullseye text-blue-600 mr-3"></i>Learning Objectives
            </h2>
            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold text-blue-600 mb-3">
                        <i class="fas fa-target mr-2"></i>Theoretical Mastery
                    </h3>
                    <ul class="space-y-2 text-gray-700">
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Understand solution structure: $y = y_h + y_p$</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Apply method of undetermined coefficients</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Master variation of parameters technique</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Handle resonance and modification rules</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold text-green-600 mb-3">
                        <i class="fas fa-cogs mr-2"></i>Practical Applications
                    </h3>
                    <ul class="space-y-2 text-gray-700">
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Solve forced oscillation problems</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Analyze AC electrical circuits</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Model systems with external inputs</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Apply superposition principles</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Introduction -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-rocket text-blue-600 mr-3"></i>Introduction to Nonhomogeneous Equations
            </h2>
            
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <p class="text-lg text-gray-700 mb-6">
                    In Chapter 11, we mastered homogeneous linear equations of the form $ay'' + by' + cy = 0$. Now we extend our toolkit to handle <strong>nonhomogeneous equations</strong> where the right-hand side is not zero:
                </p>
                
                <div class="bg-blue-50 p-6 rounded-lg mb-6">
                    <h3 class="text-xl font-semibold text-blue-800 mb-4">Standard Form</h3>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-blue-900">
                            $$ay'' + by' + cy = f(x)$$
                        </p>
                        <p class="text-gray-600 mt-2">where $f(x) \neq 0$ is the <em>forcing function</em> or <em>driving term</em></p>
                    </div>
                </div>

                <div class="grid md:grid-cols-2 gap-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-800 mb-2">
                            <i class="fas fa-balance-scale text-purple-600 mr-2"></i>Homogeneous vs Nonhomogeneous
                        </h4>
                        <ul class="text-sm text-gray-700 space-y-1">
                            <li><strong>Homogeneous:</strong> $y'' + 4y' + 4y = 0$</li>
                            <li><strong>Nonhomogeneous:</strong> $y'' + 4y' + 4y = e^x$</li>
                            <li><strong>Forcing function:</strong> $f(x) = e^x$</li>
                        </ul>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-800 mb-2">
                            <i class="fas fa-lightbulb text-yellow-600 mr-2"></i>Physical Interpretation
                        </h4>
                        <ul class="text-sm text-gray-700 space-y-1">
                            <li><strong>Free motion:</strong> No external forces</li>
                            <li><strong>Forced motion:</strong> External driving force</li>
                            <li><strong>Examples:</strong> AC circuits, driven oscillators</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Solution Structure -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-puzzle-piece text-blue-600 mr-3"></i>Solution Structure
            </h2>

            <div class="theorem-box">
                <h3 class="text-2xl font-bold mb-4">
                    <i class="fas fa-star mr-2"></i>Fundamental Theorem: Solution Structure
                </h3>
                <p class="text-lg mb-4">
                    The general solution to the nonhomogeneous equation $ay'' + by' + cy = f(x)$ is:
                </p>
                <div class="text-center bg-white bg-opacity-20 p-4 rounded-lg">
                    <p class="text-2xl font-bold">
                        $$y = y_h + y_p$$
                    </p>
                </div>
                <div class="grid md:grid-cols-2 gap-4 mt-6">
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">$y_h$ (Homogeneous Solution)</h4>
                        <ul class="text-sm space-y-1">
                            <li>• Solution to $ay'' + by' + cy = 0$</li>
                            <li>• Contains arbitrary constants $C_1, C_2$</li>
                            <li>• Also called <em>complementary function</em></li>
                        </ul>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">$y_p$ (Particular Solution)</h4>
                        <ul class="text-sm space-y-1">
                            <li>• Any solution to $ay'' + by' + cy = f(x)$</li>
                            <li>• No arbitrary constants</li>
                            <li>• Depends on the form of $f(x)$</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="bg-white p-8 rounded-lg shadow-md">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-question-circle text-blue-600 mr-2"></i>Why This Structure Works
                </h3>
                <p class="text-gray-700 mb-4">
                    The linearity of the differential operator ensures that if $y_h$ solves the homogeneous equation and $y_p$ solves the nonhomogeneous equation, then $y = y_h + y_p$ solves the nonhomogeneous equation:
                </p>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-center font-mono">
                        $L[y_h + y_p] = L[y_h] + L[y_p] = 0 + f(x) = f(x)$
                    </p>
                    <p class="text-center text-sm text-gray-600 mt-2">
                        where $L[y] = ay'' + by' + cy$
                    </p>
                </div>
            </div>
        </section>

        <!-- Method of Undetermined Coefficients -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-search text-blue-600 mr-3"></i>Method of Undetermined Coefficients
            </h2>

            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <p class="text-lg text-gray-700 mb-6">
                    The <strong>method of undetermined coefficients</strong> is a systematic approach for finding particular solutions when $f(x)$ has specific forms. It works by guessing the form of $y_p$ based on $f(x)$ and determining the coefficients.
                </p>

                <div class="warning-box">
                    <h3 class="text-xl font-bold mb-3">
                        <i class="fas fa-exclamation-triangle mr-2"></i>When to Use This Method
                    </h3>
                    <p class="mb-3">This method works when $f(x)$ is a combination of:</p>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="bg-white bg-opacity-50 p-3 rounded">
                            <h4 class="font-bold">Polynomials</h4>
                            <p class="text-sm">$x^n, x^2 + 3x, 5x^3 - 2$</p>
                        </div>
                        <div class="bg-white bg-opacity-50 p-3 rounded">
                            <h4 class="font-bold">Exponentials</h4>
                            <p class="text-sm">$e^{ax}, 3e^{-2x}, e^{5x}$</p>
                        </div>
                        <div class="bg-white bg-opacity-50 p-3 rounded">
                            <h4 class="font-bold">Trigonometric</h4>
                            <p class="text-sm">$\sin(bx), \cos(bx)$</p>
                        </div>
                    </div>
                    <p class="mt-3 text-sm">
                        <strong>Note:</strong> Does not work for functions like $\ln x, \tan x, x^{1/2}$, etc.
                    </p>
                </div>
            </div>

            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">
                    <i class="fas fa-table text-green-600 mr-2"></i>Trial Solution Table
                </h3>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-gray-50 border border-gray-300">
                        <thead class="bg-blue-100">
                            <tr>
                                <th class="px-4 py-3 text-left font-semibold text-gray-800 border-b">Forcing Function $f(x)$</th>
                                <th class="px-4 py-3 text-left font-semibold text-gray-800 border-b">Trial Solution $y_p$</th>
                                <th class="px-4 py-3 text-left font-semibold text-gray-800 border-b">Notes</th>
                            </tr>
                        </thead>
                        <tbody class="text-sm">
                            <tr class="bg-white">
                                <td class="px-4 py-3 border-b">$k$ (constant)</td>
                                <td class="px-4 py-3 border-b">$A$</td>
                                <td class="px-4 py-3 border-b">Constant</td>
                            </tr>
                            <tr class="bg-gray-50">
                                <td class="px-4 py-3 border-b">$ax + b$</td>
                                <td class="px-4 py-3 border-b">$Ax + B$</td>
                                <td class="px-4 py-3 border-b">Linear polynomial</td>
                            </tr>
                            <tr class="bg-white">
                                <td class="px-4 py-3 border-b">$ax^2 + bx + c$</td>
                                <td class="px-4 py-3 border-b">$Ax^2 + Bx + C$</td>
                                <td class="px-4 py-3 border-b">Quadratic polynomial</td>
                            </tr>
                            <tr class="bg-gray-50">
                                <td class="px-4 py-3 border-b">$ae^{rx}$</td>
                                <td class="px-4 py-3 border-b">$Ae^{rx}$</td>
                                <td class="px-4 py-3 border-b">Exponential</td>
                            </tr>
                            <tr class="bg-white">
                                <td class="px-4 py-3 border-b">$a\sin(kx)$ or $a\cos(kx)$</td>
                                <td class="px-4 py-3 border-b">$A\sin(kx) + B\cos(kx)$</td>
                                <td class="px-4 py-3 border-b">Always include both sin and cos</td>
                            </tr>
                            <tr class="bg-gray-50">
                                <td class="px-4 py-3 border-b">$xe^{rx}$</td>
                                <td class="px-4 py-3 border-b">$(Ax + B)e^{rx}$</td>
                                <td class="px-4 py-3 border-b">Polynomial times exponential</td>
                            </tr>
                            <tr class="bg-white">
                                <td class="px-4 py-3 border-b">$e^{rx}\sin(kx)$</td>
                                <td class="px-4 py-3 border-b">$e^{rx}(A\sin(kx) + B\cos(kx))$</td>
                                <td class="px-4 py-3 border-b">Exponential times trig</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="solution-box">
                <h3 class="text-2xl font-bold mb-4">
                    <i class="fas fa-cog mr-2"></i>Step-by-Step Algorithm
                </h3>
                <div class="space-y-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 1: Find Homogeneous Solution $y_h$</h4>
                        <p>Solve $ay'' + by' + cy = 0$ using characteristic equation method from Chapter 11.</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 2: Choose Trial Solution $y_p$</h4>
                        <p>Based on $f(x)$, select appropriate form from the table above.</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 3: Check for Resonance</h4>
                        <p>If $y_p$ duplicates terms in $y_h$, multiply by $x$ (or $x^2$ if needed).</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 4: Substitute and Solve</h4>
                        <p>Substitute $y_p$ into the original equation and solve for coefficients.</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 5: Form General Solution</h4>
                        <p>Combine: $y = y_h + y_p$ and apply initial conditions if given.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Detailed Examples -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-calculator text-blue-600 mr-3"></i>Detailed Examples
            </h2>

            <!-- Example 1: Polynomial Forcing -->
            <div class="example-box mb-8">
                <h3 class="text-2xl font-bold mb-4">
                    <i class="fas fa-play mr-2"></i>Example 1: Polynomial Forcing Function
                </h3>
                <div class="bg-white bg-opacity-20 p-4 rounded-lg mb-4">
                    <p class="text-lg font-semibold">Solve: $y'' + 4y = 2x + 1$</p>
                </div>
                
                <div class="space-y-4">
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 1: Find $y_h$</h4>
                        <p>Characteristic equation: $r^2 + 4 = 0 \Rightarrow r = \pm 2i$</p>
                        <p>$y_h = C_1\cos(2x) + C_2\sin(2x)$</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 2: Choose trial solution</h4>
                        <p>Since $f(x) = 2x + 1$ (linear polynomial), try $y_p = Ax + B$</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 3: Check resonance</h4>
                        <p>$y_p = Ax + B$ doesn't duplicate any terms in $y_h$, so no modification needed.</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 4: Substitute and solve</h4>
                        <p>$y_p' = A$, $y_p'' = 0$</p>
                        <p>$0 + 4(Ax + B) = 2x + 1$</p>
                        <p>$4Ax + 4B = 2x + 1$</p>
                        <p>Comparing coefficients: $4A = 2 \Rightarrow A = \frac{1}{2}$, $4B = 1 \Rightarrow B = \frac{1}{4}$</p>
                        <p>$y_p = \frac{1}{2}x + \frac{1}{4}$</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 5: General solution</h4>
                        <p class="text-lg font-bold">$y = C_1\cos(2x) + C_2\sin(2x) + \frac{1}{2}x + \frac{1}{4}$</p>
                    </div>
                </div>
            </div>

            <!-- Example 2: Exponential Forcing with Resonance -->
            <div class="example-box mb-8">
                <h3 class="text-2xl font-bold mb-4">
                    <i class="fas fa-play mr-2"></i>Example 2: Exponential Forcing with Resonance
                </h3>
                <div class="bg-white bg-opacity-20 p-4 rounded-lg mb-4">
                    <p class="text-lg font-semibold">Solve: $y'' - 3y' + 2y = e^x$</p>
                </div>
                
                <div class="space-y-4">
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 1: Find $y_h$</h4>
                        <p>Characteristic equation: $r^2 - 3r + 2 = 0 \Rightarrow (r-1)(r-2) = 0$</p>
                        <p>$r_1 = 1, r_2 = 2$</p>
                        <p>$y_h = C_1e^x + C_2e^{2x}$</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 2: Choose trial solution</h4>
                        <p>Since $f(x) = e^x$, normally try $y_p = Ae^x$</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 3: Check resonance</h4>
                        <p class="text-red-200 font-bold">RESONANCE: $e^x$ appears in $y_h$!</p>
                        <p>Modify: $y_p = Axe^x$</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 4: Substitute and solve</h4>
                        <p>$y_p = Axe^x$, $y_p' = A(1 + x)e^x$, $y_p'' = A(2 + x)e^x$</p>
                        <p>$A(2 + x)e^x - 3A(1 + x)e^x + 2Axe^x = e^x$</p>
                        <p>$Ae^x[(2 + x) - 3(1 + x) + 2x] = e^x$</p>
                        <p>$Ae^x[2 + x - 3 - 3x + 2x] = e^x$</p>
                        <p>$Ae^x[-1] = e^x \Rightarrow A = -1$</p>
                        <p>$y_p = -xe^x$</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 5: General solution</h4>
                        <p class="text-lg font-bold">$y = C_1e^x + C_2e^{2x} - xe^x$</p>
                    </div>
                </div>
            </div>

            <!-- Example 3: Trigonometric Forcing -->
            <div class="example-box">
                <h3 class="text-2xl font-bold mb-4">
                    <i class="fas fa-play mr-2"></i>Example 3: Trigonometric Forcing Function
                </h3>
                <div class="bg-white bg-opacity-20 p-4 rounded-lg mb-4">
                    <p class="text-lg font-semibold">Solve: $y'' + y = \sin(2x)$</p>
                </div>
                
                <div class="space-y-4">
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 1: Find $y_h$</h4>
                        <p>Characteristic equation: $r^2 + 1 = 0 \Rightarrow r = \pm i$</p>
                        <p>$y_h = C_1\cos(x) + C_2\sin(x)$</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 2: Choose trial solution</h4>
                        <p>Since $f(x) = \sin(2x)$, try $y_p = A\sin(2x) + B\cos(2x)$</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 3: Check resonance</h4>
                        <p>No resonance since frequencies are different (1 vs 2).</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 4: Substitute and solve</h4>
                        <p>$y_p' = 2A\cos(2x) - 2B\sin(2x)$</p>
                        <p>$y_p'' = -4A\sin(2x) - 4B\cos(2x)$</p>
                        <p>$-4A\sin(2x) - 4B\cos(2x) + A\sin(2x) + B\cos(2x) = \sin(2x)$</p>
                        <p>$(-4A + A)\sin(2x) + (-4B + B)\cos(2x) = \sin(2x)$</p>
                        <p>$-3A\sin(2x) - 3B\cos(2x) = \sin(2x)$</p>
                        <p>Comparing: $-3A = 1 \Rightarrow A = -\frac{1}{3}$, $-3B = 0 \Rightarrow B = 0$</p>
                        <p>$y_p = -\frac{1}{3}\sin(2x)$</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 5: General solution</h4>
                        <p class="text-lg font-bold">$y = C_1\cos(x) + C_2\sin(x) - \frac{1}{3}\sin(2x)$</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Variation of Parameters -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-exchange-alt text-blue-600 mr-3"></i>Variation of Parameters
            </h2>

            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <p class="text-lg text-gray-700 mb-6">
                    The <strong>variation of parameters</strong> method is more general than undetermined coefficients and works for any forcing function $f(x)$. It's particularly useful when $f(x)$ contains functions like $\ln x$, $\tan x$, or $e^x/x$.
                </p>

                <div class="theorem-box">
                    <h3 class="text-2xl font-bold mb-4">
                        <i class="fas fa-star mr-2"></i>Variation of Parameters Formula
                    </h3>
                    <p class="mb-4">For the equation $y'' + P(x)y' + Q(x)y = f(x)$ with homogeneous solutions $y_1, y_2$:</p>
                    
                    <div class="bg-white bg-opacity-20 p-6 rounded-lg">
                        <div class="text-center mb-4">
                            <p class="text-xl font-bold">$$y_p = -y_1\int\frac{y_2f(x)}{W(y_1,y_2)}dx + y_2\int\frac{y_1f(x)}{W(y_1,y_2)}dx$$</p>
                        </div>
                        <p class="text-center">where $W(y_1,y_2) = y_1y_2' - y_1'y_2$ is the Wronskian</p>
                    </div>
                    
                    <div class="mt-4 bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Alternative Form:</h4>
                        <p>$y_p = u_1y_1 + u_2y_2$ where:</p>
                        <ul class="mt-2 space-y-1">
                            <li>$u_1' = -\frac{y_2f(x)}{W(y_1,y_2)}$</li>
                            <li>$u_2' = \frac{y_1f(x)}{W(y_1,y_2)}$</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="solution-box">
                <h3 class="text-2xl font-bold mb-4">
                    <i class="fas fa-list-ol mr-2"></i>Variation of Parameters Algorithm
                </h3>
                <div class="space-y-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 1: Find Homogeneous Solutions</h4>
                        <p>Solve $ay'' + by' + cy = 0$ to get $y_1$ and $y_2$.</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 2: Compute Wronskian</h4>
                        <p>$W(y_1,y_2) = y_1y_2' - y_1'y_2$ (should be non-zero)</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 3: Convert to Standard Form</h4>
                        <p>If needed, divide by leading coefficient to get $y'' + P(x)y' + Q(x)y = f(x)$</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 4: Compute Integrals</h4>
                        <p>$u_1 = -\int\frac{y_2f(x)}{W}dx$ and $u_2 = \int\frac{y_1f(x)}{W}dx$</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 5: Form Particular Solution</h4>
                        <p>$y_p = u_1y_1 + u_2y_2$</p>
                    </div>
                </div>
            </div>

            <!-- Variation of Parameters Example -->
            <div class="example-box mt-8">
                <h3 class="text-2xl font-bold mb-4">
                    <i class="fas fa-play mr-2"></i>Example: Variation of Parameters
                </h3>
                <div class="bg-white bg-opacity-20 p-4 rounded-lg mb-4">
                    <p class="text-lg font-semibold">Solve: $y'' + y = \sec(x)$</p>
                    <p class="text-sm mt-2">(Note: Undetermined coefficients won't work here!)</p>
                </div>
                
                <div class="space-y-4">
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 1: Homogeneous solutions</h4>
                        <p>$r^2 + 1 = 0 \Rightarrow r = \pm i$</p>
                        <p>$y_1 = \cos(x)$, $y_2 = \sin(x)$</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 2: Wronskian</h4>
                        <p>$W = \cos(x) \cdot \cos(x) - (-\sin(x)) \cdot \sin(x) = \cos^2(x) + \sin^2(x) = 1$</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 3: Already in standard form</h4>
                        <p>$f(x) = \sec(x)$</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 4: Compute integrals</h4>
                        <p>$u_1 = -\int\frac{\sin(x) \cdot \sec(x)}{1}dx = -\int\tan(x)dx = \ln|\cos(x)|$</p>
                        <p>$u_2 = \int\frac{\cos(x) \cdot \sec(x)}{1}dx = \int 1 dx = x$</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 p-4 rounded">
                        <h4 class="font-bold mb-2">Step 5: Particular solution</h4>
                        <p>$y_p = \ln|\cos(x)| \cdot \cos(x) + x \cdot \sin(x)$</p>
                        <p class="text-lg font-bold mt-2">$y = C_1\cos(x) + C_2\sin(x) + \cos(x)\ln|\cos(x)| + x\sin(x)$</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Applications -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-cogs text-blue-600 mr-3"></i>Physical Applications
            </h2>

            <!-- Forced Oscillations -->
            <div class="application-box mb-8">
                <h3 class="text-2xl font-bold mb-4">
                    <i class="fas fa-wave-square mr-2"></i>Forced Harmonic Oscillations
                </h3>
                
                <div class="bg-white bg-opacity-20 p-6 rounded-lg mb-4">
                    <h4 class="text-xl font-bold mb-3">Mass-Spring System with External Force</h4>
                    <div class="text-center">
                        <p class="text-lg font-bold">$$m\frac{d^2x}{dt^2} + c\frac{dx}{dt} + kx = F_0\cos(\omega t)$$</p>
                    </div>
                    <div class="grid md:grid-cols-2 gap-4 mt-4">
                        <div>
                            <h5 class="font-bold mb-2">Physical Parameters:</h5>
                            <ul class="text-sm space-y-1">
                                <li>$m$: mass</li>
                                <li>$c$: damping coefficient</li>
                                <li>$k$: spring constant</li>
                                <li>$F_0$: force amplitude</li>
                                <li>$\omega$: driving frequency</li>
                            </ul>
                        </div>
                        <div>
                            <h5 class="font-bold mb-2">Key Concepts:</h5>
                            <ul class="text-sm space-y-1">
                                <li>Natural frequency: $\omega_0 = \sqrt{k/m}$</li>
                                <li>Resonance when $\omega \approx \omega_0$</li>
                                <li>Transient vs steady-state</li>
                                <li>Phase lag between force and response</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="bg-white bg-opacity-10 p-4 rounded">
                    <h4 class="font-bold mb-2">Solution Structure:</h4>
                    <ul class="space-y-2">
                        <li><strong>Homogeneous part:</strong> Natural oscillations (transient)</li>
                        <li><strong>Particular part:</strong> Forced oscillations (steady-state)</li>
                        <li><strong>Total response:</strong> $x(t) = x_{\text{transient}} + x_{\text{steady-state}}$</li>
                    </ul>
                </div>
            </div>

            <!-- RLC Circuits -->
            <div class="application-box">
                <h3 class="text-2xl font-bold mb-4">
                    <i class="fas fa-bolt mr-2"></i>RLC Circuits with AC Sources
                </h3>
                
                <div class="bg-white bg-opacity-20 p-6 rounded-lg mb-4">
                    <h4 class="text-xl font-bold mb-3">Series RLC Circuit</h4>
                    <div class="text-center">
                        <p class="text-lg font-bold">$$L\frac{d^2Q}{dt^2} + R\frac{dQ}{dt} + \frac{Q}{C} = V_0\cos(\omega t)$$</p>
                    </div>
                    <div class="grid md:grid-cols-2 gap-4 mt-4">
                        <div>
                            <h5 class="font-bold mb-2">Circuit Elements:</h5>
                            <ul class="text-sm space-y-1">
                                <li>$L$: inductance (henries)</li>
                                <li>$R$: resistance (ohms)</li>
                                <li>$C$: capacitance (farads)</li>
                                <li>$Q$: charge (coulombs)</li>
                                <li>$V_0\cos(\omega t)$: AC voltage source</li>
                            </ul>
                        </div>
                        <div>
                            <h5 class="font-bold mb-2">Key Relationships:</h5>
                            <ul class="text-sm space-y-1">
                                <li>Current: $I = dQ/dt$</li>
                                <li>Resonant frequency: $\omega_0 = 1/\sqrt{LC}$</li>
                                <li>Quality factor: $Q = \omega_0 L/R$</li>
                                <li>Impedance: $Z = R + i(\omega L - 1/(\omega C))$</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="bg-white bg-opacity-10 p-4 rounded">
                    <h4 class="font-bold mb-2">Physical Interpretation:</h4>
                    <ul class="space-y-2">
                        <li><strong>At resonance ($\omega = \omega_0$):</strong> Maximum current amplitude</li>
                        <li><strong>Below resonance:</strong> Capacitive behavior dominates</li>
                        <li><strong>Above resonance:</strong> Inductive behavior dominates</li>
                        <li><strong>Transient response:</strong> Depends on initial conditions</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Programming Implementation -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-code text-blue-600 mr-3"></i>Programming Implementation
            </h2>

            <!-- Python Implementation -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">
                    <i class="fab fa-python text-blue-600 mr-2"></i>Python Implementation
                </h3>

                <div class="code-block python-code">
                    <h4 class="font-bold mb-3">Method of Undetermined Coefficients</h4>
                    <pre class="text-sm overflow-x-auto"><code>import numpy as np
import matplotlib.pyplot as plt
from sympy import *
from scipy.integrate import solve_ivp

# Define symbolic variables
x, r, C1, C2 = symbols('x r C1 C2')
A, B = symbols('A B')

def solve_nonhomogeneous_ode():
    """
    Solve y'' + 4y = 2x + 1 using undetermined coefficients
    """
    print("Solving: y'' + 4y = 2x + 1")
    print("="*50)
    
    # Step 1: Find homogeneous solution
    char_eq = r**2 + 4
    roots = solve(char_eq, r)
    print(f"Characteristic equation: r² + 4 = 0")
    print(f"Roots: {roots}")
    
    # Homogeneous solution
    y_h = C1*cos(2*x) + C2*sin(2*x)
    print(f"Homogeneous solution: y_h = {y_h}")
    
    # Step 2: Choose trial solution for f(x) = 2x + 1
    y_p_trial = A*x + B
    print(f"Trial particular solution: y_p = {y_p_trial}")
    
    # Step 3: Substitute into ODE
    y_p_prime = diff(y_p_trial, x)
    y_p_double_prime = diff(y_p_prime, x)
    
    # Substitute into y'' + 4y = 2x + 1
    lhs = y_p_double_prime + 4*y_p_trial
    rhs = 2*x + 1
    
    print(f"Substituting: {lhs} = {rhs}")
    
    # Step 4: Solve for coefficients
    eq = Eq(lhs, rhs)
    coeffs = solve(eq, [A, B])
    print(f"Coefficients: {coeffs}")
    
    # Particular solution
    y_p = y_p_trial.subs(coeffs)
    print(f"Particular solution: y_p = {y_p}")
    
    # General solution
    y_general = y_h + y_p
    print(f"General solution: y = {y_general}")
    
    return y_general, coeffs

# Solve the equation
solution, coeffs = solve_nonhomogeneous_ode()

# Numerical verification
def verify_solution():
    """
    Numerically verify the solution
    """
    def ode_system(t, y):
        # Convert to system: y' = z, z' + 4y = 2t + 1
        return [y[1], 2*t + 1 - 4*y[0]]
    
    # Initial conditions
    t_span = (0, 2*np.pi)
    y0 = [1, 0]  # y(0) = 1, y'(0) = 0
    
    # Solve numerically
    sol = solve_ivp(ode_system, t_span, y0, dense_output=True)
    
    # Plot results
    t = np.linspace(0, 2*np.pi, 1000)
    y_numerical = sol.sol(t)[0]
    
    # Analytical solution with specific initial conditions
    # Determine C1, C2 from initial conditions
    t_sym = symbols('t')
    y_analytical = solution.subs(x, t_sym)
    
    # Apply initial conditions y(0) = 1, y'(0) = 0
    ic_eqs = [
        Eq(y_analytical.subs(t_sym, 0), 1),
        Eq(diff(y_analytical, t_sym).subs(t_sym, 0), 0)
    ]
    
    constants = solve(ic_eqs, [C1, C2])
    y_final = y_analytical.subs(constants)
    
    # Convert to numerical function
    y_func = lambdify(t_sym, y_final, 'numpy')
    y_analytical_vals = y_func(t)
    
    # Plot comparison
    plt.figure(figsize=(12, 8))
    plt.subplot(2, 1, 1)
    plt.plot(t, y_numerical, 'b-', label='Numerical Solution', linewidth=2)
    plt.plot(t, y_analytical_vals, 'r--', label='Analytical Solution', linewidth=2)
    plt.xlabel('t')
    plt.ylabel('y(t)')
    plt.title('Solution Comparison: y\'\' + 4y = 2t + 1')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot error
    plt.subplot(2, 1, 2)
    error = np.abs(y_numerical - y_analytical_vals)
    plt.semilogy(t, error, 'g-', linewidth=2)
    plt.xlabel('t')
    plt.ylabel('|Error|')
    plt.title('Numerical Error (log scale)')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    print(f"Maximum error: {np.max(error):.2e}")

# Run verification
verify_solution()</code></pre>
                </div>

                <div class="code-block python-code">
                    <h4 class="font-bold mb-3">Variation of Parameters Implementation</h4>
                    <pre class="text-sm overflow-x-auto"><code>def variation_of_parameters(y1, y2, f_x, x_var):
    """
    Solve y'' + P(x)y' + Q(x)y = f(x) using variation of parameters
    
    Parameters:
    y1, y2: Fundamental solutions to homogeneous equation
    f_x: Forcing function
    x_var: Independent variable
    """
    
    # Compute Wronskian
    y1_prime = diff(y1, x_var)
    y2_prime = diff(y2, x_var)
    W = y1 * y2_prime - y1_prime * y2
    
    print(f"y1 = {y1}")
    print(f"y2 = {y2}")
    print(f"Wronskian W = {W}")
    
    # Compute u1' and u2'
    u1_prime = -y2 * f_x / W
    u2_prime = y1 * f_x / W
    
    print(f"u1' = {u1_prime}")
    print(f"u2' = {u2_prime}")
    
    # Integrate to find u1 and u2
    u1 = integrate(u1_prime, x_var)
    u2 = integrate(u2_prime, x_var)
    
    print(f"u1 = {u1}")
    print(f"u2 = {u2}")
    
    # Particular solution
    y_p = u1 * y1 + u2 * y2
    y_p_simplified = simplify(y_p)
    
    print(f"Particular solution: y_p = {y_p_simplified}")
    
    return y_p_simplified

# Example: Solve y'' + y = sec(x)
print("Solving y'' + y = sec(x) using Variation of Parameters")
print("="*60)

x = symbols('x')
y1 = cos(x)
y2 = sin(x)
f_x = sec(x)

y_p = variation_of_parameters(y1, y2, f_x, x)

# General solution
C1, C2 = symbols('C1 C2')
y_general = C1*y1 + C2*y2 + y_p
print(f"\nGeneral solution: y = {y_general}")

# Create interactive plot for different forcing functions
def plot_forced_oscillations():
    """
    Plot solutions for different forcing functions
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    t = np.linspace(0, 4*np.pi, 1000)
    
    # Different forcing functions and their solutions
    cases = [
        {'title': 'y\'\' + y = sin(2t)', 'homogeneous': lambda t: np.cos(t), 
         'particular': lambda t: -np.sin(2*t)/3, 'forcing': lambda t: np.sin(2*t)},
        {'title': 'y\'\' + 4y = 2t + 1', 'homogeneous': lambda t: np.cos(2*t), 
         'particular': lambda t: 0.5*t + 0.25, 'forcing': lambda t: 2*t + 1},
        {'title': 'y\'\' + 2y\' + y = e^(-t)', 'homogeneous': lambda t: np.exp(-t), 
         'particular': lambda t: 0.5*t*np.exp(-t), 'forcing': lambda t: np.exp(-t)},
        {'title': 'y\'\' + y = cos(t) [Resonance]', 'homogeneous': lambda t: np.cos(t), 
         'particular': lambda t: 0.5*t*np.sin(t), 'forcing': lambda t: np.cos(t)}
    ]
    
    for i, case in enumerate(cases):
        ax = axes[i//2, i%2]
        
        # Plot components
        y_h = case['homogeneous'](t)
        y_p = case['particular'](t)
        y_total = y_h + y_p
        forcing = case['forcing'](t)
        
        ax.plot(t, y_h, 'b--', alpha=0.7, label='Homogeneous')
        ax.plot(t, y_p, 'r--', alpha=0.7, label='Particular')
        ax.plot(t, y_total, 'k-', linewidth=2, label='Total Solution')
        ax.plot(t, 0.2*forcing, 'g:', alpha=0.8, label='0.2×Forcing')
        
        ax.set_title(case['title'])
        ax.set_xlabel('t')
        ax.set_ylabel('y(t)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_xlim(0, 4*np.pi)
    
    plt.tight_layout()
    plt.show()

# Generate plots
plot_forced_oscillations()</code></pre>
                </div>
            </div>

            <!-- R Implementation -->
            <div class="bg-white p-8 rounded-lg shadow-md">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">
                    <i class="fab fa-r-project text-blue-600 mr-2"></i>R Implementation
                </h3>

                <div class="code-block r-code">
                    <h4 class="font-bold mb-3">Solving Nonhomogeneous ODEs in R</h4>
                    <pre class="text-sm overflow-x-auto"><code># Load required libraries
library(deSolve)
library(ggplot2)
library(gridExtra)
library(Ryacas)

# Function to solve nonhomogeneous ODE using undetermined coefficients
solve_nonhomogeneous <- function() {
  cat("Solving y'' + 4y = 2x + 1 using undetermined coefficients\n")
  cat(rep("=", 50), "\n")
  
  # Homogeneous solution: y_h = C1*cos(2x) + C2*sin(2x)
  cat("Homogeneous solution: y_h = C1*cos(2x) + C2*sin(2x)\n")
  
  # Trial solution for f(x) = 2x + 1: y_p = Ax + B
  # Substituting: 0 + 4(Ax + B) = 2x + 1
  # 4Ax + 4B = 2x + 1
  # A = 1/2, B = 1/4
  
  A <- 1/2
  B <- 1/4
  
  cat("Particular solution: y_p =", A, "x +", B, "\n")
  cat("General solution: y = C1*cos(2x) + C2*sin(2x) +", A, "x +", B, "\n")
  
  return(list(A = A, B = B))
}

# Numerical verification
verify_solution_r <- function() {
  # Define ODE system
  ode_func <- function(t, y, parms) {
    with(as.list(y), {
      dy1 <- y2
      dy2 <- 2*t + 1 - 4*y1
      list(c(dy1, dy2))
    })
  }
  
  # Initial conditions
  initial_conditions <- c(y1 = 1, y2 = 0)
  times <- seq(0, 2*pi, length.out = 1000)
  
  # Solve numerically
  sol <- ode(y = initial_conditions, times = times, func = ode_func, method = "rk4")
  
  # Analytical solution with initial conditions
  # y = C1*cos(2t) + C2*sin(2t) + 0.5*t + 0.25
  # From y(0) = 1: C1 + 0.25 = 1 => C1 = 0.75
  # From y'(0) = 0: 2*C2 + 0.5 = 0 => C2 = -0.25
  
  C1 <- 0.75
  C2 <- -0.25
  
  analytical <- C1*cos(2*times) + C2*sin(2*times) + 0.5*times + 0.25
  
  # Create comparison plot
  comparison_data <- data.frame(
    t = times,
    numerical = sol[, "y1"],
    analytical = analytical,
    error = abs(sol[, "y1"] - analytical)
  )
  
  p1 <- ggplot(comparison_data, aes(x = t)) +
    geom_line(aes(y = numerical, color = "Numerical"), size = 1.2) +
    geom_line(aes(y = analytical, color = "Analytical"), size = 1.2, linetype = "dashed") +
    labs(title = "Solution Comparison: y'' + 4y = 2t + 1",
         x = "t", y = "y(t)") +
    scale_color_manual(values = c("Numerical" = "blue", "Analytical" = "red")) +
    theme_minimal() +
    theme(legend.title = element_blank())
  
  p2 <- ggplot(comparison_data, aes(x = t, y = error)) +
    geom_line(color = "green", size = 1.2) +
    scale_y_log10() +
    labs(title = "Numerical Error (log scale)",
         x = "t", y = "|Error|") +
    theme_minimal()
  
  # Display plots
  grid.arrange(p1, p2, ncol = 1)
  
  cat("Maximum error:", max(comparison_data$error), "\n")
  
  return(comparison_data)
}

# Variation of parameters implementation
variation_of_parameters_r <- function() {
  cat("Solving y'' + y = sec(x) using Variation of Parameters\n")
  cat(rep("=", 60), "\n")
  
  # Homogeneous solutions
  cat("y1 = cos(x), y2 = sin(x)\n")
  cat("Wronskian W = cos²(x) + sin²(x) = 1\n")
  
  # For y'' + y = sec(x)
  # u1' = -sin(x) * sec(x) / 1 = -tan(x)
  # u2' = cos(x) * sec(x) / 1 = 1
  
  cat("u1' = -tan(x), u2' = 1\n")
  cat("u1 = ln|cos(x)|, u2 = x\n")
  cat("Particular solution: y_p = cos(x)*ln|cos(x)| + x*sin(x)\n")
  cat("General solution: y = C1*cos(x) + C2*sin(x) + cos(x)*ln|cos(x)| + x*sin(x)\n")
}

# Forced oscillation analysis
analyze_forced_oscillations <- function() {
  # Parameters for different systems
  systems <- list(
    list(name = "Underdamped", omega0 = 1, gamma = 0.1, omega_drive = 0.8),
    list(name = "Critically Damped", omega0 = 1, gamma = 1, omega_drive = 0.8),
    list(name = "Overdamped", omega0 = 1, gamma = 2, omega_drive = 0.8),
    list(name = "Resonance", omega0 = 1, gamma = 0.1, omega_drive = 1.0)
  )
  
  plots <- list()
  
  for (i in seq_along(systems)) {
    sys <- systems[[i]]
    
    # ODE: y'' + 2*gamma*y' + omega0^2*y = F0*cos(omega_drive*t)
    # Convert to system of first-order ODEs
    ode_system <- function(t, y, parms) {
      with(as.list(c(y, parms)), {
        dy1 <- y2
        dy2 <- F0*cos(omega_drive*t) - 2*gamma*y2 - omega0^2*y1
        list(c(dy1, dy2))
      })
    }
    
    # Parameters
    parms <- list(
      omega0 = sys$omega0,
      gamma = sys$gamma,
      omega_drive = sys$omega_drive,
      F0 = 1
    )
    
    # Initial conditions
    y0 <- c(y1 = 0, y2 = 0)
    
    # Time span
    times <- seq(0, 20, length.out = 2000)
    
    # Solve
    sol <- ode(y = y0, times = times, func = ode_system, parms = parms, method = "rk4")
    
    # Create plot
    plot_data <- data.frame(
      t = times,
      displacement = sol[, "y1"],
      velocity = sol[, "y2"]
    )
    
    plots[[i]] <- ggplot(plot_data, aes(x = t, y = displacement)) +
      geom_line(color = "blue", size = 1) +
      labs(title = paste("Forced Oscillation:", sys$name),
           subtitle = paste("ω₀ =", sys$omega0, ", γ =", sys$gamma, ", ωd =", sys$omega_drive),
           x = "Time", y = "Displacement") +
      theme_minimal() +
      theme(plot.title = element_text(size = 10),
            plot.subtitle = element_text(size = 8))
  }
  
  # Arrange plots
  do.call(grid.arrange, c(plots, ncol = 2))
}

# Run the analysis
coeffs <- solve_nonhomogeneous()
data <- verify_solution_r()
variation_of_parameters_r()
analyze_forced_oscillations()</code></pre>
                </div>

                <div class="code-block r-code">
                    <h4 class="font-bold mb-3">Advanced Analysis: Frequency Response</h4>
                    <pre class="text-sm overflow-x-auto"><code># Frequency response analysis for forced oscillations
frequency_response_analysis <- function() {
  # System parameters
  omega0 <- 1    # Natural frequency
  gamma <- 0.1   # Damping coefficient
  F0 <- 1        # Force amplitude
  
  # Frequency range
  omega_range <- seq(0.1, 3, length.out = 100)
  
  # Calculate amplitude and phase response
  amplitude_response <- numeric(length(omega_range))
  phase_response <- numeric(length(omega_range))
  
  for (i in seq_along(omega_range)) {
    omega <- omega_range[i]
    
    # Transfer function: H(ω) = 1/(-ω² + 2iγω + ω₀²)
    denominator <- complex(real = omega0^2 - omega^2, imaginary = 2*gamma*omega)
    H <- 1 / denominator
    
    amplitude_response[i] <- Mod(H)
    phase_response[i] <- Arg(H) * 180 / pi  # Convert to degrees
  }
  
  # Create frequency response plots
  freq_data <- data.frame(
    frequency = omega_range,
    amplitude = amplitude_response,
    phase = phase_response
  )
  
  # Amplitude plot
  p1 <- ggplot(freq_data, aes(x = frequency, y = amplitude)) +
    geom_line(color = "blue", size = 1.2) +
    geom_vline(xintercept = omega0, linetype = "dashed", color = "red", alpha = 0.7) +
    labs(title = "Amplitude Response",
         x = "Driving Frequency (ω)", y = "Amplitude |H(ω)|") +
    annotate("text", x = omega0 + 0.2, y = max(amplitude_response) * 0.8, 
             label = "Resonance", color = "red") +
    theme_minimal()
  
  # Phase plot
  p2 <- ggplot(freq_data, aes(x = frequency, y = phase)) +
    geom_line(color = "red", size = 1.2) +
    geom_vline(xintercept = omega0, linetype = "dashed", color = "red", alpha = 0.7) +
    labs(title = "Phase Response",
         x = "Driving Frequency (ω)", y = "Phase (degrees)") +
    theme_minimal()
  
  # Combined plot
  grid.arrange(p1, p2, ncol = 1)
  
  # Find resonance frequency and peak amplitude
  peak_index <- which.max(amplitude_response)
  resonance_freq <- omega_range[peak_index]
  peak_amplitude <- amplitude_response[peak_index]
  
  cat("Resonance Analysis:\n")
  cat("Natural frequency (ω₀):", omega0, "\n")
  cat("Resonance frequency:", resonance_freq, "\n")
  cat("Peak amplitude:", peak_amplitude, "\n")
  cat("Quality factor (Q):", omega0 / (2 * gamma), "\n")
  
  return(freq_data)
}

# Quality factor analysis
quality_factor_analysis <- function() {
  omega0 <- 1
  gamma_values <- c(0.05, 0.1, 0.2, 0.5, 1.0)
  omega_range <- seq(0.1, 3, length.out = 200)
  
  plot_data <- data.frame()
  
  for (gamma in gamma_values) {
    amplitude_response <- numeric(length(omega_range))
    
    for (i in seq_along(omega_range)) {
      omega <- omega_range[i]
      denominator <- complex(real = omega0^2 - omega^2, imaginary = 2*gamma*omega)
      H <- 1 / denominator
      amplitude_response[i] <- Mod(H)
    }
    
    temp_data <- data.frame(
      frequency = omega_range,
      amplitude = amplitude_response,
      gamma = factor(gamma),
      Q = omega0 / (2 * gamma)
    )
    
    plot_data <- rbind(plot_data, temp_data)
  }
  
  # Create plot
  p <- ggplot(plot_data, aes(x = frequency, y = amplitude, color = gamma)) +
    geom_line(size = 1.2) +
    geom_vline(xintercept = omega0, linetype = "dashed", alpha = 0.5) +
    labs(title = "Effect of Damping on Frequency Response",
         subtitle = "Different Quality Factors (Q = ω₀/2γ)",
         x = "Frequency (ω)", y = "Amplitude |H(ω)|",
         color = "Damping (γ)") +
    scale_color_viridis_d() +
    theme_minimal() +
    theme(legend.position = "right")
  
  print(p)
  
  # Summary statistics
  cat("Quality Factor Analysis:\n")
  for (gamma in gamma_values) {
    Q <- omega0 / (2 * gamma)
    cat(sprintf("γ = %.2f, Q = %.1f\n", gamma, Q))
  }
}

# Run advanced analyses
freq_data <- frequency_response_analysis()
quality_factor_analysis()</code></pre>
                </div>
            </div>
        </section>

        <!-- Advanced Topics -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-rocket text-blue-600 mr-3"></i>Advanced Topics
            </h2>

            <!-- Green's Functions -->
            <div class="bg-white p-8 rounded-lg shadow-md mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">
                    <i class="fas fa-function text-green-600 mr-2"></i>Green's Functions
                </h3>
                
                <div class="theorem-box">
                    <h4 class="text-xl font-bold mb-4">Green's Function Method</h4>
                    <p class="mb-4">
                        The Green's function $G(x,\xi)$ for the operator $L = a\frac{d^2}{dx^2} + b\frac{d}{dx} + c$ satisfies:
                    </p>
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <p class="text-center text-lg">$$LG(x,\xi) = \delta(x-\xi)$$</p>
                        <p class="text-center mt-2">where $\delta(x-\xi)$ is the Dirac delta function</p>
                    </div>
                    <p class="mt-4">
                        The particular solution is then given by:
                    </p>
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg mt-2">
                        <p class="text-center text-lg">$$y_p(x) = \int_{a}^{b} G(x,\xi) f(\xi) d\xi$$</p>
                    </div>
                </div>

                <div class="bg-gray-50 p-6 rounded-lg mt-6">
                    <h4 class="font-bold text-gray-800 mb-3">Physical Interpretation</h4>
                    <ul class="space-y-2 text-gray-700">
                        <li><strong>Impulse Response:</strong> Response to a unit impulse at point $\xi$</li>
                        <li><strong>Superposition:</strong> Total response is integral of impulse responses</li>
                        <li><strong>Causality:</strong> $G(x,\xi) = 0$ for $x < \xi$ (causal systems)</li>
                        <li><strong>Applications:</strong> Heat conduction, wave propagation, quantum mechanics</li>
                    </ul>
                </div>
            </div>

            <!-- Superposition Principle -->
            <div class="bg-white p-8 rounded-lg shadow-md">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">
                    <i class="fas fa-layer-group text-purple-600 mr-2"></i>Superposition Principle
                </h3>
                
                <div class="example-box">
                    <h4 class="text-xl font-bold mb-4">Linearity of Differential Operators</h4>
                    <p class="mb-4">
                        For the linear operator $L[y] = ay'' + by' + cy$ and forcing functions $f_1(x), f_2(x)$:
                    </p>
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <p class="text-center">If $L[y_1] = f_1(x)$ and $L[y_2] = f_2(x)$</p>
                        <p class="text-center">Then $L[\alpha y_1 + \beta y_2] = \alpha f_1(x) + \beta f_2(x)$</p>
                    </div>
                </div>

                <div class="grid md:grid-cols-2 gap-6 mt-6">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-bold text-blue-800 mb-2">Applications</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Multiple forcing functions</li>
                            <li>• Fourier series decomposition</li>
                            <li>• AC circuit analysis</li>
                            <li>• Signal processing</li>
                        </ul>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="font-bold text-green-800 mb-2">Examples</h4>
                        <ul class="text-sm text-green-700 space-y-1">
                            <li>• $f(x) = \sin(x) + \cos(2x)$</li>
                            <li>• $f(x) = e^x + x^2 + 1$</li>
                            <li>• Piecewise functions</li>
                            <li>• Periodic forcing</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Problem-Solving Strategy -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-lightbulb text-blue-600 mr-3"></i>Problem-Solving Strategy
            </h2>

            <div class="solution-box">
                <h3 class="text-2xl font-bold mb-6">
                    <i class="fas fa-map mr-2"></i>Complete Solution Flowchart
                </h3>
                
                <div class="space-y-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 1: Identify Equation Type</h4>
                        <p class="text-sm">Standard form: $ay'' + by' + cy = f(x)$</p>
                        <p class="text-sm">Check if $f(x) = 0$ (homogeneous) or $f(x) \neq 0$ (nonhomogeneous)</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 2: Find Homogeneous Solution $y_h$</h4>
                        <p class="text-sm">Use characteristic equation method from Chapter 11</p>
                        <p class="text-sm">$ar^2 + br + c = 0$ → Real distinct, complex conjugate, or repeated roots</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 3: Choose Method for Particular Solution</h4>
                        <div class="grid md:grid-cols-2 gap-2 text-sm">
                            <div>
                                <strong>Undetermined Coefficients:</strong>
                                <ul class="ml-4 mt-1 space-y-1">
                                    <li>• Polynomials</li>
                                    <li>• Exponentials</li>
                                    <li>• Trigonometric</li>
                                    <li>• Combinations</li>
                                </ul>
                            </div>
                            <div>
                                <strong>Variation of Parameters:</strong>
                                <ul class="ml-4 mt-1 space-y-1">
                                    <li>• Any function $f(x)$</li>
                                    <li>• $\ln x, \tan x$, etc.</li>
                                    <li>• Complex expressions</li>
                                    <li>• When UC fails</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 4: Check for Resonance (UC method)</h4>
                        <p class="text-sm">If trial solution duplicates terms in $y_h$, multiply by $x$ (or $x^2$)</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 5: Form General Solution</h4>
                        <p class="text-sm">$y = y_h + y_p$ with two arbitrary constants</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-20 p-4 rounded-lg">
                        <h4 class="font-bold text-lg mb-2">Step 6: Apply Initial/Boundary Conditions</h4>
                        <p class="text-sm">Solve for specific values of arbitrary constants</p>
                    </div>
                </div>
            </div>

            <div class="warning-box mt-8">
                <h3 class="text-xl font-bold mb-3">
                    <i class="fas fa-exclamation-circle mr-2"></i>Common Pitfalls and Tips
                </h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-2">⚠️ Common Mistakes</h4>
                        <ul class="text-sm space-y-1">
                            <li>• Forgetting to check for resonance</li>
                            <li>• Using wrong trial solution form</li>
                            <li>• Arithmetic errors in coefficient comparison</li>
                            <li>• Missing terms in trigonometric solutions</li>
                            <li>• Incorrect Wronskian calculation</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">💡 Success Tips</h4>
                        <ul class="text-sm space-y-1">
                            <li>• Always find $y_h$ first</li>
                            <li>• Include both sin and cos for trig forcing</li>
                            <li>• Verify solution by substitution</li>
                            <li>• Check units and physical meaning</li>
                            <li>• Use computer algebra systems for verification</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Summary -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                <i class="fas fa-check-circle text-blue-600 mr-3"></i>Chapter Summary
            </h2>

            <div class="bg-white p-8 rounded-lg shadow-md">
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <i class="fas fa-key text-yellow-600 mr-2"></i>Key Concepts Mastered
                        </h3>
                        <ul class="space-y-3 text-gray-700">
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                                <span><strong>Solution Structure:</strong> $y = y_h + y_p$ decomposition</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                                <span><strong>Undetermined Coefficients:</strong> Systematic trial solution method</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                                <span><strong>Variation of Parameters:</strong> General method for any forcing function</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                                <span><strong>Resonance Handling:</strong> Modification rules for repeated terms</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                                <span><strong>Physical Applications:</strong> Forced oscillations and AC circuits</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <i class="fas fa-tools text-blue-600 mr-2"></i>Computational Skills
                        </h3>
                        <ul class="space-y-3 text-gray-700">
                            <li class="flex items-start">
                                <i class="fas fa-code text-blue-500 mr-2 mt-1"></i>
                                <span><strong>Symbolic Computation:</strong> SymPy and Ryacas for exact solutions</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-chart-line text-blue-500 mr-2 mt-1"></i>
                                <span><strong>Numerical Methods:</strong> ODE solvers and verification</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-eye text-blue-500 mr-2 mt-1"></i>
                                <span><strong>Visualization:</strong> Solution plots and frequency response</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-calculator text-blue-500 mr-2 mt-1"></i>
                                <span><strong>Parameter Studies:</strong> Sensitivity and resonance analysis</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-flask text-blue-500 mr-2 mt-1"></i>
                                <span><strong>Model Validation:</strong> Comparing theory with experiment</span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-8 p-6 bg-blue-50 rounded-lg">
                    <h3 class="text-xl font-bold text-blue-800 mb-4">
                        <i class="fas fa-forward text-blue-600 mr-2"></i>Bridge to Next Chapter
                    </h3>
                    <p class="text-blue-700">
                        With mastery of nonhomogeneous linear equations, you're ready to explore their rich applications in 
                        <strong>Chapter 13: Applications</strong>, where we'll dive deep into spring-mass-damper systems, 
                        RLC circuits, and other engineering applications. You'll see how the mathematical techniques learned 
                        here directly solve real-world problems in physics and engineering.
                    </p>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="text-center py-8 border-t border-gray-300">
            <div class="mb-4">
                <h3 class="text-2xl font-bold text-gray-800 mb-2">
                    <i class="fas fa-graduation-cap text-blue-600 mr-2"></i>
                    Congratulations!
                </h3>
                <p class="text-lg text-gray-600">
                    You've completed Chapter 12: Nonhomogeneous Equations
                </p>
            </div>
            
            <div class="flex justify-center space-x-8 text-sm text-gray-500">
                <div class="text-center">
                    <i class="fas fa-book text-2xl text-blue-500 mb-2"></i>
                    <p>Part 3: Second-Order ODEs</p>
                    <p class="font-semibold">Chapter 2 of 5</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-clock text-2xl text-green-500 mb-2"></i>
                    <p>Estimated Study Time</p>
                    <p class="font-semibold">4-6 Hours</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-star text-2xl text-yellow-500 mb-2"></i>
                    <p>Difficulty Level</p>
                    <p class="font-semibold">Intermediate</p>
                </div>
            </div>
            
            <div class="mt-6 text-xs text-gray-400">
                <p>© 2024 Comprehensive ODE Tutorial Series</p>
                <p>Interactive Mathematical Education</p>
            </div>
        </footer>
    </div>

    <script>
        // Interactive elements and enhanced functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth scrolling for internal links
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });

            // Add copy functionality to code blocks
            const codeBlocks = document.querySelectorAll('.code-block pre');
            codeBlocks.forEach(block => {
                const button = document.createElement('button');
                button.innerHTML = '<i class="fas fa-copy"></i> Copy';
                button.className = 'absolute top-2 right-2 bg-gray-600 text-white px-2 py-1 rounded text-xs opacity-70 hover:opacity-100';
                
                block.style.position = 'relative';
                block.appendChild(button);
                
                button.addEventListener('click', function() {
                    const code = block.querySelector('code').textContent;
                    navigator.clipboard.writeText(code).then(() => {
                        button.innerHTML = '<i class="fas fa-check"></i> Copied!';
                        setTimeout(() => {
                            button.innerHTML = '<i class="fas fa-copy"></i> Copy';
                        }, 2000);
                    });
                });
            });

            // Add interactive formula highlighting
            const formulas = document.querySelectorAll('.MathJax');
            formulas.forEach(formula => {
                formula.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f0f9ff';
                    this.style.borderRadius = '4px';
                    this.style.padding = '2px';
                });
                
                formula.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                    this.style.borderRadius = '';
                    this.style.padding = '';
                });
            });

            console.log('Chapter 12: Nonhomogeneous Equations - Interactive features loaded');
        });
    </script>
</body>
</html>
    <script id="html_badge_script1">
        window.__genspark_remove_badge_link = "https://www.genspark.ai/api/html_badge/" +
            "remove_badge?token=To%2FBnjzloZ3UfQdcSaYfDsJqlWru73Uwbu3zmE8gzrPESdLUcz%2BqPHGjS2gAPqy69HJlV4GBMsX3SthHlJ1Mwpc%2BdW5XrE96lxxhUQJKV2v%2BuQ7JI6o8ybcorOZUqZ%2FshIgaAW7hKlroGA1X1r6vKzIf52I7TQI3uN%2BVyKNqEUzy7%2FoAlgmIa4Sfz11R9W2Bxb53hKkdSPPI3Jqzm8SQMBMeEKzzyG597WIDmcIzZHmZ2oYJqtDmkOfqWgrmVIV0VJyrOYpTnz1UM%2B8X1rYNJKrEM76%2BJqFMlDg9MzTcn44BI%2FX1QfCPkEBRhxPKNZpSxJoI1Gpenlg%2FV3S5oiS9S%2FWY8Pq2Q3GNndg1OfiFPHlU6W7UdHMZK1oumEcRWxlRa9KRsA0M7P1aJFpqWXxXxJilDEARkxNaE3scxl3PloPYCT4rrUrINmuzGTqYYrCAmgO9kgHIK6q3YnQ%2FJ2AMxw2f6J%2FAUHGdWNUklNn6HR164CxLXsegRI5AoQfVntkvsLbCzaPZE8HParMQYplK2nLA%2B3cJKn1u5dL4KrjXHHoJOAn0ujZ7bGcGklN%2BU4Rq";
        window.__genspark_locale = "en-US";
        window.__genspark_token = "To/BnjzloZ3UfQdcSaYfDsJqlWru73Uwbu3zmE8gzrPESdLUcz+qPHGjS2gAPqy69HJlV4GBMsX3SthHlJ1Mwpc+dW5XrE96lxxhUQJKV2v+uQ7JI6o8ybcorOZUqZ/shIgaAW7hKlroGA1X1r6vKzIf52I7TQI3uN+VyKNqEUzy7/oAlgmIa4Sfz11R9W2Bxb53hKkdSPPI3Jqzm8SQMBMeEKzzyG597WIDmcIzZHmZ2oYJqtDmkOfqWgrmVIV0VJyrOYpTnz1UM+8X1rYNJKrEM76+JqFMlDg9MzTcn44BI/X1QfCPkEBRhxPKNZpSxJoI1Gpenlg/V3S5oiS9S/WY8Pq2Q3GNndg1OfiFPHlU6W7UdHMZK1oumEcRWxlRa9KRsA0M7P1aJFpqWXxXxJilDEARkxNaE3scxl3PloPYCT4rrUrINmuzGTqYYrCAmgO9kgHIK6q3YnQ/J2AMxw2f6J/AUHGdWNUklNn6HR164CxLXsegRI5AoQfVntkvsLbCzaPZE8HParMQYplK2nLA+3cJKn1u5dL4KrjXHHoJOAn0ujZ7bGcGklN+U4Rq";
    </script>
    
    <script id="html_notice_dialog_script" src="https://www.genspark.ai/notice_dialog.js"></script>
    